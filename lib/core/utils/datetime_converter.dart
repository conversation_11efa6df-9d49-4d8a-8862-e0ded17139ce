import 'package:dependencies/dependencies.dart';

/// Custom DateTime converter for helpdesk entities
/// Converts between DateTime objects and string format "2025-05-27 16:12:14"
class HelpdeskDateTimeConverter implements JsonConverter<DateTime?, String?> {
  const HelpdeskDateTimeConverter();

  @override
  DateTime? fromJson(String? json) {
    if (json == null || json.isEmpty) return null;
    
    try {
      // Parse the datetime string format "2025-05-27 16:12:14"
      return DateTime.parse(json.replaceAll(' ', 'T'));
    } catch (e) {
      // If parsing fails, return null
      return null;
    }
  }

  @override
  String? toJson(DateTime? object) {
    if (object == null) return null;
    
    // Format DateTime to "2025-05-27 16:12:14" format
    return '${object.year.toString().padLeft(4, '0')}-'
           '${object.month.toString().padLeft(2, '0')}-'
           '${object.day.toString().padLeft(2, '0')} '
           '${object.hour.toString().padLeft(2, '0')}:'
           '${object.minute.toString().padLeft(2, '0')}:'
           '${object.second.toString().padLeft(2, '0')}';
  }
}
