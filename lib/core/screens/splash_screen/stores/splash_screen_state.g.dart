// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'splash_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$SplashScreenState on _SplashScreenState, Store {
  late final _$exchangeAuthTokenAsyncAction =
      AsyncAction('_SplashScreenState.exchangeAuthToken', context: context);

  @override
  Future<void> exchangeAuthToken() {
    return _$exchangeAuthTokenAsyncAction.run(() => super.exchangeAuthToken());
  }

  @override
  String toString() {
    return '''

    ''';
  }
}
