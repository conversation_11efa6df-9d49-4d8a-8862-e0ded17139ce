import 'package:dependencies/dependencies.dart';
import 'package:flutter/cupertino.dart';
import 'package:miles_masterclass/core/app_config/app_config.dart';
import 'package:miles_masterclass/core/app_router/routes.dart';
import 'package:miles_masterclass/core/screens/coming_soon_screen.dart';
import 'package:miles_masterclass/core/stores/layout_store.dart';
import 'package:miles_masterclass/data/cache/cache_service.dart';
import 'package:miles_masterclass/data/cache/i_cache_service.dart';
import 'package:miles_masterclass/data/repositories/auth_repository.dart';
import 'package:miles_masterclass/domain/entities/auth/app_user/app_user.dart';
import 'package:miles_masterclass/domain/entities/masterclass/masterclass_details/masterclass_details.dart';
import 'package:miles_masterclass/domain/entities/webinar/webinar_details/webinar_details.dart';
import 'package:miles_masterclass/main.dart';
import 'package:miles_masterclass/presentation/auth/login_screen/login_screen.dart';
import 'package:miles_masterclass/presentation/auth/onboarding_screen/onboarding_screen.dart';
import 'package:miles_masterclass/presentation/auth/otp_screen/otp_screen.dart';
import 'package:miles_masterclass/presentation/cpe_tracker/cpe_tracker_screen/cpe_tracker_screen.dart';
import 'package:miles_masterclass/presentation/feedback/feedback_screen.dart';
import 'package:miles_masterclass/presentation/final_assessment/exam_completed_screen/exam_completed_screen.dart';
import 'package:miles_masterclass/presentation/final_assessment/exam_report_screen/exam_report_screen.dart';
import 'package:miles_masterclass/presentation/final_assessment/final_assessment_screen/final_assessment_screen.dart';
import 'package:miles_masterclass/presentation/helpdesk/all_tickets_screen.dart';
import 'package:miles_masterclass/presentation/helpdesk/helpdesk_screen.dart';
import 'package:miles_masterclass/presentation/helpdesk/ticket_details_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/dashboard_screen/dashboard_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/masterclass_detail_screen/masterclass_detail_screen.dart';
import 'package:miles_masterclass/core/screens/splash_screen/splash_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/one_time_intro_screens/cpe_tracker_intro_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/one_time_intro_screens/masterclass_intro_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/one_time_intro_screens/premier_intro_screen.dart';
import 'package:miles_masterclass/presentation/masterclass/search_screen/search_screen.dart';
import 'package:miles_masterclass/presentation/order_history/order_history_screen.dart';
import 'package:miles_masterclass/presentation/payment/add_new_address/add_new_address_screen.dart';
import 'package:miles_masterclass/presentation/payment/address_screen/address_screen.dart';
import 'package:miles_masterclass/presentation/payment/apply_coupon_screen/apply_coupon_screen.dart';
import 'package:miles_masterclass/presentation/payment/cart_review_screen/cart_preview_sceen.dart';
import 'package:miles_masterclass/presentation/payment/my_cart_screen/my_cart_screen.dart';
import 'package:miles_masterclass/presentation/payment/post_payment_screen/post_payment_screen.dart';
import 'package:miles_masterclass/presentation/payment/post_payment_screen/stripe_receipt_page.dart';
import 'package:miles_masterclass/presentation/plan/plan_screen.dart';
import 'package:miles_masterclass/presentation/privacy_policy/web_view.dart';
import 'package:miles_masterclass/presentation/profile/edit_profile_screen/edit_profile_screen.dart';
import 'package:miles_masterclass/presentation/profile/profile_screen/profile_screen.dart';
import 'package:miles_masterclass/presentation/quiz/quiz_screen/quiz_screen.dart';
import 'package:miles_masterclass/presentation/webinar-feature/webinar/webinar_screen.dart';
import 'package:miles_masterclass/presentation/webinar-feature/webinar_details/webinar_details_screen.dart';
import 'package:resources/resources.dart';

class AppRouter {
  static Page<dynamic> pageBuilder(
    BuildContext context,
    GoRouterState state,
    Widget child, {
    String transitionKind = 'default',
  }) {
    sl<LayoutStore>().setGoRouterState(state);

    switch (transitionKind) {
      case 'none':
        return CustomTransitionPage<void>(
            key: state.pageKey,
            child: child,
            name: state.name,
            transitionsBuilder:
                (context, animation, secondaryAnimation, child) {
              return child;
            });
      case 'full-screen-dialog':
        return CupertinoPage(
            child: child, fullscreenDialog: true, name: state.name);

      case 'fade':
        return CustomTransitionPage<void>(
          key: state.pageKey,
          child: child,
          name: state.name,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
        );

      case 'slide-left': // Custom Left-to-Right transition
        return CustomTransitionPage<void>(
          key: state.pageKey,
          child: child,
          name: state.name,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            const begin = Offset(1.0, 0.0); // Start from right
            const end = Offset.zero; // Move to center
            const reverseBegin = Offset.zero;
            const reverseEnd = Offset(-1.0, 0.0); // Move to left when popping

            var tween = Tween<Offset>(
              begin: begin,
              end: end,
            ).chain(CurveTween(curve: Curves.easeInOut));

            var reverseTween = Tween<Offset>(
              begin: reverseBegin,
              end: reverseEnd,
            ).chain(CurveTween(curve: Curves.easeInOut));

            return SlideTransition(
              position: animation.drive(tween),
              child: SlideTransition(
                position: secondaryAnimation
                    .drive(reverseTween), // Handle back navigation
                child: child,
              ),
            );
          },
        );

      default:
        // Default iOS transition
        return CupertinoPage(child: child, name: state.name);
    }
  }

  static Future<String?> _redirectToOnboardingRoute(
      BuildContext context, GoRouterState state) async {
    final tokenRes = await sl<AuthRepository>().getAccessToken();

    if (tokenRes.isRight()) {
      final userRes = await sl<AuthRepository>().getAppUser();

      if (userRes.isRight()) {
        final user = userRes.asRight();

        final email = user.email;
        final phone = user.mobile;

        final countryCode = user.countryCode;

        //! Check if user has not completed onboarding process
        if (user.isExistingUser == false) {
          String queryParams =
              '?phone=$phone&countryCode=$countryCode&email=$email';

          return Routes.onboardingRoute + queryParams;
        }
      }
    }
    return null;
  }

  static Future<String?> _redirectToFirstTimeIntroRoute(
      BuildContext context, GoRouterState state) async {
    final isAppInstalledNewly =
        await sl<AuthRepository>().getIsAppInstalledNewly();

    if (isAppInstalledNewly.isRight()) {
      return Routes.splashScreenRoute;
    }
    return Routes.masterclassIntro;
  }

  static final router = GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: Routes.splashScreenRoute,
    observers: [sl<LayoutStore>().routeObserver],
    routes: [
      // Splash Screen Route,
      GoRoute(
        path: Routes.splashScreenRoute,
        name: Routes.splashScreenRoute,
        redirect: _redirectToFirstTimeIntroRoute,
        pageBuilder: (context, state) => AppRouter.pageBuilder(
          context,
          state,
          const SplashScreen(),
          transitionKind: 'none',
        ),
      ),

      // Dashboard Route
      GoRoute(
          path: Routes.dashboardRoute,
          redirect: _redirectToOnboardingRoute,
          name: Routes.dashboardRoute,
          pageBuilder: (context, state) {
            return AppRouter.pageBuilder(
              context,
              state,
              const DashboardScreen(),
              transitionKind: 'fade',
            );
          }),

      // Search Route
      GoRoute(
          path: Routes.searchRoute,
          name: Routes.searchRoute,
          pageBuilder: (context, state) {
            bool? isForWebinar = state.extra as bool?;
            return AppRouter.pageBuilder(
              context,
              state,
              SearchScreen(
                isForWebinar: isForWebinar,
              ),
              transitionKind: 'none',
            );
          }),

      // Webinars Route,
      GoRoute(
        path: Routes.webinarsRoute,
        name: Routes.webinarsRoute,
        pageBuilder: (context, state) => AppRouter.pageBuilder(
          context,
          state,
          const WebinarScreen(),
          transitionKind: 'none',
        ),
      ),

      // Webinar Details Route,
      GoRoute(
          path: Routes.webinarsDetailsRoute,
          name: Routes.webinarsDetailsRoute,
          pageBuilder: (context, state) {
            bool? isOnTapOfAttendedCard = state.extra as bool?;
            return AppRouter.pageBuilder(
              context,
              state,
              WebinarDetailsScreen(
                  isOnTapOfAttendedCard: isOnTapOfAttendedCard ?? false),
              transitionKind: 'none',
            );
          }),

      // CPE Tracker Route,
      GoRoute(
        path: Routes.cpeTrackerRoute,
        name: Routes.cpeTrackerRoute,
        pageBuilder: (context, state) => AppRouter.pageBuilder(
          context,
          state,
          const CpeTrackerScreen(),
          transitionKind: 'fade',
        ),
      ),

      // Become Instructor Route,
      GoRoute(
        path: Routes.becomeInstructorRoute,
        name: Routes.becomeInstructorRoute,
        pageBuilder: (context, state) => AppRouter.pageBuilder(
          context,
          state,
          const ComingSoonScreen(),
          transitionKind: 'none',
        ),
      ),

      // Profile Route,
      GoRoute(
        path: Routes.profileRoute,
        name: Routes.profileRoute,
        pageBuilder: (context, state) => AppRouter.pageBuilder(
          context,
          state,
          const ProfileScreen(),
          transitionKind: 'fade',
        ),
      ),
      GoRoute(
        path: Routes.masterclassDetailsRoute,
        name: Routes.masterclassDetailsRoute,
        pageBuilder: (context, state) {
          final id = state.pathParameters['id'] as String;

          return AppRouter.pageBuilder(
            context,
            state,
            MasterclassDetailScreen(id: int.parse(id)),
            transitionKind: 'full-screen-dialog',
          );
        },
      ),
      GoRoute(
        path: Routes.quizRoute,
        name: Routes.quizRoute,
        pageBuilder: (context, state) {
          final chapterId = state.pathParameters['chapterId'] as String;

          return AppRouter.pageBuilder(
            context,
            state,
            QuizScreen(chapterId: int.parse(chapterId)),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.feedbackRoute,
        name: Routes.feedbackRoute,
        pageBuilder: (context, state) {
          final id = state.pathParameters['masterclassId'] as String;
          final isFeedbackForWebinar = state.extra as bool?;
          return AppRouter.pageBuilder(
            context,
            state,
            FeedbackScreen(
                masterclassId: int.parse(id),
                isFeedbackForWebinar: isFeedbackForWebinar),
            transitionKind: 'full-screen-dialog',
          );
        },
      ),

      GoRoute(
        path: Routes.finalAssessmentRoute,
        name: Routes.finalAssessmentRoute,
        routes: _finalAssessmentRoutes,
        pageBuilder: (context, state) {
          final id = state.pathParameters['id'] as String;

          return AppRouter.pageBuilder(
            context,
            state,
            FinalAssessmentScreen(masterclassId: int.parse(id)),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.loginRoute,
        name: Routes.loginRoute,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const LoginScreen(),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.privacyPolicy,
        name: Routes.privacyPolicy,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            WebViewPage(
              urlStr: AppConfig.privacyPolicyUrl,
              //'https://campus-uat.miles-api.com/web/page/privacy-policy/',
              tittle: 'Privacy Policy',
            ),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.termsAndConditions,
        name: Routes.termsAndConditions,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            WebViewPage(
              urlStr: AppConfig.termsAndConditionsUrl,
              tittle: 'Terms and Conditions',
            ),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.faq,
        name: Routes.faq,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            WebViewPage(
              urlStr: AppConfig.faqUrl,
              tittle: 'FAQs',
            ),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.otpRoute,
        name: Routes.otpRoute,
        pageBuilder: (context, state) {
          final sessionId = state.pathParameters['sessionId'] as String;

          final query = state.uri.queryParameters;

          //? Get all the query parameters
          String? email = query['email'];
          String? phone = query['phone'];
          String? countryCode = query['countryCode'];
          String? newUser = query['newUser'];

          return AppRouter.pageBuilder(
            context,
            state,
            OtpScreen(
              sessionId: int.parse(sessionId),
              email: email,
              phone: phone,
              countryCode: countryCode,
              newUser: bool.parse(newUser ?? 'true'),
            ),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.onboardingRoute,
        name: Routes.onboardingRoute,
        pageBuilder: (context, state) {
          final query = state.uri.queryParameters;
          //? Get all the query parameters
          String? email = query['email'];
          String? phone = query['phone'];
          String? countryCode = query['countryCode'];

          return AppRouter.pageBuilder(
            context,
            state,
            OnboardingScreen(
              email: email,
              phone: phone,
              countryCode: countryCode,
            ),
            transitionKind: 'none',
          );
        },
      ),
      GoRoute(
        path: Routes.editProfile,
        name: Routes.editProfile,
        pageBuilder: (context, state) {
          final appUser = state.extra as AppUser;
          return AppRouter.pageBuilder(
            context,
            state,
            EditProfileScreen(
              appUser: appUser,
            ),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.purchasePreview,
        name: Routes.purchasePreview,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            CartPreviewScreen(),
            //const MyCartScreen(),
            transitionKind: 'none',
          );
        },
      ),
      GoRoute(
        path: Routes.myCart,
        name: Routes.myCart,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const MyCartScreen(),
            //const MyCartScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),
      GoRoute(
        path: Routes.addNewAddress,
        name: Routes.addNewAddress,
        pageBuilder: (context, state) {
          final addNewAddressScreenData =
              state.extra as AddNewAddressScreenData;
          return AppRouter.pageBuilder(
            context,
            state,
            AddNewAddressScreen(
              addNewAddressScreenData: addNewAddressScreenData,
            ),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.applyCoupons,
        name: Routes.applyCoupons,
        pageBuilder: (context, state) {
          final applyCouponScreenData = state.extra as ApplyCouponScreenData;
          return AppRouter.pageBuilder(
            context,
            state,
            ApplyCouponScreen(
              applyCouponScreenData: applyCouponScreenData,
            ),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.address,
        name: Routes.address,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            AddressScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.preview,
        name: Routes.preview,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            CartPreviewScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.postPayment,
        name: Routes.postPayment,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const PostPaymentScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.downloadStripeReceipt,
        name: Routes.downloadStripeReceipt,
        pageBuilder: (context, state) {
          final receiptUrl = state.extra as String;
          return AppRouter.pageBuilder(
            context,
            state,
            StripeReceiptPage(
              receiptUrl: receiptUrl,
            ),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.orderHistory,
        name: Routes.orderHistory,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const OrderHistoryScreen(),
            transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.plan,
        name: Routes.plan,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const PlanScreen(),
            transitionKind: 'full-screen-dialog',
          );
        },
      ),

      GoRoute(
        path: Routes.masterclassIntro,
        name: Routes.masterclassIntro,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const MasterclassIntoScreen(),
            //transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.premierIntro,
        name: Routes.premierIntro,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const PremiereIntroScreen(),
            //transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.cpeTrackerIntro,
        name: Routes.cpeTrackerIntro,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const CpeTrackerIntroScreen(),
            //transitionKind: 'none',
          );
        },
      ),

      GoRoute(
        path: Routes.helpdesk,
        name: Routes.helpdesk,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const HelpdeskScreen(),
            transitionKind: 'full-screen-dialog',
          );
        },
      ),

      GoRoute(
        path: Routes.helpdeskTicketDetails,
        name: Routes.helpdeskTicketDetails,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const TicketDetailsScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),

      GoRoute(
        path: Routes.helpdeskAllTickets,
        name: Routes.helpdeskAllTickets,
        pageBuilder: (context, state) {
          return AppRouter.pageBuilder(
            context,
            state,
            const AllTicketsScreen(),
            transitionKind: 'slide-left',
          );
        },
      ),

      // GoRoute(
      //   path: Routes.viewAllWebinar,
      //   name: Routes.viewAllWebinar,
      //   pageBuilder: (context, state) {
      //     return AppRouter.pageBuilder(
      //       context,
      //       state,
      //       const ViewAllWebinarScreen(header: header, webinarList: webinarList),
      //       transitionKind: 'none',
      //     );
      //   },
      // ),
    ],
  );

  static List<GoRoute> _finalAssessmentRoutes = [
    GoRoute(
      path: Routes.examCompletedRoute.replaceFirstFinalAssessment(),
      name: Routes.examCompletedRoute,
      pageBuilder: (context, state) {
        final id = state.pathParameters['sessionId'] as String;

        return AppRouter.pageBuilder(
          context,
          state,
          ExamCompletedScreen(sessionId: int.parse(id)),
          transitionKind: 'none',
        );
      },
    ),
    GoRoute(
      path: Routes.examResultsRoute.replaceFirstFinalAssessment(),
      name: Routes.examResultsRoute,
      pageBuilder: (context, state) {
        final id = state.pathParameters['sessionId'] as String;

        final bool feedbackSubmitted =
            state.uri.queryParameters['feedbackSubmitted'] == 'true';

        return AppRouter.pageBuilder(
          context,
          state,
          ExamReportScreen(
            sessionId: int.parse(id),
            feedbackSubmitted: feedbackSubmitted,
          ),
          transitionKind: 'none',
        );
      },
    ),
  ];
}
