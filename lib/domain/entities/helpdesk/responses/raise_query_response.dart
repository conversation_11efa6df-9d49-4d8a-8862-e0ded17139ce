import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';

part 'raise_query_response.g.dart';

/// Response model for raise query API call
@JsonSerializable()
class RaiseQueryResponse {
  final int? code;
  final String? message;
  final bool? status;
  final List<int>? data;
  final List<dynamic>? metadata;

  const RaiseQueryResponse({
    this.code,
    this.message,
    this.status,
    this.data,
    this.metadata,
  });

  factory RaiseQueryResponse.fromJson(Map<String, dynamic> json) =>
      _$RaiseQueryResponseFromJson(json);

  Map<String, dynamic> toJson() => _$RaiseQueryResponseToJson(this);

  @override
  String toString() {
    return 'RaiseQueryResponse(code: $code, message: $message, status: $status, data: $data, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RaiseQueryResponse &&
        other.code == code &&
        other.message == message &&
        other.status == status &&
        listEquals(other.data, data) &&
        listEquals(other.metadata, metadata);
  }

  @override
  int get hashCode {
    return code.hashCode ^
        message.hashCode ^
        status.hashCode ^
        data.hashCode ^
        metadata.hashCode;
  }

  /// Copy with method for immutable updates
  RaiseQueryResponse copyWith({
    int? code,
    String? message,
    bool? status,
    List<int>? data,
    List<dynamic>? metadata,
  }) {
    return RaiseQueryResponse(
      code: code ?? this.code,
      message: message ?? this.message,
      status: status ?? this.status,
      data: data ?? this.data,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get the first ticket ID from the data array
  int? get ticketId => data?.isNotEmpty == true ? data!.first : null;
}
