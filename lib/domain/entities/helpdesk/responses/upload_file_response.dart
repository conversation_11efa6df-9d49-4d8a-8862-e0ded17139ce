import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';

part 'upload_file_response.g.dart';

/// Response model for file upload API call
@JsonSerializable()
class UploadFileResponse {
  final int? code;
  final String? message;
  final bool? status;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'secure_url')
  final String? secureUrl;
  
  @J<PERSON><PERSON><PERSON>(name: 'original_filename')
  final String? originalFilename;
  
  final String? format;
  final int? bytes;
  final List<dynamic>? metadata;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'public_id')
  final String? publicId;

  const UploadFileResponse({
    this.code,
    this.message,
    this.status,
    this.secureUrl,
    this.originalFilename,
    this.format,
    this.bytes,
    this.metadata,
    this.publicId,
  });

  factory UploadFileResponse.fromJson(Map<String, dynamic> json) =>
      _$UploadFileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UploadFileResponseToJson(this);

  @override
  String toString() {
    return 'UploadFileResponse(code: $code, message: $message, status: $status, secureUrl: $secureUrl, originalFilename: $originalFilename, format: $format, bytes: $bytes, publicId: $publicId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadFileResponse &&
        other.code == code &&
        other.message == message &&
        other.status == status &&
        other.secureUrl == secureUrl &&
        other.originalFilename == originalFilename &&
        other.format == format &&
        other.bytes == bytes &&
        other.publicId == publicId &&
        listEquals(other.metadata, metadata);
  }

  @override
  int get hashCode {
    return code.hashCode ^
        message.hashCode ^
        status.hashCode ^
        secureUrl.hashCode ^
        originalFilename.hashCode ^
        format.hashCode ^
        bytes.hashCode ^
        publicId.hashCode ^
        metadata.hashCode;
  }

  /// Copy with method for immutable updates
  UploadFileResponse copyWith({
    int? code,
    String? message,
    bool? status,
    String? secureUrl,
    String? originalFilename,
    String? format,
    int? bytes,
    List<dynamic>? metadata,
    String? publicId,
  }) {
    return UploadFileResponse(
      code: code ?? this.code,
      message: message ?? this.message,
      status: status ?? this.status,
      secureUrl: secureUrl ?? this.secureUrl,
      originalFilename: originalFilename ?? this.originalFilename,
      format: format ?? this.format,
      bytes: bytes ?? this.bytes,
      metadata: metadata ?? this.metadata,
      publicId: publicId ?? this.publicId,
    );
  }
}
