// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_file_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadFileResponse _$UploadFileResponseFromJson(Map<String, dynamic> json) =>
    UploadFileResponse(
      code: (json['code'] as num?)?.toInt(),
      message: json['message'] as String?,
      status: json['status'] as bool?,
      secureUrl: json['secure_url'] as String?,
      originalFilename: json['original_filename'] as String?,
      format: json['format'] as String?,
      bytes: (json['bytes'] as num?)?.toInt(),
      metadata: json['metadata'] as List<dynamic>?,
      publicId: json['public_id'] as String?,
    );

Map<String, dynamic> _$UploadFileResponseToJson(UploadFileResponse instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'status': instance.status,
      'secure_url': instance.secureUrl,
      'original_filename': instance.originalFilename,
      'format': instance.format,
      'bytes': instance.bytes,
      'metadata': instance.metadata,
      'public_id': instance.publicId,
    };
