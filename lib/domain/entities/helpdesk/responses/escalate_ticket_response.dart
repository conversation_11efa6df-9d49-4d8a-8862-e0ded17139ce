import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';

part 'escalate_ticket_response.g.dart';

/// Response model for escalate ticket API call
@JsonSerializable()
class EscalateTicketResponse {
  final int? code;
  final String? message;
  final bool? status;
  final int? data;
  final List<dynamic>? metadata;

  const EscalateTicketResponse({
    this.code,
    this.message,
    this.status,
    this.data,
    this.metadata,
  });

  factory EscalateTicketResponse.fromJson(Map<String, dynamic> json) =>
      _$EscalateTicketResponseFromJson(json);

  Map<String, dynamic> toJson() => _$EscalateTicketResponseToJson(this);

  @override
  String toString() {
    return 'EscalateTicketResponse(code: $code, message: $message, status: $status, data: $data, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EscalateTicketResponse &&
        other.code == code &&
        other.message == message &&
        other.status == status &&
        other.data == data &&
        listEquals(other.metadata, metadata);
  }

  @override
  int get hashCode {
    return code.hashCode ^
        message.hashCode ^
        status.hashCode ^
        data.hashCode ^
        metadata.hashCode;
  }

  /// Copy with method for immutable updates
  EscalateTicketResponse copyWith({
    int? code,
    String? message,
    bool? status,
    int? data,
    List<dynamic>? metadata,
  }) {
    return EscalateTicketResponse(
      code: code ?? this.code,
      message: message ?? this.message,
      status: status ?? this.status,
      data: data ?? this.data,
      metadata: metadata ?? this.metadata,
    );
  }
}
