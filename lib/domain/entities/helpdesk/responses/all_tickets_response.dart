import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_ticket/helpdesk_ticket.dart';

part 'all_tickets_response.g.dart';

/// Response model for all tickets API call
@JsonSerializable()
class AllTicketsResponse {
  final int? code;
  final String? message;
  final bool? status;
  final List<HelpdeskTicket>? data;
  final List<dynamic>? metadata;

  const AllTicketsResponse({
    this.code,
    this.message,
    this.status,
    this.data,
    this.metadata,
  });

  factory AllTicketsResponse.fromJson(Map<String, dynamic> json) =>
      _$AllTicketsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AllTicketsResponseToJson(this);

  @override
  String toString() {
    return 'AllTicketsResponse(code: $code, message: $message, status: $status, data: $data, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AllTicketsResponse &&
        other.code == code &&
        other.message == message &&
        other.status == status &&
        listEquals(other.data, data) &&
        listEquals(other.metadata, metadata);
  }

  @override
  int get hashCode {
    return code.hashCode ^
        message.hashCode ^
        status.hashCode ^
        data.hashCode ^
        metadata.hashCode;
  }

  /// Copy with method for immutable updates
  AllTicketsResponse copyWith({
    int? code,
    String? message,
    bool? status,
    List<HelpdeskTicket>? data,
    List<dynamic>? metadata,
  }) {
    return AllTicketsResponse(
      code: code ?? this.code,
      message: message ?? this.message,
      status: status ?? this.status,
      data: data ?? this.data,
      metadata: metadata ?? this.metadata,
    );
  }
}
