import 'package:dependencies/dependencies.dart';

part 'helpdesk_image.g.dart';

/// Model for helpdesk image upload information
@JsonSerializable()
class HelpdeskImage {
  final String name;
  final String path;
  final String format;
  final int size;

  const HelpdeskImage({
    required this.name,
    required this.path,
    required this.format,
    required this.size,
  });

  factory HelpdeskImage.fromJson(Map<String, dynamic> json) =>
      _$HelpdeskImageFromJson(json);

  Map<String, dynamic> toJson() => _$HelpdeskImageToJson(this);

  @override
  String toString() {
    return 'HelpdeskImage(name: $name, path: $path, format: $format, size: $size)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HelpdeskImage &&
        other.name == name &&
        other.path == path &&
        other.format == format &&
        other.size == size;
  }

  @override
  int get hashCode {
    return name.hashCode ^
        path.hashCode ^
        format.hashCode ^
        size.hashCode;
  }

  /// Copy with method for immutable updates
  HelpdeskImage copyWith({
    String? name,
    String? path,
    String? format,
    int? size,
  }) {
    return HelpdeskImage(
      name: name ?? this.name,
      path: path ?? this.path,
      format: format ?? this.format,
      size: size ?? this.size,
    );
  }
}
