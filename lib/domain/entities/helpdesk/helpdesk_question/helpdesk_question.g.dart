// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'helpdesk_question.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HelpdeskQuestion _$HelpdeskQuestionFromJson(Map<String, dynamic> json) =>
    HelpdeskQuestion(
      id: (json['id'] as num?)?.toInt(),
      subQuestion: json['sub_question'] as String?,
      rating: (json['rating'] as num?)?.toInt(),
      categoryId: (json['category_id'] as num?)?.toInt(),
      questionId: (json['question_id'] as num?)?.toInt(),
      status: (json['status'] as num?)?.toInt(),
      feedbackType: (json['feedback_type'] as num?)?.toInt(),
      createdDate: const HelpdeskDateTimeConverter()
          .fromJson(json['created_date'] as String?),
      updatedDate: const HelpdeskDateTimeConverter()
          .fromJson(json['updated_date'] as String?),
      createdBy: (json['created_by'] as num?)?.toInt(),
      updatedBy: (json['updated_by'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HelpdeskQuestionToJson(HelpdeskQuestion instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sub_question': instance.subQuestion,
      'rating': instance.rating,
      'category_id': instance.categoryId,
      'question_id': instance.questionId,
      'status': instance.status,
      'feedback_type': instance.feedbackType,
      'created_date':
          const HelpdeskDateTimeConverter().toJson(instance.createdDate),
      'updated_date':
          const HelpdeskDateTimeConverter().toJson(instance.updatedDate),
      'created_by': instance.createdBy,
      'updated_by': instance.updatedBy,
    };
