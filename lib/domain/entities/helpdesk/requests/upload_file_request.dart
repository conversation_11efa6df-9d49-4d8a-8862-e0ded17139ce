import 'dart:io';
import 'package:dependencies/dependencies.dart';

part 'upload_file_request.g.dart';

/// Request model for uploading a file to the server
@JsonSerializable()
class UploadFileRequest {
  @JsonKey(name: 'upload_preset')
  final String uploadPreset;

  final String folder;

  // Note: The file field will be handled separately as multipart data
  // This is just for JSON serialization of other fields
  @JsonKey(includeFromJson: false, includeToJson: false)
  final File? file;

  const UploadFileRequest({
    required this.uploadPreset,
    required this.folder,
    this.file,
  });

  factory UploadFileRequest.fromJson(Map<String, dynamic> json) =>
      _$UploadFileRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UploadFileRequestToJson(this);

  @override
  String toString() {
    return 'UploadFileRequest(uploadPreset: $uploadPreset, folder: $folder, fileName: ${file?.path.split('/').last ?? 'null'})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UploadFileRequest &&
        other.uploadPreset == uploadPreset &&
        other.folder == folder &&
        other.file?.path == file?.path;
  }

  @override
  int get hashCode {
    return uploadPreset.hashCode ^ folder.hashCode ^ (file?.path.hashCode ?? 0);
  }

  /// Copy with method for immutable updates
  UploadFileRequest copyWith({
    String? uploadPreset,
    String? folder,
    File? file,
  }) {
    return UploadFileRequest(
      uploadPreset: uploadPreset ?? this.uploadPreset,
      folder: folder ?? this.folder,
      file: file ?? this.file,
    );
  }

  /// Create default request for forum uploads
  factory UploadFileRequest.forForum(File file) {
    return UploadFileRequest(
      uploadPreset: 'itx0z6fi',
      folder: 'forum',
      file: file,
    );
  }

  /// Create request without file for JSON serialization
  factory UploadFileRequest.forJson({
    required String uploadPreset,
    required String folder,
  }) {
    return UploadFileRequest(
      uploadPreset: uploadPreset,
      folder: folder,
      file: null,
    );
  }
}
