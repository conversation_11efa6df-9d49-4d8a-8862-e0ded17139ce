import 'package:dependencies/dependencies.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/models/helpdesk_image.dart';

part 'raise_query_request.g.dart';

/// Helpdesk item model for raise query request
@JsonSerializable(explicitToJson: true)
class HelpdeskItem {
  @Json<PERSON>ey(name: 'category_id')
  final int? categoryId;
  
  @Json<PERSON>ey(name: 'question_id')
  final int? questionId;
  
  @JsonKey(name: 'sub_question_id')
  final List<int>? subQuestionId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'subject_id')
  final int? subjectId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'trainer_id')
  final int? trainerId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'service_team_id')
  final int? serviceTeamId;
  
  final String? helpdesk;
  
  @Json<PERSON>ey(name: 'issue_question_id')
  final String? issueQuestionId;
  
  @<PERSON><PERSON><PERSON>ey(name: 'img_url')
  final List<HelpdeskImage>? imgUrl;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'gp_team_id')
  final int? gpTeamId;

  const HelpdeskItem({
    this.categoryId,
    this.questionId,
    this.subQuestionId,
    this.subjectId,
    this.trainerId,
    this.serviceTeamId,
    this.helpdesk,
    this.issueQuestionId,
    this.imgUrl,
    this.gpTeamId,
  });

  factory HelpdeskItem.fromJson(Map<String, dynamic> json) =>
      _$HelpdeskItemFromJson(json);

  Map<String, dynamic> toJson() => _$HelpdeskItemToJson(this);

  @override
  String toString() {
    return 'HelpdeskItem(categoryId: $categoryId, questionId: $questionId, subQuestionId: $subQuestionId, subjectId: $subjectId, trainerId: $trainerId, serviceTeamId: $serviceTeamId, helpdesk: $helpdesk, issueQuestionId: $issueQuestionId, imgUrl: $imgUrl, gpTeamId: $gpTeamId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HelpdeskItem &&
        other.categoryId == categoryId &&
        other.questionId == questionId &&
        other.subQuestionId == subQuestionId &&
        other.subjectId == subjectId &&
        other.trainerId == trainerId &&
        other.serviceTeamId == serviceTeamId &&
        other.helpdesk == helpdesk &&
        other.issueQuestionId == issueQuestionId &&
        other.imgUrl == imgUrl &&
        other.gpTeamId == gpTeamId;
  }

  @override
  int get hashCode {
    return categoryId.hashCode ^
        questionId.hashCode ^
        subQuestionId.hashCode ^
        subjectId.hashCode ^
        trainerId.hashCode ^
        serviceTeamId.hashCode ^
        helpdesk.hashCode ^
        issueQuestionId.hashCode ^
        imgUrl.hashCode ^
        gpTeamId.hashCode;
  }

  HelpdeskItem copyWith({
    int? categoryId,
    int? questionId,
    List<int>? subQuestionId,
    int? subjectId,
    int? trainerId,
    int? serviceTeamId,
    String? helpdesk,
    String? issueQuestionId,
    List<HelpdeskImage>? imgUrl,
    int? gpTeamId,
  }) {
    return HelpdeskItem(
      categoryId: categoryId ?? this.categoryId,
      questionId: questionId ?? this.questionId,
      subQuestionId: subQuestionId ?? this.subQuestionId,
      subjectId: subjectId ?? this.subjectId,
      trainerId: trainerId ?? this.trainerId,
      serviceTeamId: serviceTeamId ?? this.serviceTeamId,
      helpdesk: helpdesk ?? this.helpdesk,
      issueQuestionId: issueQuestionId ?? this.issueQuestionId,
      imgUrl: imgUrl ?? this.imgUrl,
      gpTeamId: gpTeamId ?? this.gpTeamId,
    );
  }
}

/// Request model for raise query API call
@JsonSerializable(explicitToJson: true)
class RaiseQueryRequest {
  final List<HelpdeskItem>? helpdesk;
  
  @JsonKey(name: 'lms_course')
  final String? lmsCourse;
  
  @JsonKey(name: 'unique_id')
  final String? uniqueId;

  @JsonKey(name: 'can_id')
  final String? canId;
  
  final String? name;
  final String? email;
  final String? phonenumber;

  @JsonKey(name: 'city_id')
  final int? cityId;

  const RaiseQueryRequest({
    this.helpdesk,
    this.lmsCourse,
    this.uniqueId,
    this.name,
    this.email,
    this.phonenumber,
    this.cityId,
    this.canId = 'B-10113' // Mocked
  });

  factory RaiseQueryRequest.fromJson(Map<String, dynamic> json) =>
      _$RaiseQueryRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RaiseQueryRequestToJson(this);

  @override
  String toString() {
    return 'RaiseQueryRequest(helpdesk: $helpdesk, lmsCourse: $lmsCourse, uniqueId: $uniqueId, name: $name, email: $email, phonenumber: $phonenumber, cityId: $cityId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RaiseQueryRequest &&
        other.helpdesk == helpdesk &&
        other.lmsCourse == lmsCourse &&
        other.uniqueId == uniqueId &&
        other.name == name &&
        other.email == email &&
        other.phonenumber == phonenumber &&
        other.cityId == cityId;
  }

  @override
  int get hashCode {
    return helpdesk.hashCode ^
        lmsCourse.hashCode ^
        uniqueId.hashCode ^
        name.hashCode ^
        email.hashCode ^
        phonenumber.hashCode ^
        cityId.hashCode;
  }

  /// Copy with method for immutable updates
  RaiseQueryRequest copyWith({
    List<HelpdeskItem>? helpdesk,
    String? lmsCourse,
    String? uniqueId,
    String? name,
    String? email,
    String? phonenumber,
    int? cityId,
  }) {
    return RaiseQueryRequest(
      helpdesk: helpdesk ?? this.helpdesk,
      lmsCourse: lmsCourse ?? this.lmsCourse,
      uniqueId: uniqueId ?? this.uniqueId,
      name: name ?? this.name,
      email: email ?? this.email,
      phonenumber: phonenumber ?? this.phonenumber,
      cityId: cityId ?? this.cityId,
    );
  }
}
