import 'package:dependencies/dependencies.dart';

part 'all_tickets_request.g.dart';

/// Request model for getting all tickets
@JsonSerializable()
class AllTicketsRequest {
  @JsonKey(name: 'ticket_type')
  final int ticketType;
  
  @Json<PERSON>ey(name: 'can_id')
  final String canId;
  
  final String course;

  const AllTicketsRequest({
    required this.ticketType,
    required this.canId,
    required this.course,
  });

  factory AllTicketsRequest.fromJson(Map<String, dynamic> json) =>
      _$AllTicketsRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AllTicketsRequestToJson(this);

  @override
  String toString() {
    return 'AllTicketsRequest(ticketType: $ticketType, canId: $canId, course: $course)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AllTicketsRequest &&
        other.ticketType == ticketType &&
        other.canId == canId &&
        other.course == course;
  }

  @override
  int get hashCode {
    return ticketType.hashCode ^ canId.hashCode ^ course.hashCode;
  }

  /// Copy with method for immutable updates
  AllTicketsRequest copyWith({
    int? ticketType,
    String? canId,
    String? course,
  }) {
    return AllTicketsRequest(
      ticketType: ticketType ?? this.ticketType,
      canId: canId ?? this.canId,
      course: course ?? this.course,
    );
  }
}
