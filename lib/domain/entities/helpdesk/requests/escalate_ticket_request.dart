import 'package:dependencies/dependencies.dart';

part 'escalate_ticket_request.g.dart';

/// Request model for escalating a helpdesk ticket
@JsonSerializable()
class EscalateTicketRequest {
  @JsonKey(name: 'can_id')
  final String canId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'helpdesk_id')
  final int helpdeskId;

  const EscalateTicketRequest({
    required this.canId,
    required this.helpdeskId,
  });

  factory EscalateTicketRequest.fromJson(Map<String, dynamic> json) =>
      _$EscalateTicketRequestFromJson(json);

  Map<String, dynamic> toJson() => _$EscalateTicketRequestToJson(this);

  @override
  String toString() {
    return 'EscalateTicketRequest(canId: $canId, helpdeskId: $helpdeskId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EscalateTicketRequest &&
        other.canId == canId &&
        other.helpdeskId == helpdeskId;
  }

  @override
  int get hashCode {
    return canId.hashCode ^ helpdeskId.hashCode;
  }

  /// Copy with method for immutable updates
  EscalateTicketRequest copyWith({
    String? canId,
    int? helpdeskId,
  }) {
    return EscalateTicketRequest(
      canId: canId ?? this.canId,
      helpdeskId: helpdeskId ?? this.helpdeskId,
    );
  }
}
