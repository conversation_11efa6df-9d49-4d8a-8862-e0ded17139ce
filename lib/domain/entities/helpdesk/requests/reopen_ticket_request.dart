import 'package:dependencies/dependencies.dart';

part 'reopen_ticket_request.g.dart';

/// Request model for reopening a helpdesk ticket
@JsonSerializable()
class ReopenTicketRequest {
  @JsonKey(name: 'can_id')
  final String canId;
  
  @Json<PERSON>ey(name: 'helpdesk_id')
  final int helpdeskId;
  
  final String reason;

  const ReopenTicketRequest({
    required this.canId,
    required this.helpdeskId,
    required this.reason,
  });

  factory ReopenTicketRequest.fromJson(Map<String, dynamic> json) =>
      _$ReopenTicketRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ReopenTicketRequestToJson(this);

  @override
  String toString() {
    return 'ReopenTicketRequest(canId: $canId, helpdeskId: $helpdeskId, reason: $reason)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReopenTicketRequest &&
        other.canId == canId &&
        other.helpdeskId == helpdeskId &&
        other.reason == reason;
  }

  @override
  int get hashCode {
    return canId.hashCode ^ helpdeskId.hashCode ^ reason.hashCode;
  }

  /// Copy with method for immutable updates
  ReopenTicketRequest copyWith({
    String? canId,
    int? helpdeskId,
    String? reason,
  }) {
    return ReopenTicketRequest(
      canId: canId ?? this.canId,
      helpdeskId: helpdeskId ?? this.helpdeskId,
      reason: reason ?? this.reason,
    );
  }
}
