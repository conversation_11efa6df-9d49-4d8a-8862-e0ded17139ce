// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'raise_query_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HelpdeskItem _$HelpdeskItemFromJson(Map<String, dynamic> json) => HelpdeskItem(
      categoryId: (json['category_id'] as num?)?.toInt(),
      questionId: (json['question_id'] as num?)?.toInt(),
      subQuestionId: (json['sub_question_id'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      subjectId: (json['subject_id'] as num?)?.toInt(),
      trainerId: (json['trainer_id'] as num?)?.toInt(),
      serviceTeamId: (json['service_team_id'] as num?)?.toInt(),
      helpdesk: json['helpdesk'] as String?,
      issueQuestionId: json['issue_question_id'] as String?,
      imgUrl:
          (json['img_url'] as List<dynamic>?)?.map((e) => e as String).toList(),
      gpTeamId: (json['gp_team_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HelpdeskItemToJson(HelpdeskItem instance) =>
    <String, dynamic>{
      'category_id': instance.categoryId,
      'question_id': instance.questionId,
      'sub_question_id': instance.subQuestionId,
      'subject_id': instance.subjectId,
      'trainer_id': instance.trainerId,
      'service_team_id': instance.serviceTeamId,
      'helpdesk': instance.helpdesk,
      'issue_question_id': instance.issueQuestionId,
      'img_url': instance.imgUrl,
      'gp_team_id': instance.gpTeamId,
    };

RaiseQueryRequest _$RaiseQueryRequestFromJson(Map<String, dynamic> json) =>
    RaiseQueryRequest(
      helpdesk: (json['helpdesk'] as List<dynamic>?)
          ?.map((e) => HelpdeskItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      lmsCourse: json['lms_course'] as String?,
      uniqueId: json['unique_id'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phonenumber: json['phonenumber'] as String?,
      cityId: (json['city_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$RaiseQueryRequestToJson(RaiseQueryRequest instance) =>
    <String, dynamic>{
      'helpdesk': instance.helpdesk?.map((element) => element.toJson()).toList(),
      'lms_course': instance.lmsCourse,
      'unique_id': instance.uniqueId,
      'name': instance.name,
      'email': instance.email,
      'phonenumber': instance.phonenumber,
      'city_id': instance.cityId,
    };
