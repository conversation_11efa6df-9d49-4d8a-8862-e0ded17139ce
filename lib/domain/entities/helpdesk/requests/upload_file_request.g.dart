// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'upload_file_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UploadFileRequest _$UploadFileRequestFromJson(Map<String, dynamic> json) =>
    UploadFileRequest(
      uploadPreset: json['upload_preset'] as String,
      folder: json['folder'] as String,
    );

Map<String, dynamic> _$UploadFileRequestToJson(UploadFileRequest instance) =>
    <String, dynamic>{
      'upload_preset': instance.uploadPreset,
      'folder': instance.folder,
    };
