import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';
import 'package:miles_masterclass/core/utils/datetime_converter.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/ticket_status_component.dart';

part 'helpdesk_ticket.g.dart';

/// Helpdesk Ticket entity
@JsonSerializable()
class HelpdeskTicket {
  @JsonKey(name: 'helpdesk_id')
  final int? helpdeskId;
  
  final String? course;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'category_id')
  final int? categoryId;
  
  final String? category;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sub_category')
  final String? subCategory;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'issue_raised')
  final String? issueRaised;
  
  @JsonKey(name: 'issue_raised_date')
  @HelpdeskDateTimeConverter()
  final DateTime? issueRaisedDate;

  @<PERSON>son<PERSON><PERSON>(name: 'updated_date')
  @HelpdeskDateTimeConverter()
  final DateTime? updatedDate;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sub_question_id')
  final String? subQuestionId;
  
  @Json<PERSON><PERSON>(name: 'ticketStatus')
  final int? ticketStatus;
  
  @Json<PERSON>ey(name: 'student_reason_date')
  final String? studentReasonDate;
  
  @Json<PERSON>ey(name: 'is_escalate')
  final int? isEscalate;
  
  @JsonKey(name: 'ticket_id')
  final String? ticketId;
  
  @JsonKey(name: 'ticket_status')
  final String? ticketStatusText;
  
  @JsonKey(name: 'is_feedback_given')
  final int? isFeedbackGiven;
  
  @JsonKey(name: 'feedback_rating')
  final int? feedbackRating;
  
  final List<HelpdeskComment>? comment;
  
  @JsonKey(name: 'show_status')
  final int? showStatus;
  
  @JsonKey(name: 'issue_checked')
  final List<String>? issueChecked;
  
  @JsonKey(name: 'hide_reopen_ticket')
  final int? hideReopenTicket;
  
  @JsonKey(name: 'show_escalate')
  final int? showEscalate;

  const HelpdeskTicket({
    this.helpdeskId,
    this.course,
    this.categoryId,
    this.category,
    this.subCategory,
    this.issueRaised,
    this.issueRaisedDate,
    this.updatedDate,
    this.subQuestionId,
    this.ticketStatus,
    this.studentReasonDate,
    this.isEscalate,
    this.ticketId,
    this.ticketStatusText,
    this.isFeedbackGiven,
    this.feedbackRating,
    this.comment,
    this.showStatus,
    this.issueChecked,
    this.hideReopenTicket,
    this.showEscalate,
  });

  factory HelpdeskTicket.fromJson(Map<String, dynamic> json) =>
      _$HelpdeskTicketFromJson(json);

  Map<String, dynamic> toJson() => _$HelpdeskTicketToJson(this);

  @override
  String toString() {
    return 'HelpdeskTicket(helpdeskId: $helpdeskId, course: $course, categoryId: $categoryId, category: $category, subCategory: $subCategory, issueRaised: $issueRaised, issueRaisedDate: $issueRaisedDate, updatedDate: $updatedDate, subQuestionId: $subQuestionId, ticketStatus: $ticketStatus, studentReasonDate: $studentReasonDate, isEscalate: $isEscalate, ticketId: $ticketId, ticketStatusText: $ticketStatusText, isFeedbackGiven: $isFeedbackGiven, feedbackRating: $feedbackRating, comment: $comment, showStatus: $showStatus, issueChecked: $issueChecked, hideReopenTicket: $hideReopenTicket, showEscalate: $showEscalate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HelpdeskTicket &&
        other.helpdeskId == helpdeskId &&
        other.course == course &&
        other.categoryId == categoryId &&
        other.category == category &&
        other.subCategory == subCategory &&
        other.issueRaised == issueRaised &&
        other.issueRaisedDate == issueRaisedDate &&
        other.updatedDate == updatedDate &&
        other.subQuestionId == subQuestionId &&
        other.ticketStatus == ticketStatus &&
        other.studentReasonDate == studentReasonDate &&
        other.isEscalate == isEscalate &&
        other.ticketId == ticketId &&
        other.ticketStatusText == ticketStatusText &&
        other.isFeedbackGiven == isFeedbackGiven &&
        other.feedbackRating == feedbackRating &&
        listEquals(other.comment, comment) &&
        other.showStatus == showStatus &&
        listEquals(other.issueChecked, issueChecked) &&
        other.hideReopenTicket == hideReopenTicket &&
        other.showEscalate == showEscalate;
  }

  @override
  int get hashCode {
    return helpdeskId.hashCode ^
        course.hashCode ^
        categoryId.hashCode ^
        category.hashCode ^
        subCategory.hashCode ^
        issueRaised.hashCode ^
        issueRaisedDate.hashCode ^
        updatedDate.hashCode ^
        subQuestionId.hashCode ^
        ticketStatus.hashCode ^
        studentReasonDate.hashCode ^
        isEscalate.hashCode ^
        ticketId.hashCode ^
        ticketStatusText.hashCode ^
        isFeedbackGiven.hashCode ^
        feedbackRating.hashCode ^
        comment.hashCode ^
        showStatus.hashCode ^
        issueChecked.hashCode ^
        hideReopenTicket.hashCode ^
        showEscalate.hashCode;
  }

  /// Copy with method for immutable updates
  HelpdeskTicket copyWith({
    int? helpdeskId,
    String? course,
    int? categoryId,
    String? category,
    String? subCategory,
    String? issueRaised,
    DateTime? issueRaisedDate,
    DateTime? updatedDate,
    String? subQuestionId,
    int? ticketStatus,
    String? studentReasonDate,
    int? isEscalate,
    String? ticketId,
    String? ticketStatusText,
    int? isFeedbackGiven,
    int? feedbackRating,
    List<HelpdeskComment>? comment,
    int? showStatus,
    List<String>? issueChecked,
    int? hideReopenTicket,
    int? showEscalate,
  }) {
    return HelpdeskTicket(
      helpdeskId: helpdeskId ?? this.helpdeskId,
      course: course ?? this.course,
      categoryId: categoryId ?? this.categoryId,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      issueRaised: issueRaised ?? this.issueRaised,
      issueRaisedDate: issueRaisedDate ?? this.issueRaisedDate,
      updatedDate: updatedDate ?? this.updatedDate,
      subQuestionId: subQuestionId ?? this.subQuestionId,
      ticketStatus: ticketStatus ?? this.ticketStatus,
      studentReasonDate: studentReasonDate ?? this.studentReasonDate,
      isEscalate: isEscalate ?? this.isEscalate,
      ticketId: ticketId ?? this.ticketId,
      ticketStatusText: ticketStatusText ?? this.ticketStatusText,
      isFeedbackGiven: isFeedbackGiven ?? this.isFeedbackGiven,
      feedbackRating: feedbackRating ?? this.feedbackRating,
      comment: comment ?? this.comment,
      showStatus: showStatus ?? this.showStatus,
      issueChecked: issueChecked ?? this.issueChecked,
      hideReopenTicket: hideReopenTicket ?? this.hideReopenTicket,
      showEscalate: showEscalate ?? this.showEscalate,
    );
  }

  TicketStatusEnum getTicketStatus() {
    // Only status 4 (Resolved) is considered closed
    if (ticketStatus == 4) return TicketStatusEnum.closed;
    // All other statuses (1:Open, 2:Assigned, 3:Transferred, 5:Reopen, 6:In Progress) are open
    return TicketStatusEnum.open;
  }
}

/// Helpdesk Comment entity
@JsonSerializable()
class HelpdeskComment {
  final int? id;
  
  @JsonKey(name: 'helpdesk_id')
  final int? helpdeskId;
  
  @JsonKey(name: 'created_date')
  @HelpdeskDateTimeConverter()
  final DateTime? createdDate;
  
  @JsonKey(name: 'updated_by')
  final int? updatedBy;
  
  @JsonKey(name: 'category_id')
  final int? categoryId;
  
  @JsonKey(name: 'student_reason')
  final String? studentReason;
  
  @JsonKey(name: 'admin_comment')
  final String? adminComment;
  
  @JsonKey(name: 'student_additional_comment')
  final String? studentAdditionalComment;
  
  @JsonKey(name: 'user_id')
  final int? userId;
  
  @JsonKey(name: 'admin_id')
  final int? adminId;
  
  @JsonKey(name: 'comment_attachments')
  final String? commentAttachments;
  
  @JsonKey(name: 'ticket_status')
  final String? ticketStatus;
  
  final String? time;
  final String? date;

  const HelpdeskComment({
    this.id,
    this.helpdeskId,
    this.createdDate,
    this.updatedBy,
    this.categoryId,
    this.studentReason,
    this.adminComment,
    this.studentAdditionalComment,
    this.userId,
    this.adminId,
    this.commentAttachments,
    this.ticketStatus,
    this.time,
    this.date,
  });

  factory HelpdeskComment.fromJson(Map<String, dynamic> json) =>
      _$HelpdeskCommentFromJson(json);

  Map<String, dynamic> toJson() => _$HelpdeskCommentToJson(this);

  @override
  String toString() {
    return 'HelpdeskComment(id: $id, helpdeskId: $helpdeskId, createdDate: $createdDate, updatedBy: $updatedBy, categoryId: $categoryId, studentReason: $studentReason, adminComment: $adminComment, studentAdditionalComment: $studentAdditionalComment, userId: $userId, adminId: $adminId, commentAttachments: $commentAttachments, ticketStatus: $ticketStatus, time: $time, date: $date)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HelpdeskComment &&
        other.id == id &&
        other.helpdeskId == helpdeskId &&
        other.createdDate == createdDate &&
        other.updatedBy == updatedBy &&
        other.categoryId == categoryId &&
        other.studentReason == studentReason &&
        other.adminComment == adminComment &&
        other.studentAdditionalComment == studentAdditionalComment &&
        other.userId == userId &&
        other.adminId == adminId &&
        other.commentAttachments == commentAttachments &&
        other.ticketStatus == ticketStatus &&
        other.time == time &&
        other.date == date;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        helpdeskId.hashCode ^
        createdDate.hashCode ^
        updatedBy.hashCode ^
        categoryId.hashCode ^
        studentReason.hashCode ^
        adminComment.hashCode ^
        studentAdditionalComment.hashCode ^
        userId.hashCode ^
        adminId.hashCode ^
        commentAttachments.hashCode ^
        ticketStatus.hashCode ^
        time.hashCode ^
        date.hashCode;
  }

  /// Copy with method for immutable updates
  HelpdeskComment copyWith({
    int? id,
    int? helpdeskId,
    DateTime? createdDate,
    int? updatedBy,
    int? categoryId,
    String? studentReason,
    String? adminComment,
    String? studentAdditionalComment,
    int? userId,
    int? adminId,
    String? commentAttachments,
    String? ticketStatus,
    String? time,
    String? date,
  }) {
    return HelpdeskComment(
      id: id ?? this.id,
      helpdeskId: helpdeskId ?? this.helpdeskId,
      createdDate: createdDate ?? this.createdDate,
      updatedBy: updatedBy ?? this.updatedBy,
      categoryId: categoryId ?? this.categoryId,
      studentReason: studentReason ?? this.studentReason,
      adminComment: adminComment ?? this.adminComment,
      studentAdditionalComment: studentAdditionalComment ?? this.studentAdditionalComment,
      userId: userId ?? this.userId,
      adminId: adminId ?? this.adminId,
      commentAttachments: commentAttachments ?? this.commentAttachments,
      ticketStatus: ticketStatus ?? this.ticketStatus,
      time: time ?? this.time,
      date: date ?? this.date,
    );
  }
}
