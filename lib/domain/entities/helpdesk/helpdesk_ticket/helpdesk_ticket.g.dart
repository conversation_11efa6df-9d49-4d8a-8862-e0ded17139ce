// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'helpdesk_ticket.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HelpdeskTicket _$HelpdeskTicketFromJson(Map<String, dynamic> json) =>
    HelpdeskTicket(
      helpdeskId: (json['helpdesk_id'] as num?)?.toInt(),
      course: json['course'] as String?,
      categoryId: (json['category_id'] as num?)?.toInt(),
      category: json['category'] as String?,
      subCategory: json['sub_category'] as String?,
      issueRaised: json['issue_raised'] as String?,
      issueRaisedDate: const HelpdeskDateTimeConverter()
          .fromJson(json['issue_raised_date'] as String?),
      updatedDate: const HelpdeskDateTimeConverter()
          .fromJson(json['updated_date'] as String?),
      subQuestionId: json['sub_question_id'] as String?,
      ticketStatus: (json['ticketStatus'] as num?)?.toInt(),
      studentReasonDate: json['student_reason_date'] as String?,
      isEscalate: (json['is_escalate'] as num?)?.toInt(),
      ticketId: json['ticket_id'] as String?,
      ticketStatusText: json['ticket_status'] as String?,
      isFeedbackGiven: (json['is_feedback_given'] as num?)?.toInt(),
      feedbackRating: (json['feedback_rating'] as num?)?.toInt(),
      comment: (json['comment'] as List<dynamic>?)
          ?.map((e) => HelpdeskComment.fromJson(e as Map<String, dynamic>))
          .toList(),
      showStatus: (json['show_status'] as num?)?.toInt(),
      issueChecked: (json['issue_checked'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      hideReopenTicket: (json['hide_reopen_ticket'] as num?)?.toInt(),
      showEscalate: (json['show_escalate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HelpdeskTicketToJson(HelpdeskTicket instance) =>
    <String, dynamic>{
      'helpdesk_id': instance.helpdeskId,
      'course': instance.course,
      'category_id': instance.categoryId,
      'category': instance.category,
      'sub_category': instance.subCategory,
      'issue_raised': instance.issueRaised,
      'issue_raised_date':
          const HelpdeskDateTimeConverter().toJson(instance.issueRaisedDate),
      'updated_date':
          const HelpdeskDateTimeConverter().toJson(instance.updatedDate),
      'sub_question_id': instance.subQuestionId,
      'ticketStatus': instance.ticketStatus,
      'student_reason_date': instance.studentReasonDate,
      'is_escalate': instance.isEscalate,
      'ticket_id': instance.ticketId,
      'ticket_status': instance.ticketStatusText,
      'is_feedback_given': instance.isFeedbackGiven,
      'feedback_rating': instance.feedbackRating,
      'comment': instance.comment,
      'show_status': instance.showStatus,
      'issue_checked': instance.issueChecked,
      'hide_reopen_ticket': instance.hideReopenTicket,
      'show_escalate': instance.showEscalate,
    };

HelpdeskComment _$HelpdeskCommentFromJson(Map<String, dynamic> json) =>
    HelpdeskComment(
      id: (json['id'] as num?)?.toInt(),
      helpdeskId: (json['helpdesk_id'] as num?)?.toInt(),
      createdDate: const HelpdeskDateTimeConverter()
          .fromJson(json['created_date'] as String?),
      updatedBy: (json['updated_by'] as num?)?.toInt(),
      categoryId: (json['category_id'] as num?)?.toInt(),
      studentReason: json['student_reason'] as String?,
      adminComment: json['admin_comment'] as String?,
      studentAdditionalComment: json['student_additional_comment'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
      adminId: (json['admin_id'] as num?)?.toInt(),
      commentAttachments: json['comment_attachments'] as String?,
      ticketStatus: json['ticket_status'] as String?,
      time: json['time'] as String?,
      date: json['date'] as String?,
    );

Map<String, dynamic> _$HelpdeskCommentToJson(HelpdeskComment instance) =>
    <String, dynamic>{
      'id': instance.id,
      'helpdesk_id': instance.helpdeskId,
      'created_date':
          const HelpdeskDateTimeConverter().toJson(instance.createdDate),
      'updated_by': instance.updatedBy,
      'category_id': instance.categoryId,
      'student_reason': instance.studentReason,
      'admin_comment': instance.adminComment,
      'student_additional_comment': instance.studentAdditionalComment,
      'user_id': instance.userId,
      'admin_id': instance.adminId,
      'comment_attachments': instance.commentAttachments,
      'ticket_status': instance.ticketStatus,
      'time': instance.time,
      'date': instance.date,
    };
