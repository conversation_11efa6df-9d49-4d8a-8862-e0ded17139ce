// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'instructor.details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

InstructorDetails _$InstructorDetailsFromJson(Map<String, dynamic> json) {
  return _InstructorDetails.fromJson(json);
}

/// @nodoc
mixin _$InstructorDetails {
  int get id => throw _privateConstructorUsedError;
  String? get firstName => throw _privateConstructorUsedError;
  String? get lastName => throw _privateConstructorUsedError;
  String? get aboutMe => throw _privateConstructorUsedError;
  String? get facebookLink => throw _privateConstructorUsedError;
  String? get designation => throw _privateConstructorUsedError;
  String? get linkedinLink => throw _privateConstructorUsedError;
  String? get youtubeLink => throw _privateConstructorUsedError;
  String? get instagramLink => throw _privateConstructorUsedError;
  String? get profileImage => throw _privateConstructorUsedError;
  String? get promoVideo => throw _privateConstructorUsedError;

  /// Serializes this InstructorDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of InstructorDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $InstructorDetailsCopyWith<InstructorDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $InstructorDetailsCopyWith<$Res> {
  factory $InstructorDetailsCopyWith(
          InstructorDetails value, $Res Function(InstructorDetails) then) =
      _$InstructorDetailsCopyWithImpl<$Res, InstructorDetails>;
  @useResult
  $Res call(
      {int id,
      String? firstName,
      String? lastName,
      String? aboutMe,
      String? facebookLink,
      String? designation,
      String? linkedinLink,
      String? youtubeLink,
      String? instagramLink,
      String? profileImage,
      String? promoVideo});
}

/// @nodoc
class _$InstructorDetailsCopyWithImpl<$Res, $Val extends InstructorDetails>
    implements $InstructorDetailsCopyWith<$Res> {
  _$InstructorDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of InstructorDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? aboutMe = freezed,
    Object? facebookLink = freezed,
    Object? designation = freezed,
    Object? linkedinLink = freezed,
    Object? youtubeLink = freezed,
    Object? instagramLink = freezed,
    Object? profileImage = freezed,
    Object? promoVideo = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      aboutMe: freezed == aboutMe
          ? _value.aboutMe
          : aboutMe // ignore: cast_nullable_to_non_nullable
              as String?,
      facebookLink: freezed == facebookLink
          ? _value.facebookLink
          : facebookLink // ignore: cast_nullable_to_non_nullable
              as String?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedinLink: freezed == linkedinLink
          ? _value.linkedinLink
          : linkedinLink // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeLink: freezed == youtubeLink
          ? _value.youtubeLink
          : youtubeLink // ignore: cast_nullable_to_non_nullable
              as String?,
      instagramLink: freezed == instagramLink
          ? _value.instagramLink
          : instagramLink // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as String?,
      promoVideo: freezed == promoVideo
          ? _value.promoVideo
          : promoVideo // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$InstructorDetailsImplCopyWith<$Res>
    implements $InstructorDetailsCopyWith<$Res> {
  factory _$$InstructorDetailsImplCopyWith(_$InstructorDetailsImpl value,
          $Res Function(_$InstructorDetailsImpl) then) =
      __$$InstructorDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String? firstName,
      String? lastName,
      String? aboutMe,
      String? facebookLink,
      String? designation,
      String? linkedinLink,
      String? youtubeLink,
      String? instagramLink,
      String? profileImage,
      String? promoVideo});
}

/// @nodoc
class __$$InstructorDetailsImplCopyWithImpl<$Res>
    extends _$InstructorDetailsCopyWithImpl<$Res, _$InstructorDetailsImpl>
    implements _$$InstructorDetailsImplCopyWith<$Res> {
  __$$InstructorDetailsImplCopyWithImpl(_$InstructorDetailsImpl _value,
      $Res Function(_$InstructorDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of InstructorDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = freezed,
    Object? lastName = freezed,
    Object? aboutMe = freezed,
    Object? facebookLink = freezed,
    Object? designation = freezed,
    Object? linkedinLink = freezed,
    Object? youtubeLink = freezed,
    Object? instagramLink = freezed,
    Object? profileImage = freezed,
    Object? promoVideo = freezed,
  }) {
    return _then(_$InstructorDetailsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      firstName: freezed == firstName
          ? _value.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String?,
      lastName: freezed == lastName
          ? _value.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      aboutMe: freezed == aboutMe
          ? _value.aboutMe
          : aboutMe // ignore: cast_nullable_to_non_nullable
              as String?,
      facebookLink: freezed == facebookLink
          ? _value.facebookLink
          : facebookLink // ignore: cast_nullable_to_non_nullable
              as String?,
      designation: freezed == designation
          ? _value.designation
          : designation // ignore: cast_nullable_to_non_nullable
              as String?,
      linkedinLink: freezed == linkedinLink
          ? _value.linkedinLink
          : linkedinLink // ignore: cast_nullable_to_non_nullable
              as String?,
      youtubeLink: freezed == youtubeLink
          ? _value.youtubeLink
          : youtubeLink // ignore: cast_nullable_to_non_nullable
              as String?,
      instagramLink: freezed == instagramLink
          ? _value.instagramLink
          : instagramLink // ignore: cast_nullable_to_non_nullable
              as String?,
      profileImage: freezed == profileImage
          ? _value.profileImage
          : profileImage // ignore: cast_nullable_to_non_nullable
              as String?,
      promoVideo: freezed == promoVideo
          ? _value.promoVideo
          : promoVideo // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$InstructorDetailsImpl implements _InstructorDetails {
  _$InstructorDetailsImpl(
      {required this.id,
      this.firstName,
      this.lastName,
      this.aboutMe,
      this.facebookLink,
      this.designation,
      this.linkedinLink,
      this.youtubeLink,
      this.instagramLink,
      this.profileImage,
      this.promoVideo});

  factory _$InstructorDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$InstructorDetailsImplFromJson(json);

  @override
  final int id;
  @override
  final String? firstName;
  @override
  final String? lastName;
  @override
  final String? aboutMe;
  @override
  final String? facebookLink;
  @override
  final String? designation;
  @override
  final String? linkedinLink;
  @override
  final String? youtubeLink;
  @override
  final String? instagramLink;
  @override
  final String? profileImage;
  @override
  final String? promoVideo;

  @override
  String toString() {
    return 'InstructorDetails(id: $id, firstName: $firstName, lastName: $lastName, aboutMe: $aboutMe, facebookLink: $facebookLink, designation: $designation, linkedinLink: $linkedinLink, youtubeLink: $youtubeLink, instagramLink: $instagramLink, profileImage: $profileImage, promoVideo: $promoVideo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$InstructorDetailsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.aboutMe, aboutMe) || other.aboutMe == aboutMe) &&
            (identical(other.facebookLink, facebookLink) ||
                other.facebookLink == facebookLink) &&
            (identical(other.designation, designation) ||
                other.designation == designation) &&
            (identical(other.linkedinLink, linkedinLink) ||
                other.linkedinLink == linkedinLink) &&
            (identical(other.youtubeLink, youtubeLink) ||
                other.youtubeLink == youtubeLink) &&
            (identical(other.instagramLink, instagramLink) ||
                other.instagramLink == instagramLink) &&
            (identical(other.profileImage, profileImage) ||
                other.profileImage == profileImage) &&
            (identical(other.promoVideo, promoVideo) ||
                other.promoVideo == promoVideo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      aboutMe,
      facebookLink,
      designation,
      linkedinLink,
      youtubeLink,
      instagramLink,
      profileImage,
      promoVideo);

  /// Create a copy of InstructorDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$InstructorDetailsImplCopyWith<_$InstructorDetailsImpl> get copyWith =>
      __$$InstructorDetailsImplCopyWithImpl<_$InstructorDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$InstructorDetailsImplToJson(
      this,
    );
  }
}

abstract class _InstructorDetails implements InstructorDetails {
  factory _InstructorDetails(
      {required final int id,
      final String? firstName,
      final String? lastName,
      final String? aboutMe,
      final String? facebookLink,
      final String? designation,
      final String? linkedinLink,
      final String? youtubeLink,
      final String? instagramLink,
      final String? profileImage,
      final String? promoVideo}) = _$InstructorDetailsImpl;

  factory _InstructorDetails.fromJson(Map<String, dynamic> json) =
      _$InstructorDetailsImpl.fromJson;

  @override
  int get id;
  @override
  String? get firstName;
  @override
  String? get lastName;
  @override
  String? get aboutMe;
  @override
  String? get facebookLink;
  @override
  String? get designation;
  @override
  String? get linkedinLink;
  @override
  String? get youtubeLink;
  @override
  String? get instagramLink;
  @override
  String? get profileImage;
  @override
  String? get promoVideo;

  /// Create a copy of InstructorDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$InstructorDetailsImplCopyWith<_$InstructorDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
