// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'masterclass_summary.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MasterclassSummaryImpl _$$MasterclassSummaryImplFromJson(
        Map<String, dynamic> json) =>
    _$MasterclassSummaryImpl(
      id: (json['id'] as num).toInt(),
      courseCategoryDetails: CourseCategoryDetails.fromJson(
          json['course_category_details'] as Map<String, dynamic>),
      instructorDetails: InstructorDetails.fromJson(
          json['instructor_details'] as Map<String, dynamic>),
      cpeModeDetails: json['cpe_mode_details'] == null
          ? null
          : CpeModeDetails.fromJson(
              json['cpe_mode_details'] as Map<String, dynamic>),
      userAssessmentDetails: json['user_assessment_details'] == null
          ? null
          : UserAssessmentDetails.fromJson(
              json['user_assessment_details'] as Map<String, dynamic>),
      addedBookmark: json['added_bookmark'] as bool?,
      userFeedbackDetails: json['user_feedback_details'] == null
          ? null
          : UserFeedbackDetails.fromJson(
              json['user_feedback_details'] as Map<String, dynamic>),
      userFeedbackSubmitted: json['user_feedback_submitted'] as bool?,
      horizontalThumbnail: json['horizontal_thumbnail'] as String? ?? '',
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      title: json['title'] as String,
      thumbnail: json['thumbnail'] as String?,
      thumbnailGif: json['thumbnail_gif'] as String?,
      mobileThumbnailGif: json['mobile_thumbnail_gif'] as String?,
      enableCoomingSoon: json['enable_cooming_soon'] as bool,
      courseOverview: json['course_overview'] as String,
      learningObjectives: json['learning_objectives'] as String?,
      examRules: json['exam_rules'] as String?,
      classCredits: json['class_credits'] as num,
      trailerLink: json['trailer_link'] as String?,
      sampleLink: json['sample_link'] as String?,
      priorityOrder: (json['priority_order'] as num).toInt(),
      courseCategory: (json['course_category'] as num).toInt(),
      hostInstructor: (json['host_instructor'] as num).toInt(),
    );

Map<String, dynamic> _$$MasterclassSummaryImplToJson(
        _$MasterclassSummaryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'course_category_details': instance.courseCategoryDetails,
      'instructor_details': instance.instructorDetails,
      'cpe_mode_details': instance.cpeModeDetails,
      'user_assessment_details': instance.userAssessmentDetails,
      'added_bookmark': instance.addedBookmark,
      'user_feedback_details': instance.userFeedbackDetails,
      'user_feedback_submitted': instance.userFeedbackSubmitted,
      'horizontal_thumbnail': instance.horizontalThumbnail,
      'updated_at': instance.updatedAt?.toIso8601String(),
      'title': instance.title,
      'thumbnail': instance.thumbnail,
      'thumbnail_gif': instance.thumbnailGif,
      'mobile_thumbnail_gif': instance.mobileThumbnailGif,
      'enable_cooming_soon': instance.enableCoomingSoon,
      'course_overview': instance.courseOverview,
      'learning_objectives': instance.learningObjectives,
      'exam_rules': instance.examRules,
      'class_credits': instance.classCredits,
      'trailer_link': instance.trailerLink,
      'sample_link': instance.sampleLink,
      'priority_order': instance.priorityOrder,
      'course_category': instance.courseCategory,
      'host_instructor': instance.hostInstructor,
    };
