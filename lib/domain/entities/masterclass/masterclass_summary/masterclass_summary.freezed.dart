// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'masterclass_summary.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MasterclassSummary _$MasterclassSummaryFromJson(Map<String, dynamic> json) {
  return _MasterclassSummary.fromJson(json);
}

/// @nodoc
mixin _$MasterclassSummary {
  int get id => throw _privateConstructorUsedError;
  CourseCategoryDetails get courseCategoryDetails =>
      throw _privateConstructorUsedError;
  InstructorDetails get instructorDetails => throw _privateConstructorUsedError;
  CpeModeDetails? get cpeModeDetails => throw _privateConstructorUsedError;
  UserAssessmentDetails? get userAssessmentDetails =>
      throw _privateConstructorUsedError;
  bool? get addedBookmark => throw _privateConstructorUsedError;
  UserFeedbackDetails? get userFeedbackDetails =>
      throw _privateConstructorUsedError;
  bool? get userFeedbackSubmitted => throw _privateConstructorUsedError;
  @JsonKey(defaultValue: '')
  String get horizontalThumbnail => throw _privateConstructorUsedError;
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get thumbnail => throw _privateConstructorUsedError;
  String? get thumbnailGif => throw _privateConstructorUsedError;
  String? get mobileThumbnailGif =>
      throw _privateConstructorUsedError; // TODO: Fix the spelling mistake once it gets fixed in the API
  bool get enableCoomingSoon => throw _privateConstructorUsedError;
  String get courseOverview => throw _privateConstructorUsedError;
  String? get learningObjectives => throw _privateConstructorUsedError;
  String? get examRules => throw _privateConstructorUsedError;
  num get classCredits => throw _privateConstructorUsedError;
  String? get trailerLink => throw _privateConstructorUsedError;
  String? get sampleLink => throw _privateConstructorUsedError;
  int get priorityOrder => throw _privateConstructorUsedError;
  int get courseCategory => throw _privateConstructorUsedError;
  int get hostInstructor => throw _privateConstructorUsedError;

  /// Serializes this MasterclassSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MasterclassSummaryCopyWith<MasterclassSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MasterclassSummaryCopyWith<$Res> {
  factory $MasterclassSummaryCopyWith(
          MasterclassSummary value, $Res Function(MasterclassSummary) then) =
      _$MasterclassSummaryCopyWithImpl<$Res, MasterclassSummary>;
  @useResult
  $Res call(
      {int id,
      CourseCategoryDetails courseCategoryDetails,
      InstructorDetails instructorDetails,
      CpeModeDetails? cpeModeDetails,
      UserAssessmentDetails? userAssessmentDetails,
      bool? addedBookmark,
      UserFeedbackDetails? userFeedbackDetails,
      bool? userFeedbackSubmitted,
      @JsonKey(defaultValue: '') String horizontalThumbnail,
      DateTime? updatedAt,
      String title,
      String? thumbnail,
      String? thumbnailGif,
      String? mobileThumbnailGif,
      bool enableCoomingSoon,
      String courseOverview,
      String? learningObjectives,
      String? examRules,
      num classCredits,
      String? trailerLink,
      String? sampleLink,
      int priorityOrder,
      int courseCategory,
      int hostInstructor});

  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails;
  $InstructorDetailsCopyWith<$Res> get instructorDetails;
  $CpeModeDetailsCopyWith<$Res>? get cpeModeDetails;
  $UserAssessmentDetailsCopyWith<$Res>? get userAssessmentDetails;
  $UserFeedbackDetailsCopyWith<$Res>? get userFeedbackDetails;
}

/// @nodoc
class _$MasterclassSummaryCopyWithImpl<$Res, $Val extends MasterclassSummary>
    implements $MasterclassSummaryCopyWith<$Res> {
  _$MasterclassSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCategoryDetails = null,
    Object? instructorDetails = null,
    Object? cpeModeDetails = freezed,
    Object? userAssessmentDetails = freezed,
    Object? addedBookmark = freezed,
    Object? userFeedbackDetails = freezed,
    Object? userFeedbackSubmitted = freezed,
    Object? horizontalThumbnail = null,
    Object? updatedAt = freezed,
    Object? title = null,
    Object? thumbnail = freezed,
    Object? thumbnailGif = freezed,
    Object? mobileThumbnailGif = freezed,
    Object? enableCoomingSoon = null,
    Object? courseOverview = null,
    Object? learningObjectives = freezed,
    Object? examRules = freezed,
    Object? classCredits = null,
    Object? trailerLink = freezed,
    Object? sampleLink = freezed,
    Object? priorityOrder = null,
    Object? courseCategory = null,
    Object? hostInstructor = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategoryDetails: null == courseCategoryDetails
          ? _value.courseCategoryDetails
          : courseCategoryDetails // ignore: cast_nullable_to_non_nullable
              as CourseCategoryDetails,
      instructorDetails: null == instructorDetails
          ? _value.instructorDetails
          : instructorDetails // ignore: cast_nullable_to_non_nullable
              as InstructorDetails,
      cpeModeDetails: freezed == cpeModeDetails
          ? _value.cpeModeDetails
          : cpeModeDetails // ignore: cast_nullable_to_non_nullable
              as CpeModeDetails?,
      userAssessmentDetails: freezed == userAssessmentDetails
          ? _value.userAssessmentDetails
          : userAssessmentDetails // ignore: cast_nullable_to_non_nullable
              as UserAssessmentDetails?,
      addedBookmark: freezed == addedBookmark
          ? _value.addedBookmark
          : addedBookmark // ignore: cast_nullable_to_non_nullable
              as bool?,
      userFeedbackDetails: freezed == userFeedbackDetails
          ? _value.userFeedbackDetails
          : userFeedbackDetails // ignore: cast_nullable_to_non_nullable
              as UserFeedbackDetails?,
      userFeedbackSubmitted: freezed == userFeedbackSubmitted
          ? _value.userFeedbackSubmitted
          : userFeedbackSubmitted // ignore: cast_nullable_to_non_nullable
              as bool?,
      horizontalThumbnail: null == horizontalThumbnail
          ? _value.horizontalThumbnail
          : horizontalThumbnail // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailGif: freezed == thumbnailGif
          ? _value.thumbnailGif
          : thumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileThumbnailGif: freezed == mobileThumbnailGif
          ? _value.mobileThumbnailGif
          : mobileThumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      enableCoomingSoon: null == enableCoomingSoon
          ? _value.enableCoomingSoon
          : enableCoomingSoon // ignore: cast_nullable_to_non_nullable
              as bool,
      courseOverview: null == courseOverview
          ? _value.courseOverview
          : courseOverview // ignore: cast_nullable_to_non_nullable
              as String,
      learningObjectives: freezed == learningObjectives
          ? _value.learningObjectives
          : learningObjectives // ignore: cast_nullable_to_non_nullable
              as String?,
      examRules: freezed == examRules
          ? _value.examRules
          : examRules // ignore: cast_nullable_to_non_nullable
              as String?,
      classCredits: null == classCredits
          ? _value.classCredits
          : classCredits // ignore: cast_nullable_to_non_nullable
              as num,
      trailerLink: freezed == trailerLink
          ? _value.trailerLink
          : trailerLink // ignore: cast_nullable_to_non_nullable
              as String?,
      sampleLink: freezed == sampleLink
          ? _value.sampleLink
          : sampleLink // ignore: cast_nullable_to_non_nullable
              as String?,
      priorityOrder: null == priorityOrder
          ? _value.priorityOrder
          : priorityOrder // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategory: null == courseCategory
          ? _value.courseCategory
          : courseCategory // ignore: cast_nullable_to_non_nullable
              as int,
      hostInstructor: null == hostInstructor
          ? _value.hostInstructor
          : hostInstructor // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails {
    return $CourseCategoryDetailsCopyWith<$Res>(_value.courseCategoryDetails,
        (value) {
      return _then(_value.copyWith(courseCategoryDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InstructorDetailsCopyWith<$Res> get instructorDetails {
    return $InstructorDetailsCopyWith<$Res>(_value.instructorDetails, (value) {
      return _then(_value.copyWith(instructorDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CpeModeDetailsCopyWith<$Res>? get cpeModeDetails {
    if (_value.cpeModeDetails == null) {
      return null;
    }

    return $CpeModeDetailsCopyWith<$Res>(_value.cpeModeDetails!, (value) {
      return _then(_value.copyWith(cpeModeDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAssessmentDetailsCopyWith<$Res>? get userAssessmentDetails {
    if (_value.userAssessmentDetails == null) {
      return null;
    }

    return $UserAssessmentDetailsCopyWith<$Res>(_value.userAssessmentDetails!,
        (value) {
      return _then(_value.copyWith(userAssessmentDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserFeedbackDetailsCopyWith<$Res>? get userFeedbackDetails {
    if (_value.userFeedbackDetails == null) {
      return null;
    }

    return $UserFeedbackDetailsCopyWith<$Res>(_value.userFeedbackDetails!,
        (value) {
      return _then(_value.copyWith(userFeedbackDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MasterclassSummaryImplCopyWith<$Res>
    implements $MasterclassSummaryCopyWith<$Res> {
  factory _$$MasterclassSummaryImplCopyWith(_$MasterclassSummaryImpl value,
          $Res Function(_$MasterclassSummaryImpl) then) =
      __$$MasterclassSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      CourseCategoryDetails courseCategoryDetails,
      InstructorDetails instructorDetails,
      CpeModeDetails? cpeModeDetails,
      UserAssessmentDetails? userAssessmentDetails,
      bool? addedBookmark,
      UserFeedbackDetails? userFeedbackDetails,
      bool? userFeedbackSubmitted,
      @JsonKey(defaultValue: '') String horizontalThumbnail,
      DateTime? updatedAt,
      String title,
      String? thumbnail,
      String? thumbnailGif,
      String? mobileThumbnailGif,
      bool enableCoomingSoon,
      String courseOverview,
      String? learningObjectives,
      String? examRules,
      num classCredits,
      String? trailerLink,
      String? sampleLink,
      int priorityOrder,
      int courseCategory,
      int hostInstructor});

  @override
  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails;
  @override
  $InstructorDetailsCopyWith<$Res> get instructorDetails;
  @override
  $CpeModeDetailsCopyWith<$Res>? get cpeModeDetails;
  @override
  $UserAssessmentDetailsCopyWith<$Res>? get userAssessmentDetails;
  @override
  $UserFeedbackDetailsCopyWith<$Res>? get userFeedbackDetails;
}

/// @nodoc
class __$$MasterclassSummaryImplCopyWithImpl<$Res>
    extends _$MasterclassSummaryCopyWithImpl<$Res, _$MasterclassSummaryImpl>
    implements _$$MasterclassSummaryImplCopyWith<$Res> {
  __$$MasterclassSummaryImplCopyWithImpl(_$MasterclassSummaryImpl _value,
      $Res Function(_$MasterclassSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCategoryDetails = null,
    Object? instructorDetails = null,
    Object? cpeModeDetails = freezed,
    Object? userAssessmentDetails = freezed,
    Object? addedBookmark = freezed,
    Object? userFeedbackDetails = freezed,
    Object? userFeedbackSubmitted = freezed,
    Object? horizontalThumbnail = null,
    Object? updatedAt = freezed,
    Object? title = null,
    Object? thumbnail = freezed,
    Object? thumbnailGif = freezed,
    Object? mobileThumbnailGif = freezed,
    Object? enableCoomingSoon = null,
    Object? courseOverview = null,
    Object? learningObjectives = freezed,
    Object? examRules = freezed,
    Object? classCredits = null,
    Object? trailerLink = freezed,
    Object? sampleLink = freezed,
    Object? priorityOrder = null,
    Object? courseCategory = null,
    Object? hostInstructor = null,
  }) {
    return _then(_$MasterclassSummaryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategoryDetails: null == courseCategoryDetails
          ? _value.courseCategoryDetails
          : courseCategoryDetails // ignore: cast_nullable_to_non_nullable
              as CourseCategoryDetails,
      instructorDetails: null == instructorDetails
          ? _value.instructorDetails
          : instructorDetails // ignore: cast_nullable_to_non_nullable
              as InstructorDetails,
      cpeModeDetails: freezed == cpeModeDetails
          ? _value.cpeModeDetails
          : cpeModeDetails // ignore: cast_nullable_to_non_nullable
              as CpeModeDetails?,
      userAssessmentDetails: freezed == userAssessmentDetails
          ? _value.userAssessmentDetails
          : userAssessmentDetails // ignore: cast_nullable_to_non_nullable
              as UserAssessmentDetails?,
      addedBookmark: freezed == addedBookmark
          ? _value.addedBookmark
          : addedBookmark // ignore: cast_nullable_to_non_nullable
              as bool?,
      userFeedbackDetails: freezed == userFeedbackDetails
          ? _value.userFeedbackDetails
          : userFeedbackDetails // ignore: cast_nullable_to_non_nullable
              as UserFeedbackDetails?,
      userFeedbackSubmitted: freezed == userFeedbackSubmitted
          ? _value.userFeedbackSubmitted
          : userFeedbackSubmitted // ignore: cast_nullable_to_non_nullable
              as bool?,
      horizontalThumbnail: null == horizontalThumbnail
          ? _value.horizontalThumbnail
          : horizontalThumbnail // ignore: cast_nullable_to_non_nullable
              as String,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailGif: freezed == thumbnailGif
          ? _value.thumbnailGif
          : thumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      mobileThumbnailGif: freezed == mobileThumbnailGif
          ? _value.mobileThumbnailGif
          : mobileThumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      enableCoomingSoon: null == enableCoomingSoon
          ? _value.enableCoomingSoon
          : enableCoomingSoon // ignore: cast_nullable_to_non_nullable
              as bool,
      courseOverview: null == courseOverview
          ? _value.courseOverview
          : courseOverview // ignore: cast_nullable_to_non_nullable
              as String,
      learningObjectives: freezed == learningObjectives
          ? _value.learningObjectives
          : learningObjectives // ignore: cast_nullable_to_non_nullable
              as String?,
      examRules: freezed == examRules
          ? _value.examRules
          : examRules // ignore: cast_nullable_to_non_nullable
              as String?,
      classCredits: null == classCredits
          ? _value.classCredits
          : classCredits // ignore: cast_nullable_to_non_nullable
              as num,
      trailerLink: freezed == trailerLink
          ? _value.trailerLink
          : trailerLink // ignore: cast_nullable_to_non_nullable
              as String?,
      sampleLink: freezed == sampleLink
          ? _value.sampleLink
          : sampleLink // ignore: cast_nullable_to_non_nullable
              as String?,
      priorityOrder: null == priorityOrder
          ? _value.priorityOrder
          : priorityOrder // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategory: null == courseCategory
          ? _value.courseCategory
          : courseCategory // ignore: cast_nullable_to_non_nullable
              as int,
      hostInstructor: null == hostInstructor
          ? _value.hostInstructor
          : hostInstructor // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$MasterclassSummaryImpl implements _MasterclassSummary {
  _$MasterclassSummaryImpl(
      {required this.id,
      required this.courseCategoryDetails,
      required this.instructorDetails,
      this.cpeModeDetails,
      this.userAssessmentDetails,
      this.addedBookmark,
      this.userFeedbackDetails,
      this.userFeedbackSubmitted,
      @JsonKey(defaultValue: '') required this.horizontalThumbnail,
      this.updatedAt,
      required this.title,
      this.thumbnail,
      this.thumbnailGif,
      this.mobileThumbnailGif,
      required this.enableCoomingSoon,
      required this.courseOverview,
      this.learningObjectives,
      this.examRules,
      required this.classCredits,
      this.trailerLink,
      this.sampleLink,
      required this.priorityOrder,
      required this.courseCategory,
      required this.hostInstructor});

  factory _$MasterclassSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$MasterclassSummaryImplFromJson(json);

  @override
  final int id;
  @override
  final CourseCategoryDetails courseCategoryDetails;
  @override
  final InstructorDetails instructorDetails;
  @override
  final CpeModeDetails? cpeModeDetails;
  @override
  final UserAssessmentDetails? userAssessmentDetails;
  @override
  final bool? addedBookmark;
  @override
  final UserFeedbackDetails? userFeedbackDetails;
  @override
  final bool? userFeedbackSubmitted;
  @override
  @JsonKey(defaultValue: '')
  final String horizontalThumbnail;
  @override
  final DateTime? updatedAt;
  @override
  final String title;
  @override
  final String? thumbnail;
  @override
  final String? thumbnailGif;
  @override
  final String? mobileThumbnailGif;
// TODO: Fix the spelling mistake once it gets fixed in the API
  @override
  final bool enableCoomingSoon;
  @override
  final String courseOverview;
  @override
  final String? learningObjectives;
  @override
  final String? examRules;
  @override
  final num classCredits;
  @override
  final String? trailerLink;
  @override
  final String? sampleLink;
  @override
  final int priorityOrder;
  @override
  final int courseCategory;
  @override
  final int hostInstructor;

  @override
  String toString() {
    return 'MasterclassSummary(id: $id, courseCategoryDetails: $courseCategoryDetails, instructorDetails: $instructorDetails, cpeModeDetails: $cpeModeDetails, userAssessmentDetails: $userAssessmentDetails, addedBookmark: $addedBookmark, userFeedbackDetails: $userFeedbackDetails, userFeedbackSubmitted: $userFeedbackSubmitted, horizontalThumbnail: $horizontalThumbnail, updatedAt: $updatedAt, title: $title, thumbnail: $thumbnail, thumbnailGif: $thumbnailGif, mobileThumbnailGif: $mobileThumbnailGif, enableCoomingSoon: $enableCoomingSoon, courseOverview: $courseOverview, learningObjectives: $learningObjectives, examRules: $examRules, classCredits: $classCredits, trailerLink: $trailerLink, sampleLink: $sampleLink, priorityOrder: $priorityOrder, courseCategory: $courseCategory, hostInstructor: $hostInstructor)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MasterclassSummaryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.courseCategoryDetails, courseCategoryDetails) ||
                other.courseCategoryDetails == courseCategoryDetails) &&
            (identical(other.instructorDetails, instructorDetails) ||
                other.instructorDetails == instructorDetails) &&
            (identical(other.cpeModeDetails, cpeModeDetails) ||
                other.cpeModeDetails == cpeModeDetails) &&
            (identical(other.userAssessmentDetails, userAssessmentDetails) ||
                other.userAssessmentDetails == userAssessmentDetails) &&
            (identical(other.addedBookmark, addedBookmark) ||
                other.addedBookmark == addedBookmark) &&
            (identical(other.userFeedbackDetails, userFeedbackDetails) ||
                other.userFeedbackDetails == userFeedbackDetails) &&
            (identical(other.userFeedbackSubmitted, userFeedbackSubmitted) ||
                other.userFeedbackSubmitted == userFeedbackSubmitted) &&
            (identical(other.horizontalThumbnail, horizontalThumbnail) ||
                other.horizontalThumbnail == horizontalThumbnail) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.thumbnailGif, thumbnailGif) ||
                other.thumbnailGif == thumbnailGif) &&
            (identical(other.mobileThumbnailGif, mobileThumbnailGif) ||
                other.mobileThumbnailGif == mobileThumbnailGif) &&
            (identical(other.enableCoomingSoon, enableCoomingSoon) ||
                other.enableCoomingSoon == enableCoomingSoon) &&
            (identical(other.courseOverview, courseOverview) ||
                other.courseOverview == courseOverview) &&
            (identical(other.learningObjectives, learningObjectives) ||
                other.learningObjectives == learningObjectives) &&
            (identical(other.examRules, examRules) ||
                other.examRules == examRules) &&
            (identical(other.classCredits, classCredits) ||
                other.classCredits == classCredits) &&
            (identical(other.trailerLink, trailerLink) ||
                other.trailerLink == trailerLink) &&
            (identical(other.sampleLink, sampleLink) ||
                other.sampleLink == sampleLink) &&
            (identical(other.priorityOrder, priorityOrder) ||
                other.priorityOrder == priorityOrder) &&
            (identical(other.courseCategory, courseCategory) ||
                other.courseCategory == courseCategory) &&
            (identical(other.hostInstructor, hostInstructor) ||
                other.hostInstructor == hostInstructor));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        courseCategoryDetails,
        instructorDetails,
        cpeModeDetails,
        userAssessmentDetails,
        addedBookmark,
        userFeedbackDetails,
        userFeedbackSubmitted,
        horizontalThumbnail,
        updatedAt,
        title,
        thumbnail,
        thumbnailGif,
        mobileThumbnailGif,
        enableCoomingSoon,
        courseOverview,
        learningObjectives,
        examRules,
        classCredits,
        trailerLink,
        sampleLink,
        priorityOrder,
        courseCategory,
        hostInstructor
      ]);

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MasterclassSummaryImplCopyWith<_$MasterclassSummaryImpl> get copyWith =>
      __$$MasterclassSummaryImplCopyWithImpl<_$MasterclassSummaryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MasterclassSummaryImplToJson(
      this,
    );
  }
}

abstract class _MasterclassSummary implements MasterclassSummary {
  factory _MasterclassSummary(
      {required final int id,
      required final CourseCategoryDetails courseCategoryDetails,
      required final InstructorDetails instructorDetails,
      final CpeModeDetails? cpeModeDetails,
      final UserAssessmentDetails? userAssessmentDetails,
      final bool? addedBookmark,
      final UserFeedbackDetails? userFeedbackDetails,
      final bool? userFeedbackSubmitted,
      @JsonKey(defaultValue: '') required final String horizontalThumbnail,
      final DateTime? updatedAt,
      required final String title,
      final String? thumbnail,
      final String? thumbnailGif,
      final String? mobileThumbnailGif,
      required final bool enableCoomingSoon,
      required final String courseOverview,
      final String? learningObjectives,
      final String? examRules,
      required final num classCredits,
      final String? trailerLink,
      final String? sampleLink,
      required final int priorityOrder,
      required final int courseCategory,
      required final int hostInstructor}) = _$MasterclassSummaryImpl;

  factory _MasterclassSummary.fromJson(Map<String, dynamic> json) =
      _$MasterclassSummaryImpl.fromJson;

  @override
  int get id;
  @override
  CourseCategoryDetails get courseCategoryDetails;
  @override
  InstructorDetails get instructorDetails;
  @override
  CpeModeDetails? get cpeModeDetails;
  @override
  UserAssessmentDetails? get userAssessmentDetails;
  @override
  bool? get addedBookmark;
  @override
  UserFeedbackDetails? get userFeedbackDetails;
  @override
  bool? get userFeedbackSubmitted;
  @override
  @JsonKey(defaultValue: '')
  String get horizontalThumbnail;
  @override
  DateTime? get updatedAt;
  @override
  String get title;
  @override
  String? get thumbnail;
  @override
  String? get thumbnailGif;
  @override
  String?
      get mobileThumbnailGif; // TODO: Fix the spelling mistake once it gets fixed in the API
  @override
  bool get enableCoomingSoon;
  @override
  String get courseOverview;
  @override
  String? get learningObjectives;
  @override
  String? get examRules;
  @override
  num get classCredits;
  @override
  String? get trailerLink;
  @override
  String? get sampleLink;
  @override
  int get priorityOrder;
  @override
  int get courseCategory;
  @override
  int get hostInstructor;

  /// Create a copy of MasterclassSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MasterclassSummaryImplCopyWith<_$MasterclassSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
