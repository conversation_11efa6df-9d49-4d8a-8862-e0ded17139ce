// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dashboard_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DashboardDetails _$DashboardDetailsFromJson(Map<String, dynamic> json) {
  return _DashboardDetails.fromJson(json);
}

/// @nodoc
mixin _$DashboardDetails {
  List<CourseCategoryDetails> get masterclassCategory =>
      throw _privateConstructorUsedError;
  List<MasterclassSummary> get highlightMasterclass =>
      throw _privateConstructorUsedError; // TODO: Correct the spelling once BE does that
  List<MasterclassSummary> get coomingSoonMasterclass =>
      throw _privateConstructorUsedError;
  List<MasterclassSummary> get newlyAddedMasterclass =>
      throw _privateConstructorUsedError;
  List<MasterclassSummary> get popularMasterclass =>
      throw _privateConstructorUsedError;

  /// Serializes this DashboardDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DashboardDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DashboardDetailsCopyWith<DashboardDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DashboardDetailsCopyWith<$Res> {
  factory $DashboardDetailsCopyWith(
          DashboardDetails value, $Res Function(DashboardDetails) then) =
      _$DashboardDetailsCopyWithImpl<$Res, DashboardDetails>;
  @useResult
  $Res call(
      {List<CourseCategoryDetails> masterclassCategory,
      List<MasterclassSummary> highlightMasterclass,
      List<MasterclassSummary> coomingSoonMasterclass,
      List<MasterclassSummary> newlyAddedMasterclass,
      List<MasterclassSummary> popularMasterclass});
}

/// @nodoc
class _$DashboardDetailsCopyWithImpl<$Res, $Val extends DashboardDetails>
    implements $DashboardDetailsCopyWith<$Res> {
  _$DashboardDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DashboardDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? masterclassCategory = null,
    Object? highlightMasterclass = null,
    Object? coomingSoonMasterclass = null,
    Object? newlyAddedMasterclass = null,
    Object? popularMasterclass = null,
  }) {
    return _then(_value.copyWith(
      masterclassCategory: null == masterclassCategory
          ? _value.masterclassCategory
          : masterclassCategory // ignore: cast_nullable_to_non_nullable
              as List<CourseCategoryDetails>,
      highlightMasterclass: null == highlightMasterclass
          ? _value.highlightMasterclass
          : highlightMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      coomingSoonMasterclass: null == coomingSoonMasterclass
          ? _value.coomingSoonMasterclass
          : coomingSoonMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      newlyAddedMasterclass: null == newlyAddedMasterclass
          ? _value.newlyAddedMasterclass
          : newlyAddedMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      popularMasterclass: null == popularMasterclass
          ? _value.popularMasterclass
          : popularMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DashboardDetailsImplCopyWith<$Res>
    implements $DashboardDetailsCopyWith<$Res> {
  factory _$$DashboardDetailsImplCopyWith(_$DashboardDetailsImpl value,
          $Res Function(_$DashboardDetailsImpl) then) =
      __$$DashboardDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<CourseCategoryDetails> masterclassCategory,
      List<MasterclassSummary> highlightMasterclass,
      List<MasterclassSummary> coomingSoonMasterclass,
      List<MasterclassSummary> newlyAddedMasterclass,
      List<MasterclassSummary> popularMasterclass});
}

/// @nodoc
class __$$DashboardDetailsImplCopyWithImpl<$Res>
    extends _$DashboardDetailsCopyWithImpl<$Res, _$DashboardDetailsImpl>
    implements _$$DashboardDetailsImplCopyWith<$Res> {
  __$$DashboardDetailsImplCopyWithImpl(_$DashboardDetailsImpl _value,
      $Res Function(_$DashboardDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of DashboardDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? masterclassCategory = null,
    Object? highlightMasterclass = null,
    Object? coomingSoonMasterclass = null,
    Object? newlyAddedMasterclass = null,
    Object? popularMasterclass = null,
  }) {
    return _then(_$DashboardDetailsImpl(
      masterclassCategory: null == masterclassCategory
          ? _value._masterclassCategory
          : masterclassCategory // ignore: cast_nullable_to_non_nullable
              as List<CourseCategoryDetails>,
      highlightMasterclass: null == highlightMasterclass
          ? _value._highlightMasterclass
          : highlightMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      coomingSoonMasterclass: null == coomingSoonMasterclass
          ? _value._coomingSoonMasterclass
          : coomingSoonMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      newlyAddedMasterclass: null == newlyAddedMasterclass
          ? _value._newlyAddedMasterclass
          : newlyAddedMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
      popularMasterclass: null == popularMasterclass
          ? _value._popularMasterclass
          : popularMasterclass // ignore: cast_nullable_to_non_nullable
              as List<MasterclassSummary>,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$DashboardDetailsImpl implements _DashboardDetails {
  _$DashboardDetailsImpl(
      {required final List<CourseCategoryDetails> masterclassCategory,
      required final List<MasterclassSummary> highlightMasterclass,
      required final List<MasterclassSummary> coomingSoonMasterclass,
      required final List<MasterclassSummary> newlyAddedMasterclass,
      required final List<MasterclassSummary> popularMasterclass})
      : _masterclassCategory = masterclassCategory,
        _highlightMasterclass = highlightMasterclass,
        _coomingSoonMasterclass = coomingSoonMasterclass,
        _newlyAddedMasterclass = newlyAddedMasterclass,
        _popularMasterclass = popularMasterclass;

  factory _$DashboardDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DashboardDetailsImplFromJson(json);

  final List<CourseCategoryDetails> _masterclassCategory;
  @override
  List<CourseCategoryDetails> get masterclassCategory {
    if (_masterclassCategory is EqualUnmodifiableListView)
      return _masterclassCategory;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_masterclassCategory);
  }

  final List<MasterclassSummary> _highlightMasterclass;
  @override
  List<MasterclassSummary> get highlightMasterclass {
    if (_highlightMasterclass is EqualUnmodifiableListView)
      return _highlightMasterclass;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_highlightMasterclass);
  }

// TODO: Correct the spelling once BE does that
  final List<MasterclassSummary> _coomingSoonMasterclass;
// TODO: Correct the spelling once BE does that
  @override
  List<MasterclassSummary> get coomingSoonMasterclass {
    if (_coomingSoonMasterclass is EqualUnmodifiableListView)
      return _coomingSoonMasterclass;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_coomingSoonMasterclass);
  }

  final List<MasterclassSummary> _newlyAddedMasterclass;
  @override
  List<MasterclassSummary> get newlyAddedMasterclass {
    if (_newlyAddedMasterclass is EqualUnmodifiableListView)
      return _newlyAddedMasterclass;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_newlyAddedMasterclass);
  }

  final List<MasterclassSummary> _popularMasterclass;
  @override
  List<MasterclassSummary> get popularMasterclass {
    if (_popularMasterclass is EqualUnmodifiableListView)
      return _popularMasterclass;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_popularMasterclass);
  }

  @override
  String toString() {
    return 'DashboardDetails(masterclassCategory: $masterclassCategory, highlightMasterclass: $highlightMasterclass, coomingSoonMasterclass: $coomingSoonMasterclass, newlyAddedMasterclass: $newlyAddedMasterclass, popularMasterclass: $popularMasterclass)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DashboardDetailsImpl &&
            const DeepCollectionEquality()
                .equals(other._masterclassCategory, _masterclassCategory) &&
            const DeepCollectionEquality()
                .equals(other._highlightMasterclass, _highlightMasterclass) &&
            const DeepCollectionEquality().equals(
                other._coomingSoonMasterclass, _coomingSoonMasterclass) &&
            const DeepCollectionEquality()
                .equals(other._newlyAddedMasterclass, _newlyAddedMasterclass) &&
            const DeepCollectionEquality()
                .equals(other._popularMasterclass, _popularMasterclass));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_masterclassCategory),
      const DeepCollectionEquality().hash(_highlightMasterclass),
      const DeepCollectionEquality().hash(_coomingSoonMasterclass),
      const DeepCollectionEquality().hash(_newlyAddedMasterclass),
      const DeepCollectionEquality().hash(_popularMasterclass));

  /// Create a copy of DashboardDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DashboardDetailsImplCopyWith<_$DashboardDetailsImpl> get copyWith =>
      __$$DashboardDetailsImplCopyWithImpl<_$DashboardDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DashboardDetailsImplToJson(
      this,
    );
  }
}

abstract class _DashboardDetails implements DashboardDetails {
  factory _DashboardDetails(
          {required final List<CourseCategoryDetails> masterclassCategory,
          required final List<MasterclassSummary> highlightMasterclass,
          required final List<MasterclassSummary> coomingSoonMasterclass,
          required final List<MasterclassSummary> newlyAddedMasterclass,
          required final List<MasterclassSummary> popularMasterclass}) =
      _$DashboardDetailsImpl;

  factory _DashboardDetails.fromJson(Map<String, dynamic> json) =
      _$DashboardDetailsImpl.fromJson;

  @override
  List<CourseCategoryDetails> get masterclassCategory;
  @override
  List<MasterclassSummary>
      get highlightMasterclass; // TODO: Correct the spelling once BE does that
  @override
  List<MasterclassSummary> get coomingSoonMasterclass;
  @override
  List<MasterclassSummary> get newlyAddedMasterclass;
  @override
  List<MasterclassSummary> get popularMasterclass;

  /// Create a copy of DashboardDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DashboardDetailsImplCopyWith<_$DashboardDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
