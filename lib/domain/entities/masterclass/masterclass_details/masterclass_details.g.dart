// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'masterclass_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MasterclassDetailsImpl _$$MasterclassDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$MasterclassDetailsImpl(
      id: (json['id'] as num).toInt(),
      courseCategoryDetails: CourseCategoryDetails.fromJson(
          json['course_category_details'] as Map<String, dynamic>),
      instructorDetails: InstructorDetails.fromJson(
          json['instructor_details'] as Map<String, dynamic>),
      masterclassChapters: (json['masterclass_chapters'] as List<dynamic>)
          .map((e) => ChapterDetails.fromJson(e as Map<String, dynamic>))
          .toList(),
      addedBookmark: json['added_bookmark'] as bool,
      userFeedbackDetails: UserFeedbackDetails.fromJson(
          json['user_feedback_details'] as Map<String, dynamic>),
      totalDuration: (json['total_duration'] as num).toInt(),
      cpeModeDetails: json['cpe_mode_details'] == null
          ? null
          : CPEModeDetails.fromJson(
              json['cpe_mode_details'] as Map<String, dynamic>),
      userAssessmentDetails: UserAssessmentDetails.fromJson(
          json['user_assessment_details'] as Map<String, dynamic>),
      creditDetails: json['credit_details'] == null
          ? null
          : CreditDetails.fromJson(
              json['credit_details'] as Map<String, dynamic>),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      title: json['title'] as String,
      thumbnail: json['thumbnail'] as String?,
      thumbnailGif: json['thumbnail_gif'] as String?,
      courseType: json['course_type'] as String,
      enableCoomingSoon: json['enable_cooming_soon'] as bool,
      courseOverview: json['course_overview'] as String,
      learningObjectives: json['learning_objectives'] as String?,
      examRules: json['exam_rules'],
      classCredits: (json['class_credits'] as num).toDouble(),
      documentFile: json['document_file'],
      totalAssessmentQuestions:
          (json['total_assessment_questions'] as num).toInt(),
      passPercentage: (json['pass_percentage'] as num).toInt(),
      trailerLink: json['trailer_link'] as String?,
      sampleLink: json['sample_link'] as String?,
      priorityOrder: (json['priority_order'] as num).toInt(),
      courseCategory: (json['course_category'] as num).toInt(),
      hostInstructor: (json['host_instructor'] as num).toInt(),
      allClassesCompleted: json['all_classes_completed'] as bool,
      isAddedToCart: json['is_added_to_cart'] as bool?,
      isFree: json['is_free'] as bool?,
      glossaryTranscriptText: json['glossary_transcript_text'] as String?,
      navigationLink: json['navigation_link'] as String?,
      glossaryDoc: json['glossary_doc'] as String?,
      courseCreatedDate: json['course_created_date'] == null
          ? null
          : DateTime.parse(json['course_created_date'] as String),
      priceDetail: json['price_detail'] == null
          ? null
          : PriceDetail.fromJson(json['price_detail'] as Map<String, dynamic>),
      horizontalThumbnail: json['horizontal_thumbnail'] as String?,
      courseReviewedDate: json['course_reviewed_date'] == null
          ? null
          : DateTime.parse(json['course_reviewed_date'] as String),
      courseUpdatedDate: json['course_updated_date'] == null
          ? null
          : DateTime.parse(json['course_updated_date'] as String),
      topics:
          (json['topics'] as List<dynamic>).map((e) => e as String).toList(),
      intDeliveryMethod: json['int_delivery_method'] as String?,
      programLevel: json['program_level'] as String?,
      courseExpiry: json['course_expiry'] as String,
      prerequisiteEducation: json['prerequisite_education'] as String,
      advancePreparation: json['advance_preparation'] as String,
      activePlan: json['active_plan'] == null
          ? null
          : ActivePlan.fromJson(json['active_plan'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$MasterclassDetailsImplToJson(
        _$MasterclassDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'course_category_details': instance.courseCategoryDetails,
      'instructor_details': instance.instructorDetails,
      'masterclass_chapters': instance.masterclassChapters,
      'added_bookmark': instance.addedBookmark,
      'user_feedback_details': instance.userFeedbackDetails,
      'total_duration': instance.totalDuration,
      'cpe_mode_details': instance.cpeModeDetails,
      'user_assessment_details': instance.userAssessmentDetails,
      'credit_details': instance.creditDetails,
      'updated_at': instance.updatedAt.toIso8601String(),
      'title': instance.title,
      'thumbnail': instance.thumbnail,
      'thumbnail_gif': instance.thumbnailGif,
      'course_type': instance.courseType,
      'enable_cooming_soon': instance.enableCoomingSoon,
      'course_overview': instance.courseOverview,
      'learning_objectives': instance.learningObjectives,
      'exam_rules': instance.examRules,
      'class_credits': instance.classCredits,
      'document_file': instance.documentFile,
      'total_assessment_questions': instance.totalAssessmentQuestions,
      'pass_percentage': instance.passPercentage,
      'trailer_link': instance.trailerLink,
      'sample_link': instance.sampleLink,
      'priority_order': instance.priorityOrder,
      'course_category': instance.courseCategory,
      'host_instructor': instance.hostInstructor,
      'all_classes_completed': instance.allClassesCompleted,
      'is_added_to_cart': instance.isAddedToCart,
      'is_free': instance.isFree,
      'glossary_transcript_text': instance.glossaryTranscriptText,
      'navigation_link': instance.navigationLink,
      'glossary_doc': instance.glossaryDoc,
      'course_created_date': instance.courseCreatedDate?.toIso8601String(),
      'price_detail': instance.priceDetail,
      'horizontal_thumbnail': instance.horizontalThumbnail,
      'course_reviewed_date': instance.courseReviewedDate?.toIso8601String(),
      'course_updated_date': instance.courseUpdatedDate?.toIso8601String(),
      'topics': instance.topics,
      'int_delivery_method': instance.intDeliveryMethod,
      'program_level': instance.programLevel,
      'course_expiry': instance.courseExpiry,
      'prerequisite_education': instance.prerequisiteEducation,
      'advance_preparation': instance.advancePreparation,
      'active_plan': instance.activePlan,
    };

_$CPECreditDetailsImpl _$$CPECreditDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$CPECreditDetailsImpl(
      totalCredit: (json['total_credit'] as num).toInt(),
    );

Map<String, dynamic> _$$CPECreditDetailsImplToJson(
        _$CPECreditDetailsImpl instance) =>
    <String, dynamic>{
      'total_credit': instance.totalCredit,
    };

_$PriceDetailImpl _$$PriceDetailImplFromJson(Map<String, dynamic> json) =>
    _$PriceDetailImpl(
      price: (json['price'] as num).toDouble(),
      currencyCode: json['currency_code'] as String,
      discount: (json['discount'] as num).toDouble(),
      currencySymbol: json['currency_symbol'] as String,
      sellingPrice: (json['selling_price'] as num).toDouble(),
    );

Map<String, dynamic> _$$PriceDetailImplToJson(_$PriceDetailImpl instance) =>
    <String, dynamic>{
      'price': instance.price,
      'currency_code': instance.currencyCode,
      'discount': instance.discount,
      'currency_symbol': instance.currencySymbol,
      'selling_price': instance.sellingPrice,
    };
