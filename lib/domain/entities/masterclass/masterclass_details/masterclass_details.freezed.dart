// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'masterclass_details.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MasterclassDetails _$MasterclassDetailsFromJson(Map<String, dynamic> json) {
  return _MasterclassDetails.fromJson(json);
}

/// @nodoc
mixin _$MasterclassDetails {
  int get id => throw _privateConstructorUsedError;
  CourseCategoryDetails get courseCategoryDetails =>
      throw _privateConstructorUsedError;
  InstructorDetails get instructorDetails => throw _privateConstructorUsedError;
  List<ChapterDetails> get masterclassChapters =>
      throw _privateConstructorUsedError;
  bool get addedBookmark => throw _privateConstructorUsedError;
  UserFeedbackDetails get userFeedbackDetails =>
      throw _privateConstructorUsedError;
  int get totalDuration => throw _privateConstructorUsedError;
  CPEModeDetails? get cpeModeDetails => throw _privateConstructorUsedError;
  UserAssessmentDetails get userAssessmentDetails =>
      throw _privateConstructorUsedError;
  CreditDetails? get creditDetails => throw _privateConstructorUsedError;
  DateTime get updatedAt => throw _privateConstructorUsedError;
  String get title => throw _privateConstructorUsedError;
  String? get thumbnail => throw _privateConstructorUsedError;
  String? get thumbnailGif => throw _privateConstructorUsedError;
  String get courseType => throw _privateConstructorUsedError;
  bool get enableCoomingSoon => throw _privateConstructorUsedError;
  String get courseOverview => throw _privateConstructorUsedError;
  String? get learningObjectives => throw _privateConstructorUsedError;
  dynamic get examRules => throw _privateConstructorUsedError;
  double get classCredits => throw _privateConstructorUsedError;
  dynamic get documentFile => throw _privateConstructorUsedError;
  int get totalAssessmentQuestions => throw _privateConstructorUsedError;
  int get passPercentage => throw _privateConstructorUsedError;
  String? get trailerLink => throw _privateConstructorUsedError;
  String? get sampleLink => throw _privateConstructorUsedError;
  int get priorityOrder => throw _privateConstructorUsedError;
  int get courseCategory => throw _privateConstructorUsedError;
  int get hostInstructor => throw _privateConstructorUsedError;
  bool get allClassesCompleted => throw _privateConstructorUsedError;
  bool? get isAddedToCart => throw _privateConstructorUsedError;
  bool? get isFree => throw _privateConstructorUsedError;
  String? get glossaryTranscriptText => throw _privateConstructorUsedError;
  String? get navigationLink => throw _privateConstructorUsedError;
  String? get glossaryDoc => throw _privateConstructorUsedError;
  DateTime? get courseCreatedDate => throw _privateConstructorUsedError;
  PriceDetail? get priceDetail => throw _privateConstructorUsedError;
  String? get horizontalThumbnail => throw _privateConstructorUsedError;
  DateTime? get courseReviewedDate => throw _privateConstructorUsedError;
  DateTime? get courseUpdatedDate => throw _privateConstructorUsedError;
  List<String> get topics => throw _privateConstructorUsedError;
  String? get intDeliveryMethod => throw _privateConstructorUsedError;
  String? get programLevel => throw _privateConstructorUsedError;
  String get courseExpiry => throw _privateConstructorUsedError;
  String get prerequisiteEducation => throw _privateConstructorUsedError;
  String get advancePreparation => throw _privateConstructorUsedError;
  ActivePlan? get activePlan => throw _privateConstructorUsedError;

  /// Serializes this MasterclassDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MasterclassDetailsCopyWith<MasterclassDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MasterclassDetailsCopyWith<$Res> {
  factory $MasterclassDetailsCopyWith(
          MasterclassDetails value, $Res Function(MasterclassDetails) then) =
      _$MasterclassDetailsCopyWithImpl<$Res, MasterclassDetails>;
  @useResult
  $Res call(
      {int id,
      CourseCategoryDetails courseCategoryDetails,
      InstructorDetails instructorDetails,
      List<ChapterDetails> masterclassChapters,
      bool addedBookmark,
      UserFeedbackDetails userFeedbackDetails,
      int totalDuration,
      CPEModeDetails? cpeModeDetails,
      UserAssessmentDetails userAssessmentDetails,
      CreditDetails? creditDetails,
      DateTime updatedAt,
      String title,
      String? thumbnail,
      String? thumbnailGif,
      String courseType,
      bool enableCoomingSoon,
      String courseOverview,
      String? learningObjectives,
      dynamic examRules,
      double classCredits,
      dynamic documentFile,
      int totalAssessmentQuestions,
      int passPercentage,
      String? trailerLink,
      String? sampleLink,
      int priorityOrder,
      int courseCategory,
      int hostInstructor,
      bool allClassesCompleted,
      bool? isAddedToCart,
      bool? isFree,
      String? glossaryTranscriptText,
      String? navigationLink,
      String? glossaryDoc,
      DateTime? courseCreatedDate,
      PriceDetail? priceDetail,
      String? horizontalThumbnail,
      DateTime? courseReviewedDate,
      DateTime? courseUpdatedDate,
      List<String> topics,
      String? intDeliveryMethod,
      String? programLevel,
      String courseExpiry,
      String prerequisiteEducation,
      String advancePreparation,
      ActivePlan? activePlan});

  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails;
  $InstructorDetailsCopyWith<$Res> get instructorDetails;
  $UserFeedbackDetailsCopyWith<$Res> get userFeedbackDetails;
  $CPEModeDetailsCopyWith<$Res>? get cpeModeDetails;
  $UserAssessmentDetailsCopyWith<$Res> get userAssessmentDetails;
  $CreditDetailsCopyWith<$Res>? get creditDetails;
  $PriceDetailCopyWith<$Res>? get priceDetail;
}

/// @nodoc
class _$MasterclassDetailsCopyWithImpl<$Res, $Val extends MasterclassDetails>
    implements $MasterclassDetailsCopyWith<$Res> {
  _$MasterclassDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCategoryDetails = null,
    Object? instructorDetails = null,
    Object? masterclassChapters = null,
    Object? addedBookmark = null,
    Object? userFeedbackDetails = null,
    Object? totalDuration = null,
    Object? cpeModeDetails = freezed,
    Object? userAssessmentDetails = null,
    Object? creditDetails = freezed,
    Object? updatedAt = null,
    Object? title = null,
    Object? thumbnail = freezed,
    Object? thumbnailGif = freezed,
    Object? courseType = null,
    Object? enableCoomingSoon = null,
    Object? courseOverview = null,
    Object? learningObjectives = freezed,
    Object? examRules = freezed,
    Object? classCredits = null,
    Object? documentFile = freezed,
    Object? totalAssessmentQuestions = null,
    Object? passPercentage = null,
    Object? trailerLink = freezed,
    Object? sampleLink = freezed,
    Object? priorityOrder = null,
    Object? courseCategory = null,
    Object? hostInstructor = null,
    Object? allClassesCompleted = null,
    Object? isAddedToCart = freezed,
    Object? isFree = freezed,
    Object? glossaryTranscriptText = freezed,
    Object? navigationLink = freezed,
    Object? glossaryDoc = freezed,
    Object? courseCreatedDate = freezed,
    Object? priceDetail = freezed,
    Object? horizontalThumbnail = freezed,
    Object? courseReviewedDate = freezed,
    Object? courseUpdatedDate = freezed,
    Object? topics = null,
    Object? intDeliveryMethod = freezed,
    Object? programLevel = freezed,
    Object? courseExpiry = null,
    Object? prerequisiteEducation = null,
    Object? advancePreparation = null,
    Object? activePlan = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategoryDetails: null == courseCategoryDetails
          ? _value.courseCategoryDetails
          : courseCategoryDetails // ignore: cast_nullable_to_non_nullable
              as CourseCategoryDetails,
      instructorDetails: null == instructorDetails
          ? _value.instructorDetails
          : instructorDetails // ignore: cast_nullable_to_non_nullable
              as InstructorDetails,
      masterclassChapters: null == masterclassChapters
          ? _value.masterclassChapters
          : masterclassChapters // ignore: cast_nullable_to_non_nullable
              as List<ChapterDetails>,
      addedBookmark: null == addedBookmark
          ? _value.addedBookmark
          : addedBookmark // ignore: cast_nullable_to_non_nullable
              as bool,
      userFeedbackDetails: null == userFeedbackDetails
          ? _value.userFeedbackDetails
          : userFeedbackDetails // ignore: cast_nullable_to_non_nullable
              as UserFeedbackDetails,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as int,
      cpeModeDetails: freezed == cpeModeDetails
          ? _value.cpeModeDetails
          : cpeModeDetails // ignore: cast_nullable_to_non_nullable
              as CPEModeDetails?,
      userAssessmentDetails: null == userAssessmentDetails
          ? _value.userAssessmentDetails
          : userAssessmentDetails // ignore: cast_nullable_to_non_nullable
              as UserAssessmentDetails,
      creditDetails: freezed == creditDetails
          ? _value.creditDetails
          : creditDetails // ignore: cast_nullable_to_non_nullable
              as CreditDetails?,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailGif: freezed == thumbnailGif
          ? _value.thumbnailGif
          : thumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: null == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String,
      enableCoomingSoon: null == enableCoomingSoon
          ? _value.enableCoomingSoon
          : enableCoomingSoon // ignore: cast_nullable_to_non_nullable
              as bool,
      courseOverview: null == courseOverview
          ? _value.courseOverview
          : courseOverview // ignore: cast_nullable_to_non_nullable
              as String,
      learningObjectives: freezed == learningObjectives
          ? _value.learningObjectives
          : learningObjectives // ignore: cast_nullable_to_non_nullable
              as String?,
      examRules: freezed == examRules
          ? _value.examRules
          : examRules // ignore: cast_nullable_to_non_nullable
              as dynamic,
      classCredits: null == classCredits
          ? _value.classCredits
          : classCredits // ignore: cast_nullable_to_non_nullable
              as double,
      documentFile: freezed == documentFile
          ? _value.documentFile
          : documentFile // ignore: cast_nullable_to_non_nullable
              as dynamic,
      totalAssessmentQuestions: null == totalAssessmentQuestions
          ? _value.totalAssessmentQuestions
          : totalAssessmentQuestions // ignore: cast_nullable_to_non_nullable
              as int,
      passPercentage: null == passPercentage
          ? _value.passPercentage
          : passPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      trailerLink: freezed == trailerLink
          ? _value.trailerLink
          : trailerLink // ignore: cast_nullable_to_non_nullable
              as String?,
      sampleLink: freezed == sampleLink
          ? _value.sampleLink
          : sampleLink // ignore: cast_nullable_to_non_nullable
              as String?,
      priorityOrder: null == priorityOrder
          ? _value.priorityOrder
          : priorityOrder // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategory: null == courseCategory
          ? _value.courseCategory
          : courseCategory // ignore: cast_nullable_to_non_nullable
              as int,
      hostInstructor: null == hostInstructor
          ? _value.hostInstructor
          : hostInstructor // ignore: cast_nullable_to_non_nullable
              as int,
      allClassesCompleted: null == allClassesCompleted
          ? _value.allClassesCompleted
          : allClassesCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddedToCart: freezed == isAddedToCart
          ? _value.isAddedToCart
          : isAddedToCart // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as bool?,
      glossaryTranscriptText: freezed == glossaryTranscriptText
          ? _value.glossaryTranscriptText
          : glossaryTranscriptText // ignore: cast_nullable_to_non_nullable
              as String?,
      navigationLink: freezed == navigationLink
          ? _value.navigationLink
          : navigationLink // ignore: cast_nullable_to_non_nullable
              as String?,
      glossaryDoc: freezed == glossaryDoc
          ? _value.glossaryDoc
          : glossaryDoc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCreatedDate: freezed == courseCreatedDate
          ? _value.courseCreatedDate
          : courseCreatedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      priceDetail: freezed == priceDetail
          ? _value.priceDetail
          : priceDetail // ignore: cast_nullable_to_non_nullable
              as PriceDetail?,
      horizontalThumbnail: freezed == horizontalThumbnail
          ? _value.horizontalThumbnail
          : horizontalThumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      courseReviewedDate: freezed == courseReviewedDate
          ? _value.courseReviewedDate
          : courseReviewedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      courseUpdatedDate: freezed == courseUpdatedDate
          ? _value.courseUpdatedDate
          : courseUpdatedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      topics: null == topics
          ? _value.topics
          : topics // ignore: cast_nullable_to_non_nullable
              as List<String>,
      intDeliveryMethod: freezed == intDeliveryMethod
          ? _value.intDeliveryMethod
          : intDeliveryMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      programLevel: freezed == programLevel
          ? _value.programLevel
          : programLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseExpiry: null == courseExpiry
          ? _value.courseExpiry
          : courseExpiry // ignore: cast_nullable_to_non_nullable
              as String,
      prerequisiteEducation: null == prerequisiteEducation
          ? _value.prerequisiteEducation
          : prerequisiteEducation // ignore: cast_nullable_to_non_nullable
              as String,
      advancePreparation: null == advancePreparation
          ? _value.advancePreparation
          : advancePreparation // ignore: cast_nullable_to_non_nullable
              as String,
      activePlan: freezed == activePlan
          ? _value.activePlan
          : activePlan // ignore: cast_nullable_to_non_nullable
              as ActivePlan?,
    ) as $Val);
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails {
    return $CourseCategoryDetailsCopyWith<$Res>(_value.courseCategoryDetails,
        (value) {
      return _then(_value.copyWith(courseCategoryDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InstructorDetailsCopyWith<$Res> get instructorDetails {
    return $InstructorDetailsCopyWith<$Res>(_value.instructorDetails, (value) {
      return _then(_value.copyWith(instructorDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserFeedbackDetailsCopyWith<$Res> get userFeedbackDetails {
    return $UserFeedbackDetailsCopyWith<$Res>(_value.userFeedbackDetails,
        (value) {
      return _then(_value.copyWith(userFeedbackDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CPEModeDetailsCopyWith<$Res>? get cpeModeDetails {
    if (_value.cpeModeDetails == null) {
      return null;
    }

    return $CPEModeDetailsCopyWith<$Res>(_value.cpeModeDetails!, (value) {
      return _then(_value.copyWith(cpeModeDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserAssessmentDetailsCopyWith<$Res> get userAssessmentDetails {
    return $UserAssessmentDetailsCopyWith<$Res>(_value.userAssessmentDetails,
        (value) {
      return _then(_value.copyWith(userAssessmentDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CreditDetailsCopyWith<$Res>? get creditDetails {
    if (_value.creditDetails == null) {
      return null;
    }

    return $CreditDetailsCopyWith<$Res>(_value.creditDetails!, (value) {
      return _then(_value.copyWith(creditDetails: value) as $Val);
    });
  }

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PriceDetailCopyWith<$Res>? get priceDetail {
    if (_value.priceDetail == null) {
      return null;
    }

    return $PriceDetailCopyWith<$Res>(_value.priceDetail!, (value) {
      return _then(_value.copyWith(priceDetail: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MasterclassDetailsImplCopyWith<$Res>
    implements $MasterclassDetailsCopyWith<$Res> {
  factory _$$MasterclassDetailsImplCopyWith(_$MasterclassDetailsImpl value,
          $Res Function(_$MasterclassDetailsImpl) then) =
      __$$MasterclassDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      CourseCategoryDetails courseCategoryDetails,
      InstructorDetails instructorDetails,
      List<ChapterDetails> masterclassChapters,
      bool addedBookmark,
      UserFeedbackDetails userFeedbackDetails,
      int totalDuration,
      CPEModeDetails? cpeModeDetails,
      UserAssessmentDetails userAssessmentDetails,
      CreditDetails? creditDetails,
      DateTime updatedAt,
      String title,
      String? thumbnail,
      String? thumbnailGif,
      String courseType,
      bool enableCoomingSoon,
      String courseOverview,
      String? learningObjectives,
      dynamic examRules,
      double classCredits,
      dynamic documentFile,
      int totalAssessmentQuestions,
      int passPercentage,
      String? trailerLink,
      String? sampleLink,
      int priorityOrder,
      int courseCategory,
      int hostInstructor,
      bool allClassesCompleted,
      bool? isAddedToCart,
      bool? isFree,
      String? glossaryTranscriptText,
      String? navigationLink,
      String? glossaryDoc,
      DateTime? courseCreatedDate,
      PriceDetail? priceDetail,
      String? horizontalThumbnail,
      DateTime? courseReviewedDate,
      DateTime? courseUpdatedDate,
      List<String> topics,
      String? intDeliveryMethod,
      String? programLevel,
      String courseExpiry,
      String prerequisiteEducation,
      String advancePreparation,
      ActivePlan? activePlan});

  @override
  $CourseCategoryDetailsCopyWith<$Res> get courseCategoryDetails;
  @override
  $InstructorDetailsCopyWith<$Res> get instructorDetails;
  @override
  $UserFeedbackDetailsCopyWith<$Res> get userFeedbackDetails;
  @override
  $CPEModeDetailsCopyWith<$Res>? get cpeModeDetails;
  @override
  $UserAssessmentDetailsCopyWith<$Res> get userAssessmentDetails;
  @override
  $CreditDetailsCopyWith<$Res>? get creditDetails;
  @override
  $PriceDetailCopyWith<$Res>? get priceDetail;
}

/// @nodoc
class __$$MasterclassDetailsImplCopyWithImpl<$Res>
    extends _$MasterclassDetailsCopyWithImpl<$Res, _$MasterclassDetailsImpl>
    implements _$$MasterclassDetailsImplCopyWith<$Res> {
  __$$MasterclassDetailsImplCopyWithImpl(_$MasterclassDetailsImpl _value,
      $Res Function(_$MasterclassDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? courseCategoryDetails = null,
    Object? instructorDetails = null,
    Object? masterclassChapters = null,
    Object? addedBookmark = null,
    Object? userFeedbackDetails = null,
    Object? totalDuration = null,
    Object? cpeModeDetails = freezed,
    Object? userAssessmentDetails = null,
    Object? creditDetails = freezed,
    Object? updatedAt = null,
    Object? title = null,
    Object? thumbnail = freezed,
    Object? thumbnailGif = freezed,
    Object? courseType = null,
    Object? enableCoomingSoon = null,
    Object? courseOverview = null,
    Object? learningObjectives = freezed,
    Object? examRules = freezed,
    Object? classCredits = null,
    Object? documentFile = freezed,
    Object? totalAssessmentQuestions = null,
    Object? passPercentage = null,
    Object? trailerLink = freezed,
    Object? sampleLink = freezed,
    Object? priorityOrder = null,
    Object? courseCategory = null,
    Object? hostInstructor = null,
    Object? allClassesCompleted = null,
    Object? isAddedToCart = freezed,
    Object? isFree = freezed,
    Object? glossaryTranscriptText = freezed,
    Object? navigationLink = freezed,
    Object? glossaryDoc = freezed,
    Object? courseCreatedDate = freezed,
    Object? priceDetail = freezed,
    Object? horizontalThumbnail = freezed,
    Object? courseReviewedDate = freezed,
    Object? courseUpdatedDate = freezed,
    Object? topics = null,
    Object? intDeliveryMethod = freezed,
    Object? programLevel = freezed,
    Object? courseExpiry = null,
    Object? prerequisiteEducation = null,
    Object? advancePreparation = null,
    Object? activePlan = freezed,
  }) {
    return _then(_$MasterclassDetailsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategoryDetails: null == courseCategoryDetails
          ? _value.courseCategoryDetails
          : courseCategoryDetails // ignore: cast_nullable_to_non_nullable
              as CourseCategoryDetails,
      instructorDetails: null == instructorDetails
          ? _value.instructorDetails
          : instructorDetails // ignore: cast_nullable_to_non_nullable
              as InstructorDetails,
      masterclassChapters: null == masterclassChapters
          ? _value._masterclassChapters
          : masterclassChapters // ignore: cast_nullable_to_non_nullable
              as List<ChapterDetails>,
      addedBookmark: null == addedBookmark
          ? _value.addedBookmark
          : addedBookmark // ignore: cast_nullable_to_non_nullable
              as bool,
      userFeedbackDetails: null == userFeedbackDetails
          ? _value.userFeedbackDetails
          : userFeedbackDetails // ignore: cast_nullable_to_non_nullable
              as UserFeedbackDetails,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as int,
      cpeModeDetails: freezed == cpeModeDetails
          ? _value.cpeModeDetails
          : cpeModeDetails // ignore: cast_nullable_to_non_nullable
              as CPEModeDetails?,
      userAssessmentDetails: null == userAssessmentDetails
          ? _value.userAssessmentDetails
          : userAssessmentDetails // ignore: cast_nullable_to_non_nullable
              as UserAssessmentDetails,
      creditDetails: freezed == creditDetails
          ? _value.creditDetails
          : creditDetails // ignore: cast_nullable_to_non_nullable
              as CreditDetails?,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnail: freezed == thumbnail
          ? _value.thumbnail
          : thumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      thumbnailGif: freezed == thumbnailGif
          ? _value.thumbnailGif
          : thumbnailGif // ignore: cast_nullable_to_non_nullable
              as String?,
      courseType: null == courseType
          ? _value.courseType
          : courseType // ignore: cast_nullable_to_non_nullable
              as String,
      enableCoomingSoon: null == enableCoomingSoon
          ? _value.enableCoomingSoon
          : enableCoomingSoon // ignore: cast_nullable_to_non_nullable
              as bool,
      courseOverview: null == courseOverview
          ? _value.courseOverview
          : courseOverview // ignore: cast_nullable_to_non_nullable
              as String,
      learningObjectives: freezed == learningObjectives
          ? _value.learningObjectives
          : learningObjectives // ignore: cast_nullable_to_non_nullable
              as String?,
      examRules: freezed == examRules
          ? _value.examRules
          : examRules // ignore: cast_nullable_to_non_nullable
              as dynamic,
      classCredits: null == classCredits
          ? _value.classCredits
          : classCredits // ignore: cast_nullable_to_non_nullable
              as double,
      documentFile: freezed == documentFile
          ? _value.documentFile
          : documentFile // ignore: cast_nullable_to_non_nullable
              as dynamic,
      totalAssessmentQuestions: null == totalAssessmentQuestions
          ? _value.totalAssessmentQuestions
          : totalAssessmentQuestions // ignore: cast_nullable_to_non_nullable
              as int,
      passPercentage: null == passPercentage
          ? _value.passPercentage
          : passPercentage // ignore: cast_nullable_to_non_nullable
              as int,
      trailerLink: freezed == trailerLink
          ? _value.trailerLink
          : trailerLink // ignore: cast_nullable_to_non_nullable
              as String?,
      sampleLink: freezed == sampleLink
          ? _value.sampleLink
          : sampleLink // ignore: cast_nullable_to_non_nullable
              as String?,
      priorityOrder: null == priorityOrder
          ? _value.priorityOrder
          : priorityOrder // ignore: cast_nullable_to_non_nullable
              as int,
      courseCategory: null == courseCategory
          ? _value.courseCategory
          : courseCategory // ignore: cast_nullable_to_non_nullable
              as int,
      hostInstructor: null == hostInstructor
          ? _value.hostInstructor
          : hostInstructor // ignore: cast_nullable_to_non_nullable
              as int,
      allClassesCompleted: null == allClassesCompleted
          ? _value.allClassesCompleted
          : allClassesCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      isAddedToCart: freezed == isAddedToCart
          ? _value.isAddedToCart
          : isAddedToCart // ignore: cast_nullable_to_non_nullable
              as bool?,
      isFree: freezed == isFree
          ? _value.isFree
          : isFree // ignore: cast_nullable_to_non_nullable
              as bool?,
      glossaryTranscriptText: freezed == glossaryTranscriptText
          ? _value.glossaryTranscriptText
          : glossaryTranscriptText // ignore: cast_nullable_to_non_nullable
              as String?,
      navigationLink: freezed == navigationLink
          ? _value.navigationLink
          : navigationLink // ignore: cast_nullable_to_non_nullable
              as String?,
      glossaryDoc: freezed == glossaryDoc
          ? _value.glossaryDoc
          : glossaryDoc // ignore: cast_nullable_to_non_nullable
              as String?,
      courseCreatedDate: freezed == courseCreatedDate
          ? _value.courseCreatedDate
          : courseCreatedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      priceDetail: freezed == priceDetail
          ? _value.priceDetail
          : priceDetail // ignore: cast_nullable_to_non_nullable
              as PriceDetail?,
      horizontalThumbnail: freezed == horizontalThumbnail
          ? _value.horizontalThumbnail
          : horizontalThumbnail // ignore: cast_nullable_to_non_nullable
              as String?,
      courseReviewedDate: freezed == courseReviewedDate
          ? _value.courseReviewedDate
          : courseReviewedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      courseUpdatedDate: freezed == courseUpdatedDate
          ? _value.courseUpdatedDate
          : courseUpdatedDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      topics: null == topics
          ? _value._topics
          : topics // ignore: cast_nullable_to_non_nullable
              as List<String>,
      intDeliveryMethod: freezed == intDeliveryMethod
          ? _value.intDeliveryMethod
          : intDeliveryMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      programLevel: freezed == programLevel
          ? _value.programLevel
          : programLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      courseExpiry: null == courseExpiry
          ? _value.courseExpiry
          : courseExpiry // ignore: cast_nullable_to_non_nullable
              as String,
      prerequisiteEducation: null == prerequisiteEducation
          ? _value.prerequisiteEducation
          : prerequisiteEducation // ignore: cast_nullable_to_non_nullable
              as String,
      advancePreparation: null == advancePreparation
          ? _value.advancePreparation
          : advancePreparation // ignore: cast_nullable_to_non_nullable
              as String,
      activePlan: freezed == activePlan
          ? _value.activePlan
          : activePlan // ignore: cast_nullable_to_non_nullable
              as ActivePlan?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$MasterclassDetailsImpl implements _MasterclassDetails {
  const _$MasterclassDetailsImpl(
      {required this.id,
      required this.courseCategoryDetails,
      required this.instructorDetails,
      required final List<ChapterDetails> masterclassChapters,
      required this.addedBookmark,
      required this.userFeedbackDetails,
      required this.totalDuration,
      required this.cpeModeDetails,
      required this.userAssessmentDetails,
      required this.creditDetails,
      required this.updatedAt,
      required this.title,
      required this.thumbnail,
      required this.thumbnailGif,
      required this.courseType,
      required this.enableCoomingSoon,
      required this.courseOverview,
      required this.learningObjectives,
      required this.examRules,
      required this.classCredits,
      required this.documentFile,
      required this.totalAssessmentQuestions,
      required this.passPercentage,
      required this.trailerLink,
      required this.sampleLink,
      required this.priorityOrder,
      required this.courseCategory,
      required this.hostInstructor,
      required this.allClassesCompleted,
      this.isAddedToCart,
      this.isFree,
      this.glossaryTranscriptText,
      this.navigationLink,
      this.glossaryDoc,
      this.courseCreatedDate,
      this.priceDetail,
      this.horizontalThumbnail,
      this.courseReviewedDate,
      this.courseUpdatedDate,
      required final List<String> topics,
      this.intDeliveryMethod,
      this.programLevel,
      required this.courseExpiry,
      required this.prerequisiteEducation,
      required this.advancePreparation,
      this.activePlan})
      : _masterclassChapters = masterclassChapters,
        _topics = topics;

  factory _$MasterclassDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$MasterclassDetailsImplFromJson(json);

  @override
  final int id;
  @override
  final CourseCategoryDetails courseCategoryDetails;
  @override
  final InstructorDetails instructorDetails;
  final List<ChapterDetails> _masterclassChapters;
  @override
  List<ChapterDetails> get masterclassChapters {
    if (_masterclassChapters is EqualUnmodifiableListView)
      return _masterclassChapters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_masterclassChapters);
  }

  @override
  final bool addedBookmark;
  @override
  final UserFeedbackDetails userFeedbackDetails;
  @override
  final int totalDuration;
  @override
  final CPEModeDetails? cpeModeDetails;
  @override
  final UserAssessmentDetails userAssessmentDetails;
  @override
  final CreditDetails? creditDetails;
  @override
  final DateTime updatedAt;
  @override
  final String title;
  @override
  final String? thumbnail;
  @override
  final String? thumbnailGif;
  @override
  final String courseType;
  @override
  final bool enableCoomingSoon;
  @override
  final String courseOverview;
  @override
  final String? learningObjectives;
  @override
  final dynamic examRules;
  @override
  final double classCredits;
  @override
  final dynamic documentFile;
  @override
  final int totalAssessmentQuestions;
  @override
  final int passPercentage;
  @override
  final String? trailerLink;
  @override
  final String? sampleLink;
  @override
  final int priorityOrder;
  @override
  final int courseCategory;
  @override
  final int hostInstructor;
  @override
  final bool allClassesCompleted;
  @override
  final bool? isAddedToCart;
  @override
  final bool? isFree;
  @override
  final String? glossaryTranscriptText;
  @override
  final String? navigationLink;
  @override
  final String? glossaryDoc;
  @override
  final DateTime? courseCreatedDate;
  @override
  final PriceDetail? priceDetail;
  @override
  final String? horizontalThumbnail;
  @override
  final DateTime? courseReviewedDate;
  @override
  final DateTime? courseUpdatedDate;
  final List<String> _topics;
  @override
  List<String> get topics {
    if (_topics is EqualUnmodifiableListView) return _topics;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_topics);
  }

  @override
  final String? intDeliveryMethod;
  @override
  final String? programLevel;
  @override
  final String courseExpiry;
  @override
  final String prerequisiteEducation;
  @override
  final String advancePreparation;
  @override
  final ActivePlan? activePlan;

  @override
  String toString() {
    return 'MasterclassDetails(id: $id, courseCategoryDetails: $courseCategoryDetails, instructorDetails: $instructorDetails, masterclassChapters: $masterclassChapters, addedBookmark: $addedBookmark, userFeedbackDetails: $userFeedbackDetails, totalDuration: $totalDuration, cpeModeDetails: $cpeModeDetails, userAssessmentDetails: $userAssessmentDetails, creditDetails: $creditDetails, updatedAt: $updatedAt, title: $title, thumbnail: $thumbnail, thumbnailGif: $thumbnailGif, courseType: $courseType, enableCoomingSoon: $enableCoomingSoon, courseOverview: $courseOverview, learningObjectives: $learningObjectives, examRules: $examRules, classCredits: $classCredits, documentFile: $documentFile, totalAssessmentQuestions: $totalAssessmentQuestions, passPercentage: $passPercentage, trailerLink: $trailerLink, sampleLink: $sampleLink, priorityOrder: $priorityOrder, courseCategory: $courseCategory, hostInstructor: $hostInstructor, allClassesCompleted: $allClassesCompleted, isAddedToCart: $isAddedToCart, isFree: $isFree, glossaryTranscriptText: $glossaryTranscriptText, navigationLink: $navigationLink, glossaryDoc: $glossaryDoc, courseCreatedDate: $courseCreatedDate, priceDetail: $priceDetail, horizontalThumbnail: $horizontalThumbnail, courseReviewedDate: $courseReviewedDate, courseUpdatedDate: $courseUpdatedDate, topics: $topics, intDeliveryMethod: $intDeliveryMethod, programLevel: $programLevel, courseExpiry: $courseExpiry, prerequisiteEducation: $prerequisiteEducation, advancePreparation: $advancePreparation, activePlan: $activePlan)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MasterclassDetailsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.courseCategoryDetails, courseCategoryDetails) ||
                other.courseCategoryDetails == courseCategoryDetails) &&
            (identical(other.instructorDetails, instructorDetails) ||
                other.instructorDetails == instructorDetails) &&
            const DeepCollectionEquality()
                .equals(other._masterclassChapters, _masterclassChapters) &&
            (identical(other.addedBookmark, addedBookmark) ||
                other.addedBookmark == addedBookmark) &&
            (identical(other.userFeedbackDetails, userFeedbackDetails) ||
                other.userFeedbackDetails == userFeedbackDetails) &&
            (identical(other.totalDuration, totalDuration) ||
                other.totalDuration == totalDuration) &&
            (identical(other.cpeModeDetails, cpeModeDetails) ||
                other.cpeModeDetails == cpeModeDetails) &&
            (identical(other.userAssessmentDetails, userAssessmentDetails) ||
                other.userAssessmentDetails == userAssessmentDetails) &&
            (identical(other.creditDetails, creditDetails) ||
                other.creditDetails == creditDetails) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.thumbnail, thumbnail) ||
                other.thumbnail == thumbnail) &&
            (identical(other.thumbnailGif, thumbnailGif) ||
                other.thumbnailGif == thumbnailGif) &&
            (identical(other.courseType, courseType) ||
                other.courseType == courseType) &&
            (identical(other.enableCoomingSoon, enableCoomingSoon) ||
                other.enableCoomingSoon == enableCoomingSoon) &&
            (identical(other.courseOverview, courseOverview) ||
                other.courseOverview == courseOverview) &&
            (identical(other.learningObjectives, learningObjectives) ||
                other.learningObjectives == learningObjectives) &&
            const DeepCollectionEquality().equals(other.examRules, examRules) &&
            (identical(other.classCredits, classCredits) ||
                other.classCredits == classCredits) &&
            const DeepCollectionEquality()
                .equals(other.documentFile, documentFile) &&
            (identical(other.totalAssessmentQuestions, totalAssessmentQuestions) ||
                other.totalAssessmentQuestions == totalAssessmentQuestions) &&
            (identical(other.passPercentage, passPercentage) ||
                other.passPercentage == passPercentage) &&
            (identical(other.trailerLink, trailerLink) ||
                other.trailerLink == trailerLink) &&
            (identical(other.sampleLink, sampleLink) ||
                other.sampleLink == sampleLink) &&
            (identical(other.priorityOrder, priorityOrder) ||
                other.priorityOrder == priorityOrder) &&
            (identical(other.courseCategory, courseCategory) ||
                other.courseCategory == courseCategory) &&
            (identical(other.hostInstructor, hostInstructor) ||
                other.hostInstructor == hostInstructor) &&
            (identical(other.allClassesCompleted, allClassesCompleted) ||
                other.allClassesCompleted == allClassesCompleted) &&
            (identical(other.isAddedToCart, isAddedToCart) ||
                other.isAddedToCart == isAddedToCart) &&
            (identical(other.isFree, isFree) || other.isFree == isFree) &&
            (identical(other.glossaryTranscriptText, glossaryTranscriptText) ||
                other.glossaryTranscriptText == glossaryTranscriptText) &&
            (identical(other.navigationLink, navigationLink) ||
                other.navigationLink == navigationLink) &&
            (identical(other.glossaryDoc, glossaryDoc) ||
                other.glossaryDoc == glossaryDoc) &&
            (identical(other.courseCreatedDate, courseCreatedDate) ||
                other.courseCreatedDate == courseCreatedDate) &&
            (identical(other.priceDetail, priceDetail) ||
                other.priceDetail == priceDetail) &&
            (identical(other.horizontalThumbnail, horizontalThumbnail) ||
                other.horizontalThumbnail == horizontalThumbnail) &&
            (identical(other.courseReviewedDate, courseReviewedDate) ||
                other.courseReviewedDate == courseReviewedDate) &&
            (identical(other.courseUpdatedDate, courseUpdatedDate) ||
                other.courseUpdatedDate == courseUpdatedDate) &&
            const DeepCollectionEquality().equals(other._topics, _topics) &&
            (identical(other.intDeliveryMethod, intDeliveryMethod) ||
                other.intDeliveryMethod == intDeliveryMethod) &&
            (identical(other.programLevel, programLevel) ||
                other.programLevel == programLevel) &&
            (identical(other.courseExpiry, courseExpiry) ||
                other.courseExpiry == courseExpiry) &&
            (identical(other.prerequisiteEducation, prerequisiteEducation) || other.prerequisiteEducation == prerequisiteEducation) &&
            (identical(other.advancePreparation, advancePreparation) || other.advancePreparation == advancePreparation) &&
            (identical(other.activePlan, activePlan) || other.activePlan == activePlan));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        courseCategoryDetails,
        instructorDetails,
        const DeepCollectionEquality().hash(_masterclassChapters),
        addedBookmark,
        userFeedbackDetails,
        totalDuration,
        cpeModeDetails,
        userAssessmentDetails,
        creditDetails,
        updatedAt,
        title,
        thumbnail,
        thumbnailGif,
        courseType,
        enableCoomingSoon,
        courseOverview,
        learningObjectives,
        const DeepCollectionEquality().hash(examRules),
        classCredits,
        const DeepCollectionEquality().hash(documentFile),
        totalAssessmentQuestions,
        passPercentage,
        trailerLink,
        sampleLink,
        priorityOrder,
        courseCategory,
        hostInstructor,
        allClassesCompleted,
        isAddedToCart,
        isFree,
        glossaryTranscriptText,
        navigationLink,
        glossaryDoc,
        courseCreatedDate,
        priceDetail,
        horizontalThumbnail,
        courseReviewedDate,
        courseUpdatedDate,
        const DeepCollectionEquality().hash(_topics),
        intDeliveryMethod,
        programLevel,
        courseExpiry,
        prerequisiteEducation,
        advancePreparation,
        activePlan
      ]);

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MasterclassDetailsImplCopyWith<_$MasterclassDetailsImpl> get copyWith =>
      __$$MasterclassDetailsImplCopyWithImpl<_$MasterclassDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MasterclassDetailsImplToJson(
      this,
    );
  }
}

abstract class _MasterclassDetails implements MasterclassDetails {
  const factory _MasterclassDetails(
      {required final int id,
      required final CourseCategoryDetails courseCategoryDetails,
      required final InstructorDetails instructorDetails,
      required final List<ChapterDetails> masterclassChapters,
      required final bool addedBookmark,
      required final UserFeedbackDetails userFeedbackDetails,
      required final int totalDuration,
      required final CPEModeDetails? cpeModeDetails,
      required final UserAssessmentDetails userAssessmentDetails,
      required final CreditDetails? creditDetails,
      required final DateTime updatedAt,
      required final String title,
      required final String? thumbnail,
      required final String? thumbnailGif,
      required final String courseType,
      required final bool enableCoomingSoon,
      required final String courseOverview,
      required final String? learningObjectives,
      required final dynamic examRules,
      required final double classCredits,
      required final dynamic documentFile,
      required final int totalAssessmentQuestions,
      required final int passPercentage,
      required final String? trailerLink,
      required final String? sampleLink,
      required final int priorityOrder,
      required final int courseCategory,
      required final int hostInstructor,
      required final bool allClassesCompleted,
      final bool? isAddedToCart,
      final bool? isFree,
      final String? glossaryTranscriptText,
      final String? navigationLink,
      final String? glossaryDoc,
      final DateTime? courseCreatedDate,
      final PriceDetail? priceDetail,
      final String? horizontalThumbnail,
      final DateTime? courseReviewedDate,
      final DateTime? courseUpdatedDate,
      required final List<String> topics,
      final String? intDeliveryMethod,
      final String? programLevel,
      required final String courseExpiry,
      required final String prerequisiteEducation,
      required final String advancePreparation,
      final ActivePlan? activePlan}) = _$MasterclassDetailsImpl;

  factory _MasterclassDetails.fromJson(Map<String, dynamic> json) =
      _$MasterclassDetailsImpl.fromJson;

  @override
  int get id;
  @override
  CourseCategoryDetails get courseCategoryDetails;
  @override
  InstructorDetails get instructorDetails;
  @override
  List<ChapterDetails> get masterclassChapters;
  @override
  bool get addedBookmark;
  @override
  UserFeedbackDetails get userFeedbackDetails;
  @override
  int get totalDuration;
  @override
  CPEModeDetails? get cpeModeDetails;
  @override
  UserAssessmentDetails get userAssessmentDetails;
  @override
  CreditDetails? get creditDetails;
  @override
  DateTime get updatedAt;
  @override
  String get title;
  @override
  String? get thumbnail;
  @override
  String? get thumbnailGif;
  @override
  String get courseType;
  @override
  bool get enableCoomingSoon;
  @override
  String get courseOverview;
  @override
  String? get learningObjectives;
  @override
  dynamic get examRules;
  @override
  double get classCredits;
  @override
  dynamic get documentFile;
  @override
  int get totalAssessmentQuestions;
  @override
  int get passPercentage;
  @override
  String? get trailerLink;
  @override
  String? get sampleLink;
  @override
  int get priorityOrder;
  @override
  int get courseCategory;
  @override
  int get hostInstructor;
  @override
  bool get allClassesCompleted;
  @override
  bool? get isAddedToCart;
  @override
  bool? get isFree;
  @override
  String? get glossaryTranscriptText;
  @override
  String? get navigationLink;
  @override
  String? get glossaryDoc;
  @override
  DateTime? get courseCreatedDate;
  @override
  PriceDetail? get priceDetail;
  @override
  String? get horizontalThumbnail;
  @override
  DateTime? get courseReviewedDate;
  @override
  DateTime? get courseUpdatedDate;
  @override
  List<String> get topics;
  @override
  String? get intDeliveryMethod;
  @override
  String? get programLevel;
  @override
  String get courseExpiry;
  @override
  String get prerequisiteEducation;
  @override
  String get advancePreparation;
  @override
  ActivePlan? get activePlan;

  /// Create a copy of MasterclassDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MasterclassDetailsImplCopyWith<_$MasterclassDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CreditDetails _$CreditDetailsFromJson(Map<String, dynamic> json) {
  return _CPECreditDetails.fromJson(json);
}

/// @nodoc
mixin _$CreditDetails {
  int get totalCredit => throw _privateConstructorUsedError;

  /// Serializes this CreditDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CreditDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CreditDetailsCopyWith<CreditDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CreditDetailsCopyWith<$Res> {
  factory $CreditDetailsCopyWith(
          CreditDetails value, $Res Function(CreditDetails) then) =
      _$CreditDetailsCopyWithImpl<$Res, CreditDetails>;
  @useResult
  $Res call({int totalCredit});
}

/// @nodoc
class _$CreditDetailsCopyWithImpl<$Res, $Val extends CreditDetails>
    implements $CreditDetailsCopyWith<$Res> {
  _$CreditDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CreditDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalCredit = null,
  }) {
    return _then(_value.copyWith(
      totalCredit: null == totalCredit
          ? _value.totalCredit
          : totalCredit // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CPECreditDetailsImplCopyWith<$Res>
    implements $CreditDetailsCopyWith<$Res> {
  factory _$$CPECreditDetailsImplCopyWith(_$CPECreditDetailsImpl value,
          $Res Function(_$CPECreditDetailsImpl) then) =
      __$$CPECreditDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int totalCredit});
}

/// @nodoc
class __$$CPECreditDetailsImplCopyWithImpl<$Res>
    extends _$CreditDetailsCopyWithImpl<$Res, _$CPECreditDetailsImpl>
    implements _$$CPECreditDetailsImplCopyWith<$Res> {
  __$$CPECreditDetailsImplCopyWithImpl(_$CPECreditDetailsImpl _value,
      $Res Function(_$CPECreditDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CreditDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalCredit = null,
  }) {
    return _then(_$CPECreditDetailsImpl(
      totalCredit: null == totalCredit
          ? _value.totalCredit
          : totalCredit // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$CPECreditDetailsImpl implements _CPECreditDetails {
  const _$CPECreditDetailsImpl({required this.totalCredit});

  factory _$CPECreditDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CPECreditDetailsImplFromJson(json);

  @override
  final int totalCredit;

  @override
  String toString() {
    return 'CreditDetails(totalCredit: $totalCredit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CPECreditDetailsImpl &&
            (identical(other.totalCredit, totalCredit) ||
                other.totalCredit == totalCredit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalCredit);

  /// Create a copy of CreditDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CPECreditDetailsImplCopyWith<_$CPECreditDetailsImpl> get copyWith =>
      __$$CPECreditDetailsImplCopyWithImpl<_$CPECreditDetailsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CPECreditDetailsImplToJson(
      this,
    );
  }
}

abstract class _CPECreditDetails implements CreditDetails {
  const factory _CPECreditDetails({required final int totalCredit}) =
      _$CPECreditDetailsImpl;

  factory _CPECreditDetails.fromJson(Map<String, dynamic> json) =
      _$CPECreditDetailsImpl.fromJson;

  @override
  int get totalCredit;

  /// Create a copy of CreditDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CPECreditDetailsImplCopyWith<_$CPECreditDetailsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PriceDetail _$PriceDetailFromJson(Map<String, dynamic> json) {
  return _PriceDetail.fromJson(json);
}

/// @nodoc
mixin _$PriceDetail {
  double get price => throw _privateConstructorUsedError;
  String get currencyCode => throw _privateConstructorUsedError;
  double get discount => throw _privateConstructorUsedError;
  String get currencySymbol => throw _privateConstructorUsedError;
  double get sellingPrice => throw _privateConstructorUsedError;

  /// Serializes this PriceDetail to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PriceDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PriceDetailCopyWith<PriceDetail> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PriceDetailCopyWith<$Res> {
  factory $PriceDetailCopyWith(
          PriceDetail value, $Res Function(PriceDetail) then) =
      _$PriceDetailCopyWithImpl<$Res, PriceDetail>;
  @useResult
  $Res call(
      {double price,
      String currencyCode,
      double discount,
      String currencySymbol,
      double sellingPrice});
}

/// @nodoc
class _$PriceDetailCopyWithImpl<$Res, $Val extends PriceDetail>
    implements $PriceDetailCopyWith<$Res> {
  _$PriceDetailCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PriceDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? price = null,
    Object? currencyCode = null,
    Object? discount = null,
    Object? currencySymbol = null,
    Object? sellingPrice = null,
  }) {
    return _then(_value.copyWith(
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      discount: null == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      sellingPrice: null == sellingPrice
          ? _value.sellingPrice
          : sellingPrice // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PriceDetailImplCopyWith<$Res>
    implements $PriceDetailCopyWith<$Res> {
  factory _$$PriceDetailImplCopyWith(
          _$PriceDetailImpl value, $Res Function(_$PriceDetailImpl) then) =
      __$$PriceDetailImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {double price,
      String currencyCode,
      double discount,
      String currencySymbol,
      double sellingPrice});
}

/// @nodoc
class __$$PriceDetailImplCopyWithImpl<$Res>
    extends _$PriceDetailCopyWithImpl<$Res, _$PriceDetailImpl>
    implements _$$PriceDetailImplCopyWith<$Res> {
  __$$PriceDetailImplCopyWithImpl(
      _$PriceDetailImpl _value, $Res Function(_$PriceDetailImpl) _then)
      : super(_value, _then);

  /// Create a copy of PriceDetail
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? price = null,
    Object? currencyCode = null,
    Object? discount = null,
    Object? currencySymbol = null,
    Object? sellingPrice = null,
  }) {
    return _then(_$PriceDetailImpl(
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      discount: null == discount
          ? _value.discount
          : discount // ignore: cast_nullable_to_non_nullable
              as double,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      sellingPrice: null == sellingPrice
          ? _value.sellingPrice
          : sellingPrice // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$PriceDetailImpl implements _PriceDetail {
  const _$PriceDetailImpl(
      {required this.price,
      required this.currencyCode,
      required this.discount,
      required this.currencySymbol,
      required this.sellingPrice});

  factory _$PriceDetailImpl.fromJson(Map<String, dynamic> json) =>
      _$$PriceDetailImplFromJson(json);

  @override
  final double price;
  @override
  final String currencyCode;
  @override
  final double discount;
  @override
  final String currencySymbol;
  @override
  final double sellingPrice;

  @override
  String toString() {
    return 'PriceDetail(price: $price, currencyCode: $currencyCode, discount: $discount, currencySymbol: $currencySymbol, sellingPrice: $sellingPrice)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriceDetailImpl &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.discount, discount) ||
                other.discount == discount) &&
            (identical(other.currencySymbol, currencySymbol) ||
                other.currencySymbol == currencySymbol) &&
            (identical(other.sellingPrice, sellingPrice) ||
                other.sellingPrice == sellingPrice));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, price, currencyCode, discount, currencySymbol, sellingPrice);

  /// Create a copy of PriceDetail
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PriceDetailImplCopyWith<_$PriceDetailImpl> get copyWith =>
      __$$PriceDetailImplCopyWithImpl<_$PriceDetailImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PriceDetailImplToJson(
      this,
    );
  }
}

abstract class _PriceDetail implements PriceDetail {
  const factory _PriceDetail(
      {required final double price,
      required final String currencyCode,
      required final double discount,
      required final String currencySymbol,
      required final double sellingPrice}) = _$PriceDetailImpl;

  factory _PriceDetail.fromJson(Map<String, dynamic> json) =
      _$PriceDetailImpl.fromJson;

  @override
  double get price;
  @override
  String get currencyCode;
  @override
  double get discount;
  @override
  String get currencySymbol;
  @override
  double get sellingPrice;

  /// Create a copy of PriceDetail
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PriceDetailImplCopyWith<_$PriceDetailImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
