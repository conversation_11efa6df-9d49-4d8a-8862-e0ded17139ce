// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recently_viewed_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecentlyViewedDataImpl _$$RecentlyViewedDataImplFromJson(
        Map<String, dynamic> json) =>
    _$RecentlyViewedDataImpl(
      id: (json['id'] as num).toInt(),
      masterclassDetails: MasterclassSummary.fromJson(
          json['masterclass_details'] as Map<String, dynamic>),
      chapterDetails: ChapterDetails.fromJson(
          json['chapter_details'] as Map<String, dynamic>),
      totalDuration: (json['total_duration'] as num).toInt(),
      totalDurationWatched: (json['total_duration_watched'] as num).toInt(),
      chapterStatus: ChapterStatus.fromJson(
          json['chapter_status'] as Map<String, dynamic>),
      timeStatus: (json['time_status'] as num).toInt(),
      masterClass: (json['master_class'] as num).toInt(),
      chapter: (json['chapter'] as num).toInt(),
      user: (json['user'] as num).toInt(),
    );

Map<String, dynamic> _$$RecentlyViewedDataImplToJson(
        _$RecentlyViewedDataImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'masterclass_details': instance.masterclassDetails,
      'chapter_details': instance.chapterDetails,
      'total_duration': instance.totalDuration,
      'total_duration_watched': instance.totalDurationWatched,
      'chapter_status': instance.chapterStatus,
      'time_status': instance.timeStatus,
      'master_class': instance.masterClass,
      'chapter': instance.chapter,
      'user': instance.user,
    };

_$ChapterStatusImpl _$$ChapterStatusImplFromJson(Map<String, dynamic> json) =>
    _$ChapterStatusImpl(
      status: json['status'] as bool,
    );

Map<String, dynamic> _$$ChapterStatusImplToJson(_$ChapterStatusImpl instance) =>
    <String, dynamic>{
      'status': instance.status,
    };
