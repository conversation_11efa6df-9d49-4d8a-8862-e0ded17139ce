// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recently_viewed_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecentlyViewedData _$RecentlyViewedDataFromJson(Map<String, dynamic> json) {
  return _RecentlyViewedData.fromJson(json);
}

/// @nodoc
mixin _$RecentlyViewedData {
  int get id => throw _privateConstructorUsedError;
  MasterclassSummary get masterclassDetails =>
      throw _privateConstructorUsedError;
  ChapterDetails get chapterDetails => throw _privateConstructorUsedError;
  int get totalDuration => throw _privateConstructorUsedError;
  int get totalDurationWatched => throw _privateConstructorUsedError;
  ChapterStatus get chapterStatus => throw _privateConstructorUsedError;
  int get timeStatus => throw _privateConstructorUsedError;
  int get masterClass => throw _privateConstructorUsedError;
  int get chapter => throw _privateConstructorUsedError;
  int get user => throw _privateConstructorUsedError;

  /// Serializes this RecentlyViewedData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentlyViewedDataCopyWith<RecentlyViewedData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentlyViewedDataCopyWith<$Res> {
  factory $RecentlyViewedDataCopyWith(
          RecentlyViewedData value, $Res Function(RecentlyViewedData) then) =
      _$RecentlyViewedDataCopyWithImpl<$Res, RecentlyViewedData>;
  @useResult
  $Res call(
      {int id,
      MasterclassSummary masterclassDetails,
      ChapterDetails chapterDetails,
      int totalDuration,
      int totalDurationWatched,
      ChapterStatus chapterStatus,
      int timeStatus,
      int masterClass,
      int chapter,
      int user});

  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
  $ChapterDetailsCopyWith<$Res> get chapterDetails;
  $ChapterStatusCopyWith<$Res> get chapterStatus;
}

/// @nodoc
class _$RecentlyViewedDataCopyWithImpl<$Res, $Val extends RecentlyViewedData>
    implements $RecentlyViewedDataCopyWith<$Res> {
  _$RecentlyViewedDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? masterclassDetails = null,
    Object? chapterDetails = null,
    Object? totalDuration = null,
    Object? totalDurationWatched = null,
    Object? chapterStatus = null,
    Object? timeStatus = null,
    Object? masterClass = null,
    Object? chapter = null,
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      chapterDetails: null == chapterDetails
          ? _value.chapterDetails
          : chapterDetails // ignore: cast_nullable_to_non_nullable
              as ChapterDetails,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as int,
      totalDurationWatched: null == totalDurationWatched
          ? _value.totalDurationWatched
          : totalDurationWatched // ignore: cast_nullable_to_non_nullable
              as int,
      chapterStatus: null == chapterStatus
          ? _value.chapterStatus
          : chapterStatus // ignore: cast_nullable_to_non_nullable
              as ChapterStatus,
      timeStatus: null == timeStatus
          ? _value.timeStatus
          : timeStatus // ignore: cast_nullable_to_non_nullable
              as int,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      chapter: null == chapter
          ? _value.chapter
          : chapter // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails {
    return $MasterclassSummaryCopyWith<$Res>(_value.masterclassDetails,
        (value) {
      return _then(_value.copyWith(masterclassDetails: value) as $Val);
    });
  }

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChapterDetailsCopyWith<$Res> get chapterDetails {
    return $ChapterDetailsCopyWith<$Res>(_value.chapterDetails, (value) {
      return _then(_value.copyWith(chapterDetails: value) as $Val);
    });
  }

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChapterStatusCopyWith<$Res> get chapterStatus {
    return $ChapterStatusCopyWith<$Res>(_value.chapterStatus, (value) {
      return _then(_value.copyWith(chapterStatus: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecentlyViewedDataImplCopyWith<$Res>
    implements $RecentlyViewedDataCopyWith<$Res> {
  factory _$$RecentlyViewedDataImplCopyWith(_$RecentlyViewedDataImpl value,
          $Res Function(_$RecentlyViewedDataImpl) then) =
      __$$RecentlyViewedDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      MasterclassSummary masterclassDetails,
      ChapterDetails chapterDetails,
      int totalDuration,
      int totalDurationWatched,
      ChapterStatus chapterStatus,
      int timeStatus,
      int masterClass,
      int chapter,
      int user});

  @override
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
  @override
  $ChapterDetailsCopyWith<$Res> get chapterDetails;
  @override
  $ChapterStatusCopyWith<$Res> get chapterStatus;
}

/// @nodoc
class __$$RecentlyViewedDataImplCopyWithImpl<$Res>
    extends _$RecentlyViewedDataCopyWithImpl<$Res, _$RecentlyViewedDataImpl>
    implements _$$RecentlyViewedDataImplCopyWith<$Res> {
  __$$RecentlyViewedDataImplCopyWithImpl(_$RecentlyViewedDataImpl _value,
      $Res Function(_$RecentlyViewedDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? masterclassDetails = null,
    Object? chapterDetails = null,
    Object? totalDuration = null,
    Object? totalDurationWatched = null,
    Object? chapterStatus = null,
    Object? timeStatus = null,
    Object? masterClass = null,
    Object? chapter = null,
    Object? user = null,
  }) {
    return _then(_$RecentlyViewedDataImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      chapterDetails: null == chapterDetails
          ? _value.chapterDetails
          : chapterDetails // ignore: cast_nullable_to_non_nullable
              as ChapterDetails,
      totalDuration: null == totalDuration
          ? _value.totalDuration
          : totalDuration // ignore: cast_nullable_to_non_nullable
              as int,
      totalDurationWatched: null == totalDurationWatched
          ? _value.totalDurationWatched
          : totalDurationWatched // ignore: cast_nullable_to_non_nullable
              as int,
      chapterStatus: null == chapterStatus
          ? _value.chapterStatus
          : chapterStatus // ignore: cast_nullable_to_non_nullable
              as ChapterStatus,
      timeStatus: null == timeStatus
          ? _value.timeStatus
          : timeStatus // ignore: cast_nullable_to_non_nullable
              as int,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      chapter: null == chapter
          ? _value.chapter
          : chapter // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$RecentlyViewedDataImpl implements _RecentlyViewedData {
  const _$RecentlyViewedDataImpl(
      {required this.id,
      required this.masterclassDetails,
      required this.chapterDetails,
      required this.totalDuration,
      required this.totalDurationWatched,
      required this.chapterStatus,
      required this.timeStatus,
      required this.masterClass,
      required this.chapter,
      required this.user});

  factory _$RecentlyViewedDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecentlyViewedDataImplFromJson(json);

  @override
  final int id;
  @override
  final MasterclassSummary masterclassDetails;
  @override
  final ChapterDetails chapterDetails;
  @override
  final int totalDuration;
  @override
  final int totalDurationWatched;
  @override
  final ChapterStatus chapterStatus;
  @override
  final int timeStatus;
  @override
  final int masterClass;
  @override
  final int chapter;
  @override
  final int user;

  @override
  String toString() {
    return 'RecentlyViewedData(id: $id, masterclassDetails: $masterclassDetails, chapterDetails: $chapterDetails, totalDuration: $totalDuration, totalDurationWatched: $totalDurationWatched, chapterStatus: $chapterStatus, timeStatus: $timeStatus, masterClass: $masterClass, chapter: $chapter, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentlyViewedDataImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.masterclassDetails, masterclassDetails) ||
                other.masterclassDetails == masterclassDetails) &&
            (identical(other.chapterDetails, chapterDetails) ||
                other.chapterDetails == chapterDetails) &&
            (identical(other.totalDuration, totalDuration) ||
                other.totalDuration == totalDuration) &&
            (identical(other.totalDurationWatched, totalDurationWatched) ||
                other.totalDurationWatched == totalDurationWatched) &&
            (identical(other.chapterStatus, chapterStatus) ||
                other.chapterStatus == chapterStatus) &&
            (identical(other.timeStatus, timeStatus) ||
                other.timeStatus == timeStatus) &&
            (identical(other.masterClass, masterClass) ||
                other.masterClass == masterClass) &&
            (identical(other.chapter, chapter) || other.chapter == chapter) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      masterclassDetails,
      chapterDetails,
      totalDuration,
      totalDurationWatched,
      chapterStatus,
      timeStatus,
      masterClass,
      chapter,
      user);

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentlyViewedDataImplCopyWith<_$RecentlyViewedDataImpl> get copyWith =>
      __$$RecentlyViewedDataImplCopyWithImpl<_$RecentlyViewedDataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentlyViewedDataImplToJson(
      this,
    );
  }
}

abstract class _RecentlyViewedData implements RecentlyViewedData {
  const factory _RecentlyViewedData(
      {required final int id,
      required final MasterclassSummary masterclassDetails,
      required final ChapterDetails chapterDetails,
      required final int totalDuration,
      required final int totalDurationWatched,
      required final ChapterStatus chapterStatus,
      required final int timeStatus,
      required final int masterClass,
      required final int chapter,
      required final int user}) = _$RecentlyViewedDataImpl;

  factory _RecentlyViewedData.fromJson(Map<String, dynamic> json) =
      _$RecentlyViewedDataImpl.fromJson;

  @override
  int get id;
  @override
  MasterclassSummary get masterclassDetails;
  @override
  ChapterDetails get chapterDetails;
  @override
  int get totalDuration;
  @override
  int get totalDurationWatched;
  @override
  ChapterStatus get chapterStatus;
  @override
  int get timeStatus;
  @override
  int get masterClass;
  @override
  int get chapter;
  @override
  int get user;

  /// Create a copy of RecentlyViewedData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentlyViewedDataImplCopyWith<_$RecentlyViewedDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChapterStatus _$ChapterStatusFromJson(Map<String, dynamic> json) {
  return _ChapterStatus.fromJson(json);
}

/// @nodoc
mixin _$ChapterStatus {
  bool get status => throw _privateConstructorUsedError;

  /// Serializes this ChapterStatus to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChapterStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChapterStatusCopyWith<ChapterStatus> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChapterStatusCopyWith<$Res> {
  factory $ChapterStatusCopyWith(
          ChapterStatus value, $Res Function(ChapterStatus) then) =
      _$ChapterStatusCopyWithImpl<$Res, ChapterStatus>;
  @useResult
  $Res call({bool status});
}

/// @nodoc
class _$ChapterStatusCopyWithImpl<$Res, $Val extends ChapterStatus>
    implements $ChapterStatusCopyWith<$Res> {
  _$ChapterStatusCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChapterStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChapterStatusImplCopyWith<$Res>
    implements $ChapterStatusCopyWith<$Res> {
  factory _$$ChapterStatusImplCopyWith(
          _$ChapterStatusImpl value, $Res Function(_$ChapterStatusImpl) then) =
      __$$ChapterStatusImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool status});
}

/// @nodoc
class __$$ChapterStatusImplCopyWithImpl<$Res>
    extends _$ChapterStatusCopyWithImpl<$Res, _$ChapterStatusImpl>
    implements _$$ChapterStatusImplCopyWith<$Res> {
  __$$ChapterStatusImplCopyWithImpl(
      _$ChapterStatusImpl _value, $Res Function(_$ChapterStatusImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChapterStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
  }) {
    return _then(_$ChapterStatusImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$ChapterStatusImpl implements _ChapterStatus {
  const _$ChapterStatusImpl({required this.status});

  factory _$ChapterStatusImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChapterStatusImplFromJson(json);

  @override
  final bool status;

  @override
  String toString() {
    return 'ChapterStatus(status: $status)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChapterStatusImpl &&
            (identical(other.status, status) || other.status == status));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, status);

  /// Create a copy of ChapterStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChapterStatusImplCopyWith<_$ChapterStatusImpl> get copyWith =>
      __$$ChapterStatusImplCopyWithImpl<_$ChapterStatusImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChapterStatusImplToJson(
      this,
    );
  }
}

abstract class _ChapterStatus implements ChapterStatus {
  const factory _ChapterStatus({required final bool status}) =
      _$ChapterStatusImpl;

  factory _ChapterStatus.fromJson(Map<String, dynamic> json) =
      _$ChapterStatusImpl.fromJson;

  @override
  bool get status;

  /// Create a copy of ChapterStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChapterStatusImplCopyWith<_$ChapterStatusImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
