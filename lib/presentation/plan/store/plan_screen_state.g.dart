// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$PlanScreenState on _PlanScreenState, Store {
  Computed<Subscription?>? _$activeSubscriptionComputed;

  @override
  Subscription? get activeSubscription => (_$activeSubscriptionComputed ??=
          Computed<Subscription?>(() => super.activeSubscription,
              name: '_PlanScreenState.activeSubscription'))
      .value;

  late final _$_appUserAtom =
      Atom(name: '_PlanScreenState._appUser', context: context);

  AppUser? get appUser {
    _$_appUserAtom.reportRead();
    return super._appUser;
  }

  @override
  AppUser? get _appUser => appUser;

  @override
  set _appUser(AppUser? value) {
    _$_appUserAtom.reportWrite(value, super._appUser, () {
      super._appUser = value;
    });
  }

  late final _$fetchListOfSubscriptionStateAtom = Atom(
      name: '_PlanScreenState.fetchListOfSubscriptionState', context: context);

  @override
  DataState get fetchListOfSubscriptionState {
    _$fetchListOfSubscriptionStateAtom.reportRead();
    return super.fetchListOfSubscriptionState;
  }

  @override
  set fetchListOfSubscriptionState(DataState value) {
    _$fetchListOfSubscriptionStateAtom
        .reportWrite(value, super.fetchListOfSubscriptionState, () {
      super.fetchListOfSubscriptionState = value;
    });
  }

  late final _$numberOfDaysRemainingAtom =
      Atom(name: '_PlanScreenState.numberOfDaysRemaining', context: context);

  @override
  int? get numberOfDaysRemaining {
    _$numberOfDaysRemainingAtom.reportRead();
    return super.numberOfDaysRemaining;
  }

  @override
  set numberOfDaysRemaining(int? value) {
    _$numberOfDaysRemainingAtom.reportWrite(value, super.numberOfDaysRemaining,
        () {
      super.numberOfDaysRemaining = value;
    });
  }

  late final _$trialDaysRemainingAtom =
      Atom(name: '_PlanScreenState.trialDaysRemaining', context: context);

  @override
  int? get trialDaysRemaining {
    _$trialDaysRemainingAtom.reportRead();
    return super.trialDaysRemaining;
  }

  @override
  set trialDaysRemaining(int? value) {
    _$trialDaysRemainingAtom.reportWrite(value, super.trialDaysRemaining, () {
      super.trialDaysRemaining = value;
    });
  }

  late final _$fetchListOfIapProductsAtom =
      Atom(name: '_PlanScreenState.fetchListOfIapProducts', context: context);

  @override
  DataState get fetchListOfIapProducts {
    _$fetchListOfIapProductsAtom.reportRead();
    return super.fetchListOfIapProducts;
  }

  @override
  set fetchListOfIapProducts(DataState value) {
    _$fetchListOfIapProductsAtom
        .reportWrite(value, super.fetchListOfIapProducts, () {
      super.fetchListOfIapProducts = value;
    });
  }

  late final _$iapApiCallStateAtom =
      Atom(name: '_PlanScreenState.iapApiCallState', context: context);

  @override
  DataState get iapApiCallState {
    _$iapApiCallStateAtom.reportRead();
    return super.iapApiCallState;
  }

  @override
  set iapApiCallState(DataState value) {
    _$iapApiCallStateAtom.reportWrite(value, super.iapApiCallState, () {
      super.iapApiCallState = value;
    });
  }

  late final _$_subscriptionsFromBackendAtom = Atom(
      name: '_PlanScreenState._subscriptionsFromBackend', context: context);

  List<Subscription>? get subscriptionsFromBackend {
    _$_subscriptionsFromBackendAtom.reportRead();
    return super._subscriptionsFromBackend;
  }

  @override
  List<Subscription>? get _subscriptionsFromBackend => subscriptionsFromBackend;

  @override
  set _subscriptionsFromBackend(List<Subscription>? value) {
    _$_subscriptionsFromBackendAtom
        .reportWrite(value, super._subscriptionsFromBackend, () {
      super._subscriptionsFromBackend = value;
    });
  }

  late final _$isPurchasedAtom =
      Atom(name: '_PlanScreenState.isPurchased', context: context);

  @override
  bool? get isPurchased {
    _$isPurchasedAtom.reportRead();
    return super.isPurchased;
  }

  @override
  set isPurchased(bool? value) {
    _$isPurchasedAtom.reportWrite(value, super.isPurchased, () {
      super.isPurchased = value;
    });
  }

  late final _$productsAtom =
      Atom(name: '_PlanScreenState.products', context: context);

  @override
  ObservableList<ProductDetails> get products {
    _$productsAtom.reportRead();
    return super.products;
  }

  @override
  set products(ObservableList<ProductDetails> value) {
    _$productsAtom.reportWrite(value, super.products, () {
      super.products = value;
    });
  }

  late final _$purchasesAtom =
      Atom(name: '_PlanScreenState.purchases', context: context);

  @override
  ObservableList<PurchaseDetails> get purchases {
    _$purchasesAtom.reportRead();
    return super.purchases;
  }

  @override
  set purchases(ObservableList<PurchaseDetails> value) {
    _$purchasesAtom.reportWrite(value, super.purchases, () {
      super.purchases = value;
    });
  }

  late final _$isLoadingAtom =
      Atom(name: '_PlanScreenState.isLoading', context: context);

  @override
  bool get isLoading {
    _$isLoadingAtom.reportRead();
    return super.isLoading;
  }

  @override
  set isLoading(bool value) {
    _$isLoadingAtom.reportWrite(value, super.isLoading, () {
      super.isLoading = value;
    });
  }

  late final _$_purchaseStatusAtom =
      Atom(name: '_PlanScreenState._purchaseStatus', context: context);

  PurchaseStatus? get purchaseStatus {
    _$_purchaseStatusAtom.reportRead();
    return super._purchaseStatus;
  }

  @override
  PurchaseStatus? get _purchaseStatus => purchaseStatus;

  @override
  set _purchaseStatus(PurchaseStatus? value) {
    _$_purchaseStatusAtom.reportWrite(value, super._purchaseStatus, () {
      super._purchaseStatus = value;
    });
  }

  late final _$_pendingPurchaseAtom =
      Atom(name: '_PlanScreenState._pendingPurchase', context: context);

  PurchaseDetails? get pendingPurchase {
    _$_pendingPurchaseAtom.reportRead();
    return super._pendingPurchase;
  }

  @override
  PurchaseDetails? get _pendingPurchase => pendingPurchase;

  @override
  set _pendingPurchase(PurchaseDetails? value) {
    _$_pendingPurchaseAtom.reportWrite(value, super._pendingPurchase, () {
      super._pendingPurchase = value;
    });
  }

  late final _$errorMessageAtom =
      Atom(name: '_PlanScreenState.errorMessage', context: context);

  @override
  String get errorMessage {
    _$errorMessageAtom.reportRead();
    return super.errorMessage;
  }

  @override
  set errorMessage(String value) {
    _$errorMessageAtom.reportWrite(value, super.errorMessage, () {
      super.errorMessage = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_PlanScreenState.init', context: context);

  @override
  Future<void> init() {
    return _$initAsyncAction.run(() => super.init());
  }

  late final _$_fetchUserProfileAsyncAction =
      AsyncAction('_PlanScreenState._fetchUserProfile', context: context);

  @override
  Future<void> _fetchUserProfile() {
    return _$_fetchUserProfileAsyncAction.run(() => super._fetchUserProfile());
  }

  late final _$_fetchListOfSubscriptionAsyncAction = AsyncAction(
      '_PlanScreenState._fetchListOfSubscription',
      context: context);

  @override
  Future<void> _fetchListOfSubscription() {
    return _$_fetchListOfSubscriptionAsyncAction
        .run(() => super._fetchListOfSubscription());
  }

  late final _$_iapSuccessfulAsyncAction =
      AsyncAction('_PlanScreenState._iapSuccessful', context: context);

  @override
  Future<void> _iapSuccessful(IapRequest iapRequest) {
    return _$_iapSuccessfulAsyncAction
        .run(() => super._iapSuccessful(iapRequest));
  }

  late final _$_iapSuccessfulAndroidAsyncAction =
      AsyncAction('_PlanScreenState._iapSuccessfulAndroid', context: context);

  @override
  Future<void> _iapSuccessfulAndroid(IapRequestAndroid iapRequestAndroid) {
    return _$_iapSuccessfulAndroidAsyncAction
        .run(() => super._iapSuccessfulAndroid(iapRequestAndroid));
  }

  late final _$_setListOfSubscriptionAsyncAction =
      AsyncAction('_PlanScreenState._setListOfSubscription', context: context);

  @override
  Future<void> _setListOfSubscription(List<Subscription> subscriptions) {
    return _$_setListOfSubscriptionAsyncAction
        .run(() => super._setListOfSubscription(subscriptions));
  }

  late final _$initStoreAsyncAction =
      AsyncAction('_PlanScreenState.initStore', context: context);

  @override
  Future<void> initStore() {
    return _$initStoreAsyncAction.run(() => super.initStore());
  }

  late final _$loadProductsAsyncAction =
      AsyncAction('_PlanScreenState.loadProducts', context: context);

  @override
  Future<void> loadProducts() {
    return _$loadProductsAsyncAction.run(() => super.loadProducts());
  }

  late final _$buyProductAsyncAction =
      AsyncAction('_PlanScreenState.buyProduct', context: context);

  @override
  Future<void> buyProduct(ProductDetails product) {
    return _$buyProductAsyncAction.run(() => super.buyProduct(product));
  }

  late final _$verifyExistingPurchasesAsyncAction =
      AsyncAction('_PlanScreenState.verifyExistingPurchases', context: context);

  @override
  Future<void> verifyExistingPurchases() {
    return _$verifyExistingPurchasesAsyncAction
        .run(() => super.verifyExistingPurchases());
  }

  late final _$_PlanScreenStateActionController =
      ActionController(name: '_PlanScreenState', context: context);

  @override
  void _setAppUser(AppUser? appUser) {
    final _$actionInfo = _$_PlanScreenStateActionController.startAction(
        name: '_PlanScreenState._setAppUser');
    try {
      return super._setAppUser(appUser);
    } finally {
      _$_PlanScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setIsPurchasedFromSubscription(Subscription? subscription) {
    final _$actionInfo = _$_PlanScreenStateActionController.startAction(
        name: '_PlanScreenState._setIsPurchasedFromSubscription');
    try {
      return super._setIsPurchasedFromSubscription(subscription);
    } finally {
      _$_PlanScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setIsPurchasedAfterPurchase(IapResponse? iapResponse) {
    final _$actionInfo = _$_PlanScreenStateActionController.startAction(
        name: '_PlanScreenState._setIsPurchasedAfterPurchase');
    try {
      return super._setIsPurchasedAfterPurchase(iapResponse);
    } finally {
      _$_PlanScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic _setTrialDurationForIosRequest(ProductDetails productDetails) {
    final _$actionInfo = _$_PlanScreenStateActionController.startAction(
        name: '_PlanScreenState._setTrialDurationForIosRequest');
    try {
      return super._setTrialDurationForIosRequest(productDetails);
    } finally {
      _$_PlanScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic _setTrialDurationForAndroidRequest(ProductDetails productDetails) {
    final _$actionInfo = _$_PlanScreenStateActionController.startAction(
        name: '_PlanScreenState._setTrialDurationForAndroidRequest');
    try {
      return super._setTrialDurationForAndroidRequest(productDetails);
    } finally {
      _$_PlanScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
fetchListOfSubscriptionState: ${fetchListOfSubscriptionState},
numberOfDaysRemaining: ${numberOfDaysRemaining},
trialDaysRemaining: ${trialDaysRemaining},
fetchListOfIapProducts: ${fetchListOfIapProducts},
iapApiCallState: ${iapApiCallState},
isPurchased: ${isPurchased},
products: ${products},
purchases: ${purchases},
isLoading: ${isLoading},
errorMessage: ${errorMessage},
activeSubscription: ${activeSubscription}
    ''';
  }
}
