// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_cart_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$MyCartScreenState on _MyCartScreenState, Store {
  Computed<Cart?>? _$cartDetailsComputed;

  @override
  Cart? get cartDetails =>
      (_$cartDetailsComputed ??= Computed<Cart?>(() => super.cartDetails,
              name: '_MyCartScreenState.cartDetails'))
          .value;
  Computed<bool>? _$isCouponAppliedComputed;

  @override
  bool get isCouponApplied =>
      (_$isCouponAppliedComputed ??= Computed<bool>(() => super.isCouponApplied,
              name: '_MyCartScreenState.isCouponApplied'))
          .value;
  Computed<AppliedCoupon?>? _$appliedCouponComputed;

  @override
  AppliedCoupon? get appliedCoupon => (_$appliedCouponComputed ??=
          Computed<AppliedCoupon?>(() => super.appliedCoupon,
              name: '_MyCartScreenState.appliedCoupon'))
      .value;
  Computed<CartItem?>? _$addedSubscriptionItemComputed;

  @override
  CartItem? get addedSubscriptionItem => (_$addedSubscriptionItemComputed ??=
          Computed<CartItem?>(() => super.addedSubscriptionItem,
              name: '_MyCartScreenState.addedSubscriptionItem'))
      .value;
  Computed<bool?>? _$isMasterClassItemsAddedComputed;

  @override
  bool? get isMasterClassItemsAdded => (_$isMasterClassItemsAddedComputed ??=
          Computed<bool?>(() => super.isMasterClassItemsAdded,
              name: '_MyCartScreenState.isMasterClassItemsAdded'))
      .value;
  Computed<bool?>? _$isSubscriptionAddedComputed;

  @override
  bool? get isSubscriptionAdded => (_$isSubscriptionAddedComputed ??=
          Computed<bool?>(() => super.isSubscriptionAdded,
              name: '_MyCartScreenState.isSubscriptionAdded'))
      .value;
  Computed<CheckoutCardData>? _$checkoutCartDataComputed;

  @override
  CheckoutCardData get checkoutCartData => (_$checkoutCartDataComputed ??=
          Computed<CheckoutCardData>(() => super.checkoutCartData,
              name: '_MyCartScreenState.checkoutCartData'))
      .value;
  Computed<bool?>? _$isCartEmptyComputed;

  @override
  bool? get isCartEmpty =>
      (_$isCartEmptyComputed ??= Computed<bool?>(() => super.isCartEmpty,
              name: '_MyCartScreenState.isCartEmpty'))
          .value;
  Computed<List<AddedItemToCartData>?>? _$getAddedItemToCartDataListComputed;

  @override
  List<AddedItemToCartData>? get getAddedItemToCartDataList =>
      (_$getAddedItemToCartDataListComputed ??=
              Computed<List<AddedItemToCartData>?>(
                  () => super.getAddedItemToCartDataList,
                  name: '_MyCartScreenState.getAddedItemToCartDataList'))
          .value;
  Computed<Subscription?>? _$activeSubscriptionComputed;

  @override
  Subscription? get activeSubscription => (_$activeSubscriptionComputed ??=
          Computed<Subscription?>(() => super.activeSubscription,
              name: '_MyCartScreenState.activeSubscription'))
      .value;
  Computed<bool?>? _$isAllAddressDeletedComputed;

  @override
  bool? get isAllAddressDeleted => (_$isAllAddressDeletedComputed ??=
          Computed<bool?>(() => super.isAllAddressDeleted,
              name: '_MyCartScreenState.isAllAddressDeleted'))
      .value;
  Computed<String?>? _$fullAddressComputed;

  @override
  String? get fullAddress =>
      (_$fullAddressComputed ??= Computed<String?>(() => super.fullAddress,
              name: '_MyCartScreenState.fullAddress'))
          .value;
  Computed<List<BillingAddressCardData>?>?
      _$getListOfBillingAddressCardDataComputed;

  @override
  List<BillingAddressCardData>? get getListOfBillingAddressCardData =>
      (_$getListOfBillingAddressCardDataComputed ??=
              Computed<List<BillingAddressCardData>?>(
                  () => super.getListOfBillingAddressCardData,
                  name: '_MyCartScreenState.getListOfBillingAddressCardData'))
          .value;

  late final _$_appUserAtom =
      Atom(name: '_MyCartScreenState._appUser', context: context);

  AppUser? get appUser {
    _$_appUserAtom.reportRead();
    return super._appUser;
  }

  @override
  AppUser? get _appUser => appUser;

  @override
  set _appUser(AppUser? value) {
    _$_appUserAtom.reportWrite(value, super._appUser, () {
      super._appUser = value;
    });
  }

  late final _$cartDetailsStateAtom =
      Atom(name: '_MyCartScreenState.cartDetailsState', context: context);

  @override
  DataState get cartDetailsState {
    _$cartDetailsStateAtom.reportRead();
    return super.cartDetailsState;
  }

  @override
  set cartDetailsState(DataState value) {
    _$cartDetailsStateAtom.reportWrite(value, super.cartDetailsState, () {
      super.cartDetailsState = value;
    });
  }

  late final _$_cartAtom =
      Atom(name: '_MyCartScreenState._cart', context: context);

  Cart? get cart {
    _$_cartAtom.reportRead();
    return super._cart;
  }

  @override
  Cart? get _cart => cart;

  @override
  set _cart(Cart? value) {
    _$_cartAtom.reportWrite(value, super._cart, () {
      super._cart = value;
    });
  }

  late final _$subscriptionAddRemoveActionStateAtom = Atom(
      name: '_MyCartScreenState.subscriptionAddRemoveActionState',
      context: context);

  @override
  DataState get subscriptionAddRemoveActionState {
    _$subscriptionAddRemoveActionStateAtom.reportRead();
    return super.subscriptionAddRemoveActionState;
  }

  @override
  set subscriptionAddRemoveActionState(DataState value) {
    _$subscriptionAddRemoveActionStateAtom
        .reportWrite(value, super.subscriptionAddRemoveActionState, () {
      super.subscriptionAddRemoveActionState = value;
    });
  }

  late final _$fetchListOfSubscriptionStateAtom = Atom(
      name: '_MyCartScreenState.fetchListOfSubscriptionState',
      context: context);

  @override
  DataState get fetchListOfSubscriptionState {
    _$fetchListOfSubscriptionStateAtom.reportRead();
    return super.fetchListOfSubscriptionState;
  }

  @override
  set fetchListOfSubscriptionState(DataState value) {
    _$fetchListOfSubscriptionStateAtom
        .reportWrite(value, super.fetchListOfSubscriptionState, () {
      super.fetchListOfSubscriptionState = value;
    });
  }

  late final _$_subscriptionsAtom =
      Atom(name: '_MyCartScreenState._subscriptions', context: context);

  List<Subscription>? get subscriptions {
    _$_subscriptionsAtom.reportRead();
    return super._subscriptions;
  }

  @override
  List<Subscription>? get _subscriptions => subscriptions;

  @override
  set _subscriptions(List<Subscription>? value) {
    _$_subscriptionsAtom.reportWrite(value, super._subscriptions, () {
      super._subscriptions = value;
    });
  }

  late final _$fetchListOfAddressStateAtom = Atom(
      name: '_MyCartScreenState.fetchListOfAddressState', context: context);

  @override
  DataState get fetchListOfAddressState {
    _$fetchListOfAddressStateAtom.reportRead();
    return super.fetchListOfAddressState;
  }

  @override
  set fetchListOfAddressState(DataState value) {
    _$fetchListOfAddressStateAtom
        .reportWrite(value, super.fetchListOfAddressState, () {
      super.fetchListOfAddressState = value;
    });
  }

  late final _$_addressesAtom =
      Atom(name: '_MyCartScreenState._addresses', context: context);

  List<Address>? get addresses {
    _$_addressesAtom.reportRead();
    return super._addresses;
  }

  @override
  List<Address>? get _addresses => addresses;

  @override
  set _addresses(List<Address>? value) {
    _$_addressesAtom.reportWrite(value, super._addresses, () {
      super._addresses = value;
    });
  }

  late final _$_selectedAddressAtom =
      Atom(name: '_MyCartScreenState._selectedAddress', context: context);

  Address? get selectedAddress {
    _$_selectedAddressAtom.reportRead();
    return super._selectedAddress;
  }

  @override
  Address? get _selectedAddress => selectedAddress;

  @override
  set _selectedAddress(Address? value) {
    _$_selectedAddressAtom.reportWrite(value, super._selectedAddress, () {
      super._selectedAddress = value;
    });
  }

  late final _$deleteAddressStateAtom =
      Atom(name: '_MyCartScreenState.deleteAddressState', context: context);

  @override
  DataState get deleteAddressState {
    _$deleteAddressStateAtom.reportRead();
    return super.deleteAddressState;
  }

  @override
  set deleteAddressState(DataState value) {
    _$deleteAddressStateAtom.reportWrite(value, super.deleteAddressState, () {
      super.deleteAddressState = value;
    });
  }

  late final _$_orderAtom =
      Atom(name: '_MyCartScreenState._order', context: context);

  Order? get order {
    _$_orderAtom.reportRead();
    return super._order;
  }

  @override
  Order? get _order => order;

  @override
  set _order(Order? value) {
    _$_orderAtom.reportWrite(value, super._order, () {
      super._order = value;
    });
  }

  late final _$_orderAfterTxnDoneAtom =
      Atom(name: '_MyCartScreenState._orderAfterTxnDone', context: context);

  OrderAfterTxnDone? get orderAfterTxnDone {
    _$_orderAfterTxnDoneAtom.reportRead();
    return super._orderAfterTxnDone;
  }

  @override
  OrderAfterTxnDone? get _orderAfterTxnDone => orderAfterTxnDone;

  @override
  set _orderAfterTxnDone(OrderAfterTxnDone? value) {
    _$_orderAfterTxnDoneAtom.reportWrite(value, super._orderAfterTxnDone, () {
      super._orderAfterTxnDone = value;
    });
  }

  late final _$orderAfterTxnDoneStateAtom =
      Atom(name: '_MyCartScreenState.orderAfterTxnDoneState', context: context);

  @override
  DataState get orderAfterTxnDoneState {
    _$orderAfterTxnDoneStateAtom.reportRead();
    return super.orderAfterTxnDoneState;
  }

  @override
  set orderAfterTxnDoneState(DataState value) {
    _$orderAfterTxnDoneStateAtom
        .reportWrite(value, super.orderAfterTxnDoneState, () {
      super.orderAfterTxnDoneState = value;
    });
  }

  late final _$_setOrderAfterTxnDoneAttemptAtom = Atom(
      name: '_MyCartScreenState._setOrderAfterTxnDoneAttempt',
      context: context);

  int get setOrderAfterTxnDoneAttempt {
    _$_setOrderAfterTxnDoneAttemptAtom.reportRead();
    return super._setOrderAfterTxnDoneAttempt;
  }

  @override
  int get _setOrderAfterTxnDoneAttempt => setOrderAfterTxnDoneAttempt;

  @override
  set _setOrderAfterTxnDoneAttempt(int value) {
    _$_setOrderAfterTxnDoneAttemptAtom
        .reportWrite(value, super._setOrderAfterTxnDoneAttempt, () {
      super._setOrderAfterTxnDoneAttempt = value;
    });
  }

  late final _$_isProceedToCheckoutButtonDisabledAtom = Atom(
      name: '_MyCartScreenState._isProceedToCheckoutButtonDisabled',
      context: context);

  bool? get isProceedToCheckoutButtonDisabled {
    _$_isProceedToCheckoutButtonDisabledAtom.reportRead();
    return super._isProceedToCheckoutButtonDisabled;
  }

  @override
  bool? get _isProceedToCheckoutButtonDisabled =>
      isProceedToCheckoutButtonDisabled;

  @override
  set _isProceedToCheckoutButtonDisabled(bool? value) {
    _$_isProceedToCheckoutButtonDisabledAtom
        .reportWrite(value, super._isProceedToCheckoutButtonDisabled, () {
      super._isProceedToCheckoutButtonDisabled = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_MyCartScreenState.init', context: context);

  @override
  Future<void> init(
      void Function(DiscountBoxComponentData) triggerPlanBottomSheetCallBack) {
    return _$initAsyncAction
        .run(() => super.init(triggerPlanBottomSheetCallBack));
  }

  late final _$_fetchUserProfileAsyncAction =
      AsyncAction('_MyCartScreenState._fetchUserProfile', context: context);

  @override
  Future<void> _fetchUserProfile() {
    return _$_fetchUserProfileAsyncAction.run(() => super._fetchUserProfile());
  }

  late final _$_fetchCartDetailsAsyncAction =
      AsyncAction('_MyCartScreenState._fetchCartDetails', context: context);

  @override
  Future<void> _fetchCartDetails() {
    return _$_fetchCartDetailsAsyncAction.run(() => super._fetchCartDetails());
  }

  late final _$removeItemFromCartAsyncAction =
      AsyncAction('_MyCartScreenState.removeItemFromCart', context: context);

  @override
  Future<void> removeItemFromCart(int cartItemId) {
    return _$removeItemFromCartAsyncAction
        .run(() => super.removeItemFromCart(cartItemId));
  }

  late final _$addSubscriptionToCartAsyncAction =
      AsyncAction('_MyCartScreenState.addSubscriptionToCart', context: context);

  @override
  Future<void> addSubscriptionToCart() {
    return _$addSubscriptionToCartAsyncAction
        .run(() => super.addSubscriptionToCart());
  }

  late final _$_setAddSubscriptionAsyncAction =
      AsyncAction('_MyCartScreenState._setAddSubscription', context: context);

  @override
  Future<void> _setAddSubscription(CartItem cartItem) {
    return _$_setAddSubscriptionAsyncAction
        .run(() => super._setAddSubscription(cartItem));
  }

  late final _$_removeAllExceptSubscriptionAsyncAction = AsyncAction(
      '_MyCartScreenState._removeAllExceptSubscription',
      context: context);

  @override
  Future<void> _removeAllExceptSubscription(int cartItemId) {
    return _$_removeAllExceptSubscriptionAsyncAction
        .run(() => super._removeAllExceptSubscription(cartItemId));
  }

  late final _$removeSubscriptionFromCartAsyncAction = AsyncAction(
      '_MyCartScreenState.removeSubscriptionFromCart',
      context: context);

  @override
  Future<void> removeSubscriptionFromCart() {
    return _$removeSubscriptionFromCartAsyncAction
        .run(() => super.removeSubscriptionFromCart());
  }

  late final _$_fetchListOfSubscriptionAsyncAction = AsyncAction(
      '_MyCartScreenState._fetchListOfSubscription',
      context: context);

  @override
  Future<void> _fetchListOfSubscription() {
    return _$_fetchListOfSubscriptionAsyncAction
        .run(() => super._fetchListOfSubscription());
  }

  late final _$_setListOfSubscriptionAsyncAction = AsyncAction(
      '_MyCartScreenState._setListOfSubscription',
      context: context);

  @override
  Future<void> _setListOfSubscription(List<Subscription> subscriptions) {
    return _$_setListOfSubscriptionAsyncAction
        .run(() => super._setListOfSubscription(subscriptions));
  }

  late final _$fetchListOfAddressAsyncAction =
      AsyncAction('_MyCartScreenState.fetchListOfAddress', context: context);

  @override
  Future<void> fetchListOfAddress() {
    return _$fetchListOfAddressAsyncAction
        .run(() => super.fetchListOfAddress());
  }

  late final _$_setListOfAddressAsyncAction =
      AsyncAction('_MyCartScreenState._setListOfAddress', context: context);

  @override
  Future<void> _setListOfAddress(List<Address> addresses) {
    return _$_setListOfAddressAsyncAction
        .run(() => super._setListOfAddress(addresses));
  }

  late final _$deleteAddressAsyncAction =
      AsyncAction('_MyCartScreenState.deleteAddress', context: context);

  @override
  Future<void> deleteAddress(int addressId) {
    return _$deleteAddressAsyncAction.run(() => super.deleteAddress(addressId));
  }

  late final _$_onDeleteAddressAsyncAction =
      AsyncAction('_MyCartScreenState._onDeleteAddress', context: context);

  @override
  Future<void> _onDeleteAddress(int addressId) {
    return _$_onDeleteAddressAsyncAction
        .run(() => super._onDeleteAddress(addressId));
  }

  late final _$createPaymentIntentAsyncAction =
      AsyncAction('_MyCartScreenState.createPaymentIntent', context: context);

  @override
  Future<String?> createPaymentIntent() {
    return _$createPaymentIntentAsyncAction
        .run(() => super.createPaymentIntent());
  }

  late final _$fetchOrderAfterSuccessAsyncAction = AsyncAction(
      '_MyCartScreenState.fetchOrderAfterSuccess',
      context: context);

  @override
  Future<void> fetchOrderAfterSuccess() {
    return _$fetchOrderAfterSuccessAsyncAction
        .run(() => super.fetchOrderAfterSuccess());
  }

  late final _$makePaymentAsyncAction =
      AsyncAction('_MyCartScreenState.makePayment', context: context);

  @override
  Future<void> makePayment({required VoidCallback onSuccess}) {
    return _$makePaymentAsyncAction
        .run(() => super.makePayment(onSuccess: onSuccess));
  }

  late final _$displayPaymentSheetAsyncAction =
      AsyncAction('_MyCartScreenState.displayPaymentSheet', context: context);

  @override
  Future displayPaymentSheet({required VoidCallback onSuccess}) {
    return _$displayPaymentSheetAsyncAction
        .run(() => super.displayPaymentSheet(onSuccess: onSuccess));
  }

  late final _$_MyCartScreenStateActionController =
      ActionController(name: '_MyCartScreenState', context: context);

  @override
  void _setAppUser(AppUser appUser) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState._setAppUser');
    try {
      return super._setAppUser(appUser);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCartDetails(Cart cart) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState.setCartDetails');
    try {
      return super.setCartDetails(cart);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _onRemoveFormCart(Cart cart) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState._onRemoveFormCart');
    try {
      return super._onRemoveFormCart(cart);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void tryAgainFetchCartDetails() {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState.tryAgainFetchCartDetails');
    try {
      return super.tryAgainFetchCartDetails();
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _onRemoveSubscriptionFromCart(Cart cart) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState._onRemoveSubscriptionFromCart');
    try {
      return super._onRemoveSubscriptionFromCart(cart);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSelectedAddress(int? id) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState.setSelectedAddress');
    try {
      return super.setSelectedAddress(id);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic _setOrder(Order order) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState._setOrder');
    try {
      return super._setOrder(order);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic _setOrderAfterTxnDone(OrderAfterTxnDone orderAfterTxnDone) {
    final _$actionInfo = _$_MyCartScreenStateActionController.startAction(
        name: '_MyCartScreenState._setOrderAfterTxnDone');
    try {
      return super._setOrderAfterTxnDone(orderAfterTxnDone);
    } finally {
      _$_MyCartScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
cartDetailsState: ${cartDetailsState},
subscriptionAddRemoveActionState: ${subscriptionAddRemoveActionState},
fetchListOfSubscriptionState: ${fetchListOfSubscriptionState},
fetchListOfAddressState: ${fetchListOfAddressState},
deleteAddressState: ${deleteAddressState},
orderAfterTxnDoneState: ${orderAfterTxnDoneState},
cartDetails: ${cartDetails},
isCouponApplied: ${isCouponApplied},
appliedCoupon: ${appliedCoupon},
addedSubscriptionItem: ${addedSubscriptionItem},
isMasterClassItemsAdded: ${isMasterClassItemsAdded},
isSubscriptionAdded: ${isSubscriptionAdded},
checkoutCartData: ${checkoutCartData},
isCartEmpty: ${isCartEmpty},
getAddedItemToCartDataList: ${getAddedItemToCartDataList},
activeSubscription: ${activeSubscription},
isAllAddressDeleted: ${isAllAddressDeleted},
fullAddress: ${fullAddress},
getListOfBillingAddressCardData: ${getListOfBillingAddressCardData}
    ''';
  }
}
