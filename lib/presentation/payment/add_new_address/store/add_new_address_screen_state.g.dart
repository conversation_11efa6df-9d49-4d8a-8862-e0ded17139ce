// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_new_address_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$AddNewAddressScreenState on _AddNewAddressScreenState, Store {
  Computed<String>? _$getNameComputed;

  @override
  String get getName =>
      (_$getNameComputed ??= Computed<String>(() => super.getName,
              name: '_AddNewAddressScreenState.getName'))
          .value;
  Computed<String>? _$getEmailComputed;

  @override
  String get getEmail =>
      (_$getEmailComputed ??= Computed<String>(() => super.getEmail,
              name: '_AddNewAddressScreenState.getEmail'))
          .value;
  Computed<String>? _$getPhoneComputed;

  @override
  String get getPhone =>
      (_$getPhoneComputed ??= Computed<String>(() => super.getPhone,
              name: '_AddNewAddressScreenState.getPhone'))
          .value;
  Computed<bool>? _$isNewAddressValidComputed;

  @override
  bool get isNewAddressValid => (_$isNewAddressValidComputed ??= Computed<bool>(
          () => super.isNewAddressValid,
          name: '_AddNewAddressScreenState.isNewAddressValid'))
      .value;

  late final _$_appUserAtom =
      Atom(name: '_AddNewAddressScreenState._appUser', context: context);

  AppUser? get appUser {
    _$_appUserAtom.reportRead();
    return super._appUser;
  }

  @override
  AppUser? get _appUser => appUser;

  @override
  set _appUser(AppUser? value) {
    _$_appUserAtom.reportWrite(value, super._appUser, () {
      super._appUser = value;
    });
  }

  late final _$addressAtom =
      Atom(name: '_AddNewAddressScreenState.address', context: context);

  @override
  String get address {
    _$addressAtom.reportRead();
    return super.address;
  }

  @override
  set address(String value) {
    _$addressAtom.reportWrite(value, super.address, () {
      super.address = value;
    });
  }

  late final _$countryAtom =
      Atom(name: '_AddNewAddressScreenState.country', context: context);

  @override
  String get country {
    _$countryAtom.reportRead();
    return super.country;
  }

  @override
  set country(String value) {
    _$countryAtom.reportWrite(value, super.country, () {
      super.country = value;
    });
  }

  late final _$stateAtom =
      Atom(name: '_AddNewAddressScreenState.state', context: context);

  @override
  String get state {
    _$stateAtom.reportRead();
    return super.state;
  }

  @override
  set state(String value) {
    _$stateAtom.reportWrite(value, super.state, () {
      super.state = value;
    });
  }

  late final _$pinCodeAtom =
      Atom(name: '_AddNewAddressScreenState.pinCode', context: context);

  @override
  String get pinCode {
    _$pinCodeAtom.reportRead();
    return super.pinCode;
  }

  @override
  set pinCode(String value) {
    _$pinCodeAtom.reportWrite(value, super.pinCode, () {
      super.pinCode = value;
    });
  }

  late final _$_addressIdAtom =
      Atom(name: '_AddNewAddressScreenState._addressId', context: context);

  int? get addressId {
    _$_addressIdAtom.reportRead();
    return super._addressId;
  }

  @override
  int? get _addressId => addressId;

  @override
  set _addressId(int? value) {
    _$_addressIdAtom.reportWrite(value, super._addressId, () {
      super._addressId = value;
    });
  }

  late final _$_nameAtom =
      Atom(name: '_AddNewAddressScreenState._name', context: context);

  String? get name {
    _$_nameAtom.reportRead();
    return super._name;
  }

  @override
  String? get _name => name;

  @override
  set _name(String? value) {
    _$_nameAtom.reportWrite(value, super._name, () {
      super._name = value;
    });
  }

  late final _$_emailAtom =
      Atom(name: '_AddNewAddressScreenState._email', context: context);

  String? get email {
    _$_emailAtom.reportRead();
    return super._email;
  }

  @override
  String? get _email => email;

  @override
  set _email(String? value) {
    _$_emailAtom.reportWrite(value, super._email, () {
      super._email = value;
    });
  }

  late final _$_phoneAtom =
      Atom(name: '_AddNewAddressScreenState._phone', context: context);

  String? get phone {
    _$_phoneAtom.reportRead();
    return super._phone;
  }

  @override
  String? get _phone => phone;

  @override
  set _phone(String? value) {
    _$_phoneAtom.reportWrite(value, super._phone, () {
      super._phone = value;
    });
  }

  late final _$addNewAddressStateAtom = Atom(
      name: '_AddNewAddressScreenState.addNewAddressState', context: context);

  @override
  DataState get addNewAddressState {
    _$addNewAddressStateAtom.reportRead();
    return super.addNewAddressState;
  }

  @override
  set addNewAddressState(DataState value) {
    _$addNewAddressStateAtom.reportWrite(value, super.addNewAddressState, () {
      super.addNewAddressState = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_AddNewAddressScreenState.init', context: context);

  @override
  Future<void> init(
      {required AddNewAddressScreenData addNewAddressScreenData}) {
    return _$initAsyncAction.run(
        () => super.init(addNewAddressScreenData: addNewAddressScreenData));
  }

  late final _$_fetchUserProfileAsyncAction = AsyncAction(
      '_AddNewAddressScreenState._fetchUserProfile',
      context: context);

  @override
  Future<void> _fetchUserProfile() {
    return _$_fetchUserProfileAsyncAction.run(() => super._fetchUserProfile());
  }

  late final _$addNewAddressAsyncAction =
      AsyncAction('_AddNewAddressScreenState.addNewAddress', context: context);

  @override
  Future<void> addNewAddress(VoidCallback onSuccess) {
    return _$addNewAddressAsyncAction.run(() => super.addNewAddress(onSuccess));
  }

  late final _$_AddNewAddressScreenStateActionController =
      ActionController(name: '_AddNewAddressScreenState', context: context);

  @override
  void _setAppUser(AppUser appUser) {
    final _$actionInfo = _$_AddNewAddressScreenStateActionController
        .startAction(name: '_AddNewAddressScreenState._setAppUser');
    try {
      return super._setAppUser(appUser);
    } finally {
      _$_AddNewAddressScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setUserNonEditableFields() {
    final _$actionInfo =
        _$_AddNewAddressScreenStateActionController.startAction(
            name: '_AddNewAddressScreenState._setUserNonEditableFields');
    try {
      return super._setUserNonEditableFields();
    } finally {
      _$_AddNewAddressScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _onAddNewAddress(Address address, VoidCallback onSuccess) {
    final _$actionInfo = _$_AddNewAddressScreenStateActionController
        .startAction(name: '_AddNewAddressScreenState._onAddNewAddress');
    try {
      return super._onAddNewAddress(address, onSuccess);
    } finally {
      _$_AddNewAddressScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _onUpdateAddress(Address address, VoidCallback onSuccess) {
    final _$actionInfo = _$_AddNewAddressScreenStateActionController
        .startAction(name: '_AddNewAddressScreenState._onUpdateAddress');
    try {
      return super._onUpdateAddress(address, onSuccess);
    } finally {
      _$_AddNewAddressScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
address: ${address},
country: ${country},
state: ${state},
pinCode: ${pinCode},
addNewAddressState: ${addNewAddressState},
getName: ${getName},
getEmail: ${getEmail},
getPhone: ${getPhone},
isNewAddressValid: ${isNewAddressValid}
    ''';
  }
}
