// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'apply_coupon_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$ApplyCouponScreenState on _ApplyCouponScreenState, Store {
  Computed<bool>? _$isCouponAppliedComputed;

  @override
  bool get isCouponApplied =>
      (_$isCouponAppliedComputed ??= Computed<bool>(() => super.isCouponApplied,
              name: '_ApplyCouponScreenState.isCouponApplied'))
          .value;
  Computed<CouponCodeCardData?>? _$appliedCouponCodeCardDataComputed;

  @override
  CouponCodeCardData? get appliedCouponCodeCardData =>
      (_$appliedCouponCodeCardDataComputed ??= Computed<CouponCodeCardData?>(
              () => super.appliedCouponCodeCardData,
              name: '_ApplyCouponScreenState.appliedCouponCodeCardData'))
          .value;
  Computed<List<CouponCodeCardData>?>? _$getCouponCodeCardDataComputed;

  @override
  List<CouponCodeCardData>? get getCouponCodeCardData =>
      (_$getCouponCodeCardDataComputed ??= Computed<List<CouponCodeCardData>?>(
              () => super.getCouponCodeCardData,
              name: '_ApplyCouponScreenState.getCouponCodeCardData'))
          .value;

  late final _$selectedCouponAtom =
      Atom(name: '_ApplyCouponScreenState.selectedCoupon', context: context);

  @override
  Coupon? get selectedCoupon {
    _$selectedCouponAtom.reportRead();
    return super.selectedCoupon;
  }

  @override
  set selectedCoupon(Coupon? value) {
    _$selectedCouponAtom.reportWrite(value, super.selectedCoupon, () {
      super.selectedCoupon = value;
    });
  }

  late final _$dataPassedFromCartScreenAtom = Atom(
      name: '_ApplyCouponScreenState.dataPassedFromCartScreen',
      context: context);

  @override
  ApplyCouponScreenData? get dataPassedFromCartScreen {
    _$dataPassedFromCartScreenAtom.reportRead();
    return super.dataPassedFromCartScreen;
  }

  @override
  set dataPassedFromCartScreen(ApplyCouponScreenData? value) {
    _$dataPassedFromCartScreenAtom
        .reportWrite(value, super.dataPassedFromCartScreen, () {
      super.dataPassedFromCartScreen = value;
    });
  }

  late final _$listOfCouponsStateAtom = Atom(
      name: '_ApplyCouponScreenState.listOfCouponsState', context: context);

  @override
  DataState get listOfCouponsState {
    _$listOfCouponsStateAtom.reportRead();
    return super.listOfCouponsState;
  }

  @override
  set listOfCouponsState(DataState value) {
    _$listOfCouponsStateAtom.reportWrite(value, super.listOfCouponsState, () {
      super.listOfCouponsState = value;
    });
  }

  late final _$filteredCouponCodeCardDataAtom = Atom(
      name: '_ApplyCouponScreenState.filteredCouponCodeCardData',
      context: context);

  @override
  List<CouponCodeCardData> get filteredCouponCodeCardData {
    _$filteredCouponCodeCardDataAtom.reportRead();
    return super.filteredCouponCodeCardData;
  }

  @override
  set filteredCouponCodeCardData(List<CouponCodeCardData> value) {
    _$filteredCouponCodeCardDataAtom
        .reportWrite(value, super.filteredCouponCodeCardData, () {
      super.filteredCouponCodeCardData = value;
    });
  }

  late final _$_couponsAtom =
      Atom(name: '_ApplyCouponScreenState._coupons', context: context);

  List<Coupon>? get coupons {
    _$_couponsAtom.reportRead();
    return super._coupons;
  }

  @override
  List<Coupon>? get _coupons => coupons;

  @override
  set _coupons(List<Coupon>? value) {
    _$_couponsAtom.reportWrite(value, super._coupons, () {
      super._coupons = value;
    });
  }

  late final _$_updatedCartAtom =
      Atom(name: '_ApplyCouponScreenState._updatedCart', context: context);

  Cart? get updatedCart {
    _$_updatedCartAtom.reportRead();
    return super._updatedCart;
  }

  @override
  Cart? get _updatedCart => updatedCart;

  @override
  set _updatedCart(Cart? value) {
    _$_updatedCartAtom.reportWrite(value, super._updatedCart, () {
      super._updatedCart = value;
    });
  }

  late final _$couponActionStateAtom =
      Atom(name: '_ApplyCouponScreenState.couponActionState', context: context);

  @override
  DataState get couponActionState {
    _$couponActionStateAtom.reportRead();
    return super.couponActionState;
  }

  @override
  set couponActionState(DataState value) {
    _$couponActionStateAtom.reportWrite(value, super.couponActionState, () {
      super.couponActionState = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_ApplyCouponScreenState.init', context: context);

  @override
  Future<void> init({required ApplyCouponScreenData applyCouponScreenData}) {
    return _$initAsyncAction
        .run(() => super.init(applyCouponScreenData: applyCouponScreenData));
  }

  late final _$applyCouponAsyncAction =
      AsyncAction('_ApplyCouponScreenState.applyCoupon', context: context);

  @override
  Future<void> applyCoupon({required String couponCode}) {
    return _$applyCouponAsyncAction
        .run(() => super.applyCoupon(couponCode: couponCode));
  }

  late final _$removeCouponAsyncAction =
      AsyncAction('_ApplyCouponScreenState.removeCoupon', context: context);

  @override
  Future<void> removeCoupon({required String couponCode}) {
    return _$removeCouponAsyncAction
        .run(() => super.removeCoupon(couponCode: couponCode));
  }

  late final _$_ApplyCouponScreenStateActionController =
      ActionController(name: '_ApplyCouponScreenState', context: context);

  @override
  void _setSelectedCoupon({required int? appliedCouponId}) {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState._setSelectedCoupon');
    try {
      return super._setSelectedCoupon(appliedCouponId: appliedCouponId);
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void filterCouponCodeCardData(String query) {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState.filterCouponCodeCardData');
    try {
      return super.filterCouponCodeCardData(query);
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  dynamic _fetchListOfCoupons() {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState._fetchListOfCoupons');
    try {
      return super._fetchListOfCoupons();
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setListOfCoupons(List<Coupon> coupons) {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState._setListOfCoupons');
    try {
      return super._setListOfCoupons(coupons);
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setUpdatedCartAfterCouponApplied(Cart cart) {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState._setUpdatedCartAfterCouponApplied');
    try {
      return super._setUpdatedCartAfterCouponApplied(cart);
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setUpdatedCartAfterCouponRemoved(Cart cart) {
    final _$actionInfo = _$_ApplyCouponScreenStateActionController.startAction(
        name: '_ApplyCouponScreenState._setUpdatedCartAfterCouponRemoved');
    try {
      return super._setUpdatedCartAfterCouponRemoved(cart);
    } finally {
      _$_ApplyCouponScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
selectedCoupon: ${selectedCoupon},
dataPassedFromCartScreen: ${dataPassedFromCartScreen},
listOfCouponsState: ${listOfCouponsState},
filteredCouponCodeCardData: ${filteredCouponCodeCardData},
couponActionState: ${couponActionState},
isCouponApplied: ${isCouponApplied},
appliedCouponCodeCardData: ${appliedCouponCodeCardData},
getCouponCodeCardData: ${getCouponCodeCardData}
    ''';
  }
}
