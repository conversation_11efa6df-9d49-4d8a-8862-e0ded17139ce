import 'package:component/placeholders/bh.dart';
import 'package:component/placeholders/br.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/date_time_component.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/index.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class CommetsComponents extends StatelessWidget {
  final String comment;
  final String dateString;
  final String timeString;

  const CommetsComponents({
    super.key,
    required this.comment,
    required this.dateString,
    required this.timeString,
  });

  @override
  Widget build(BuildContext context) {
  

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Comments',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: DarkThemeColors.blueTextColor,
              ),
        ),
        const BR(kBaseSize / 2),
        Text(
          comment,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DarkThemeColors.white,
              ),
        ),
        const BR(kBaseSize / 1.5),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            DateTimeComponent(dateString: dateString, timeString: timeString),
            Row(
              children: [
                Assets.icon.attachmentIcon.svg(),
                const BH(kBaseSize / 2),
                Text(
                  'View',
                  style: Theme.of(context).textTheme.bodyLarge,
                )
              ],
            )
          ],
        )
      ],
    );
  }
}
