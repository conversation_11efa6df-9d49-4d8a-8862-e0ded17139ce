import 'dart:ui';
import 'package:component/buttons/index.dart';
import 'package:component/placeholders/br.dart';
import 'package:component/buttons/close_icon_button.dart';
import 'package:component/textfields/app_text_field.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:resources/resources.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class ReopenTicketBottomSheet extends StatefulWidget {
  final VoidCallback onReopenPressed;
  
  const ReopenTicketBottomSheet({
    super.key,
    required this.onReopenPressed,
  });

  @override
  State<ReopenTicketBottomSheet> createState() => _ReopenTicketBottomSheetState();
}

class _ReopenTicketBottomSheetState extends State<ReopenTicketBottomSheet> {
  final TextEditingController _reasonController = TextEditingController();
  bool _isReasonEmpty = true;

  @override
  void initState() {
    super.initState();
    _reasonController.addListener(_onReasonChanged);
  }

  @override
  void dispose() {
    _reasonController.removeListener(_onReasonChanged);
    _reasonController.dispose();
    super.dispose();
  }

  void _onReasonChanged() {
    setState(() {
      _isReasonEmpty = _reasonController.text.trim().isEmpty;
    });
  }

  Widget _wCloseIcon(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CloseIconButton(
          onPressed: () => context.pop(),
        ),
      ],
    );
  }

  Widget _wHeading(TextTheme textTheme) {
    return Text(
      'Re-Open',
      style: textTheme.titleMedium?.copyWith(color: DarkThemeColors.gray30),
    );
  }

  Widget _wReasonTextField() {
    return AppTextField(
      name: 'reason',
      controller: _reasonController,
      labelText: 'Reason for re-opening',
      hintText: 'Please provide a reason for re-opening this ticket...',
      maxLines: 4,
      minLines: 4,
      isMultiLine: true,
      textInputAction: TextInputAction.newline,
      keyboardType: TextInputType.multiline,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please provide a reason for re-opening';
        }
        return null;
      },
    );
  }

  Widget _wContent(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _wHeading(textTheme),
        const BR(kBaseSize),
        _wReasonTextField(),
      ],
    );
  }

  Widget _wReopenBox(TextTheme textTheme) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      padding: const EdgeInsets.all(kBaseSize),
      width: double.infinity,
      decoration: BoxDecoration(
        color: DarkThemeColors.black100,
        borderRadius: BorderRadius.all(
          Radius.circular(
            L.r(16),
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: _wContent(textTheme),
      ),
    );
  }

  Widget _wReopenBtn() {
    return AppButton(
      text: 'Re-Open',
      isDisabled: _isReasonEmpty,
      onPressed: () {
        if (!_isReasonEmpty) {
          widget.onReopenPressed();
          context.pop();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 15,
          sigmaY: 15,
        ),
        child: Padding(
          padding: const EdgeInsets.all(kBaseSize),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _wCloseIcon(context),
              const BR(kBaseSize),
              _wReopenBox(textTheme),
              const BR(kBaseSize),
              _wReopenBtn(),
              const BR(kBaseSize),
            ],
          ),
        ),
      ),
    );
  }
}
