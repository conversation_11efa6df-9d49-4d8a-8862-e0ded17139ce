import 'dart:ui';
import 'package:component/buttons/index.dart';
import 'package:component/placeholders/br.dart';

import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:resources/resources.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class EscalateTicketBottomSheet extends StatelessWidget {
  final VoidCallback onEscalatePressed;
  
  const EscalateTicketBottomSheet({
    super.key,
    required this.onEscalatePressed,
  });

  Widget _wCloseIcon(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CloseIconButton(
          onPressed: () => context.pop(),
        ),
      ],
    );
  }

  Widget _wHeading(TextTheme textTheme) {
    return Text(
      'Escalate Ticket',
      style: textTheme.bodyLarge?.copyWith(color: DarkThemeColors.redColor),
    );
  }

  Widget _wConfirmationMessage(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Are you sure you want to escalate this ticket?',
          style: textTheme.bodyLarge?.copyWith(
            color: DarkThemeColors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        const BR(kBaseSize / 2),
        Text(
          'This action will forward your ticket to higher management for priority handling.',
          style: textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _wContent(TextTheme textTheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _wHeading(textTheme),
        const BR(kBaseSize/2),
        _wConfirmationMessage(textTheme),
      ],
    );
  }

  Widget _wEscalateBox(BuildContext context, TextTheme textTheme) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.5,
      ),
      padding: const EdgeInsets.all(kBaseSize),
      width: double.infinity,
      decoration: BoxDecoration(
        color: DarkThemeColors.black100,
        borderRadius: BorderRadius.all(
          Radius.circular(
            L.r(16),
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: _wContent(textTheme),
      ),
    );
  }

  Widget _wActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () => context.pop(),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: DarkThemeColors.gray40),
              padding: const EdgeInsets.symmetric(vertical: kBaseSize),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(L.r(8)),
              ),
            ),
            child: Text(
              'Cancel',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: DarkThemeColors.gray40,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        const SizedBox(width: kBaseSize),
        Expanded(
          child: AppButton(
            text: 'Escalate',
            onPressed: () {
              onEscalatePressed();
              context.pop();
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      child: BackdropFilter(
        filter: ImageFilter.blur(
          sigmaX: 15,
          sigmaY: 15,
        ),
        child: Padding(
          padding: const EdgeInsets.all(kBaseSize),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _wCloseIcon(context),
              const BR(kBaseSize),
              _wEscalateBox(context, textTheme),
              const BR(kBaseSize),
              _wActionButtons(context),
              const BR(kBaseSize),
            ],
          ),
        ),
      ),
    );
  }
}
