import 'dart:io';
import 'package:flutter/material.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

/// A component that displays an attached image with a close button
///
/// Features:
/// - Shows image preview (from File, network URL, or asset)
/// - Close button with callback
/// - Consistent styling with app theme
/// - Loading and error states
/// - Customizable styling options
class AttachedImageCard extends StatelessWidget {
  /// The image file to display
  final File? imageFile;

  /// Network image URL (if not using file)
  final String? imageUrl;

  /// Asset image path (if not using file or URL)
  final String? assetPath;

  /// The name/title to display for the image
  final String imageName;

  /// The file size to display (optional)
  final String? fileSize;

  /// Callback when close button is tapped
  final VoidCallback onClose;

  /// Optional callback when image is tapped
  final VoidCallback? onImageTap;

  /// Custom width for the card
  final double? width;

  /// Custom height for the card
  final double? height;

  /// Custom border radius
  final double? borderRadius;

  /// Whether to show a loading indicator
  final bool isLoading;

  /// Custom background color
  final Color? backgroundColor;

  /// Custom border color
  final Color? borderColor;

  const AttachedImageCard({
    super.key,
    this.imageFile,
    this.imageUrl,
    this.assetPath,
    required this.imageName,
    this.fileSize,
    required this.onClose,
    this.onImageTap,
    this.width,
    this.height = 56,
    this.borderRadius = 12,
    this.isLoading = false,
    this.backgroundColor,
    this.borderColor,
  }) : assert(
          imageFile != null || imageUrl != null || assetPath != null,
          'At least one image source must be provided',
        );

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor ?? DarkThemeColors.gray800.withOpacity(0.8),
        borderRadius: BorderRadius.circular(borderRadius ?? 12),
        border: Border.all(
          color: borderColor ?? DarkThemeColors.gray700.withOpacity(0.5),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Image preview
          _buildImagePreview(),

          const SizedBox(width: 12),

          // Image name and size
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  imageName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                ),
                if (fileSize != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    fileSize!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w400,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ],
            ),
          ),

          const SizedBox(width: 8),

          // Close button
          _buildCloseButton(),
        ],
      ),
    );
  }

  Widget _buildImagePreview() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: DarkThemeColors.gray700.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: isLoading
          ? const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    DarkThemeColors.primaryColor,
                  ),
                ),
              ),
            )
          : ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _buildImage(),
            ),
    );
  }

  Widget _buildImage() {
    Widget imageWidget;

    if (imageFile != null) {
      // File image
      imageWidget = Image.file(
        imageFile!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(),
      );
    } else if (imageUrl != null) {
      // Network image
      imageWidget = Image.network(
        imageUrl!,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildLoadingPlaceholder();
        },
        errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(),
      );
    } else if (assetPath != null) {
      // Asset image
      imageWidget = Image.asset(
        assetPath!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(),
      );
    } else {
      // Fallback placeholder
      imageWidget = _buildErrorPlaceholder();
    }

    return GestureDetector(
      onTap: onImageTap,
      child: imageWidget,
    );
  }

  Widget _buildLoadingPlaceholder() {
    return Container(
      width: 40,
      height: 40,
      color: DarkThemeColors.gray700.withOpacity(0.3),
      child: const Center(
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              DarkThemeColors.primaryColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorPlaceholder() {
    return Container(
      width: 40,
      height: 40,
      color: DarkThemeColors.gray700.withOpacity(0.3),
      child: Center(
        child: Assets.icon.placeholderImage.image(
          width: 20,
          height: 20,
          color: DarkThemeColors.gray700,
        ),
      ),
    );
  }

  Widget _buildCloseButton() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onClose,
        borderRadius: BorderRadius.circular(12),
        splashColor: DarkThemeColors.redChip.withOpacity(0.1),
        highlightColor: DarkThemeColors.redChip.withOpacity(0.05),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: Assets.icon.crossSvg.svg(
            width: 16,
            height: 16,
            colorFilter: ColorFilter.mode(
              DarkThemeColors.gray700,
              BlendMode.srcIn,
            ),
          ),
        ),
      ),
    );
  }
}
