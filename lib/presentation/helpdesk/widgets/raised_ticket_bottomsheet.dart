import 'package:component/component.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:resources/resources.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class RaisedTicketBottomSheet extends StatelessWidget {
  final VoidCallback? onCrossButtonPressed;
  const RaisedTicketBottomSheet(
      {super.key, this.onCrossButtonPressed});

   @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        return Container(
      padding: const EdgeInsets.all(kBaseSize),
      decoration: BoxDecoration(
        color: DarkThemeColors.backgroundBlackColor,
        borderRadius: BorderRadius.circular(kBaseSize / 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Align(
            alignment: Alignment.centerRight,
            child: CircularIconBackground(
                padding: const EdgeInsets.all(kBaseSize / 2),
                onPressed: () {
                  context.pop();
                },
                child: Assets.icon.crossSvg.svg(
                  height: L.h(18),
                  width: L.w(18),
                ),
              ),
          ),
        
          const BR(kBaseSize * 10 - 8),
          Text('You have successfully raised a ticket.', style: Theme.of(context).textTheme.displayMedium,
          textAlign: TextAlign.center,
          ),
          const BR(kBaseSize),
          Text('A confirmation email was <NAME_EMAIL>', style: Theme.of(context).textTheme.bodyMedium,
          textAlign: TextAlign.center,
          ),
          const BR(kBaseSize * 1.5),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: kBaseSize + 6),
            child: AppButton(
                text: 'Okay',
                onPressed: () => context.pop(),
              ),
          ),

 
          const BR(kBaseSize)
        ],
      ),
    );
      },
    );
  }

  // Show category selection bottom sheet
}
