import 'dart:io';
import 'package:component/component.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/core/services/scaffold_service.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_category/helpdesk_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_sub_category/helpdesk_sub_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_question/helpdesk_question.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attach_file_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attached_image_card.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class RaiseTicketComponent extends StatefulWidget {
  const RaiseTicketComponent({super.key});

  @override
  State<RaiseTicketComponent> createState() => _RaiseTicketComponentState();
}

class _RaiseTicketComponentState extends State<RaiseTicketComponent> {
  late HelpdeskScreenState _helpdeskState;

  // Form state
  HelpdeskCategory? _selectedCategory;
  HelpdeskSubCategory? _selectedSubCategory;
  List<int> _selectedQuestionIds = [];
  String _additionalComment = '';
  File? _attachedFile;

  @override
  void initState() {
    super.initState();
    _helpdeskState = sl<HelpdeskScreenState>();
    // Categories should already be loaded from helpdesk screen init
  }


  @override
  void dispose() {
    _helpdeskState.clearQuestions();
    _helpdeskState.clearSubCategories();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        return Container(
      padding: const EdgeInsets.all(kBaseSize),
      decoration: BoxDecoration(
        color: DarkThemeColors.backgroundBlackColor,
        borderRadius: BorderRadius.circular(kBaseSize / 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Raise Ticket',
                  style: Theme.of(context).textTheme.titleMedium),
              CircularIconBackground(
                padding: const EdgeInsets.all(kBaseSize / 2),
                onPressed: () {
                  context.pop();
                },
                child: Assets.icon.crossSvg.svg(
                  height: L.h(18),
                  width: L.w(18),
                ),
              ),
            ],
          ),
          const BR(kBaseSize + 2),
          Text('Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedCategory?.category ?? 'Select Category',
            onTap: _showCategoryBottomSheet
          ),
          const BR(kBaseSize),
          Text('Sub - Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedSubCategory?.question ?? 'Select Sub-Category',
            onTap: _selectedCategory != null ? _showSubCategoryBottomSheet : null
          ),
          const BR(kBaseSize),
          _helpdeskState.questions != null ? 
          _wIssuesList(
            context,
            questions: _helpdeskState.questions!,
            selectedQuestionIds: _selectedQuestionIds,
            onChanged: _onQuestionSelectionChanged,
          ) : const SizedBox.shrink(),
          // _wIssuesList(
          //   context,
          //   issues: const [
          //     'Incorrect Content',
          //     'Technical issue- Page didn’t load/is stuck',
          //     'Unable to navigate to different pages',
          //   ],
          //   onChanged: (value) {}, questions: [],
          // ),
          const BR(kBaseSize),
          const AppTextField(
            name: 'Not in the option type it here.',
            hintText: 'Not in the option type it here.',
            isMultiLine: true,
            minLines: 8,
          ),
          const BR(kBaseSize),
          const AttachFileComponent(),
          const BR(kBaseSize * 2),
          AttachedImageCard(
            imageName: 'chandresh phono',
            onClose: () {},
            imageUrl:
                'https://images.unsplash.com/photo-1546540551-29dc0a3f6786?q=80&w=2495&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA',
          ),
          const BR(kBaseSize),
          Center(
            child: SizedBox(
              width: 161,
              child: AppButton(text: 'Confirm', onPressed: () {}),
            ),
          ),
          const BR(kBaseSize)
        ],
      ),
    );
      },
    );
  }

  // Show category selection bottom sheet
  void _showCategoryBottomSheet() {
    if (_helpdeskState.categories == null || _helpdeskState.categories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Category',
        options: _helpdeskState.categories!.map((category) => category.category ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedCategory = _helpdeskState.categories![index];
            _selectedSubCategory = null; // Reset subcategory
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch subcategories for selected category
          if (_selectedCategory?.categoryId != null) {
            _helpdeskState.fetchSubCategories(_selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Show subcategory selection bottom sheet
  void _showSubCategoryBottomSheet() {
    if (_helpdeskState.subCategories == null || _helpdeskState.subCategories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Sub-Category',
        options: _helpdeskState.subCategories!.map((subCategory) => subCategory.question ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedSubCategory = _helpdeskState.subCategories![index];
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch questions for selected subcategory
          if (_selectedSubCategory?.questionId != null && _selectedCategory?.categoryId != null) {
            _helpdeskState.fetchQuestions(_selectedSubCategory!.questionId!, _selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Handle question selection (single-select)
  void _onQuestionSelectionChanged(int questionId, bool isSelected) {
    setState(() {
      if (isSelected) {
        // For single selection, clear all previous selections and add the new one
        _selectedQuestionIds.clear();
        _selectedQuestionIds.add(questionId);
      } else {
        _selectedQuestionIds.remove(questionId);
      }
    });
  }

  // Pick file from gallery (placeholder for now)
  void _pickFile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('File picker functionality will be implemented when image_picker package is added'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Check if form can be submitted
  bool _canSubmit() {
    return _selectedCategory != null &&
           _selectedSubCategory != null &&
           (_selectedQuestionIds.isNotEmpty || _additionalComment.isNotEmpty);
  }

  // Submit ticket
  void _submitTicket() {
    if (!_canSubmit()) return;

    // TODO: Implement ticket submission
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Ticket submission functionality will be implemented'),
        duration: Duration(seconds: 2),
      ),
    );

    // Close the bottom sheet
    context.pop();
  }

  Widget _wDropDown(BuildContext context,
      {required String text, required VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: kBaseSize + 4, vertical: kBaseSize - 3),
          decoration: BoxDecoration(
            color: onTap != null
                ? DarkThemeColors.inputFieldBlackColor
                : DarkThemeColors.inputFieldBlackColor.withOpacity(0.5),
            borderRadius: BorderRadius.circular(kBaseSize / 2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                ),
              ),
              Assets.icon.chevonDown.svg(
                colorFilter: ColorFilter.mode(
                  onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                  BlendMode.srcIn,
                ),
              ),
            ],
          )),
    );
  }

  Widget _wIssuesList(BuildContext context,
      {required List<HelpdeskQuestion> questions, required List<int> selectedQuestionIds, required void Function(int, bool) onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Issue Raised',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: DarkThemeColors.white,
                )),
        const BR(kBaseSize / 2),
        Column(
          children: questions.mapIndexed(
            (index, question) {
              final questionId = question.id ?? index;

              return Padding(
                padding: EdgeInsets.only(top: index == 0 ? 0 : kBaseSize / 2),
                child: AppRadioListTile<int>(
                  title: Text(
                    question.subQuestion ?? 'Question ${index + 1}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  value: questionId,
                  groupValue: selectedQuestionIds.isNotEmpty ? selectedQuestionIds.first : null,
                  onChanged: (value) {
                    if (value != null) {
                      // For single selection, always select the new option
                      onChanged(value, true);
                    }
                  },
                ),
              );
            },
          ).toList(),
        )
      ],
    );
  }
}
