import 'package:component/component.dart';
import 'package:component/placeholders/br.dart';
import 'package:component/placeholders/circular_icon_background.dart';
import 'package:component/tiles/app_radio_list_tile.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attach_file_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attached_image_card.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class RaiseTicketComponent extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(kBaseSize),
      decoration: BoxDecoration(
        color: DarkThemeColors.backgroundBlackColor,
        borderRadius: BorderRadius.circular(kBaseSize / 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Raise Ticket',
                  style: Theme.of(context).textTheme.titleMedium),
              CircularIconBackground(
                padding: const EdgeInsets.all(kBaseSize / 2),
                onPressed: () {
                  context.pop();
                },
                child: Assets.icon.crossSvg.svg(
                  height: L.h(18),
                  width: L.w(18),
                ),
              ),
            ],
          ),
          const BR(kBaseSize + 2),
          Text('Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(context, text: 'Select Category', onTap: () {}),
          const BR(kBaseSize),
          Text('Sub - Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(context, text: 'Select Sub-Category', onTap: () {}),
          const BR(kBaseSize),
          _wIssuesList(
            context,
            issues: const [
              'Incorrect Content',
              'Technical issue- Page didn’t load/is stuck',
              'Unable to navigate to different pages',
            ],
            onChanged: (value) {},
          ),
          const BR(kBaseSize),
          const AppTextField(
            name: 'Not in the option type it here.',
            hintText: 'Not in the option type it here.',
            isMultiLine: true,
            minLines: 8,
          ),
          const BR(kBaseSize),
          const AttachFileComponent(),
          const BR(kBaseSize * 2),
          AttachedImageCard(
            imageName: 'chandresh phono',
            onClose: () {},
            imageUrl:
                'https://images.unsplash.com/photo-1546540551-29dc0a3f6786?q=80&w=2495&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA',
          ),
          const BR(kBaseSize),
          Center(
            child: SizedBox(
              width: 161,
              child: AppButton(text: 'Confirm', onPressed: () {}),
            ),
          ),
          const BR(kBaseSize)
        ],
      ),
    );
  }

  Widget _wDropDown(BuildContext context,
      {required String text, required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: kBaseSize + 4, vertical: kBaseSize - 3),
          decoration: BoxDecoration(
            color: DarkThemeColors.inputFieldBlackColor,
            borderRadius: BorderRadius.circular(kBaseSize / 2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(text, style: Theme.of(context).textTheme.bodyMedium),
              Assets.icon.chevonDown.svg(),
            ],
          )),
    );
  }

  Widget _wIssuesList(BuildContext context,
      {required List<String> issues, required void Function(int?)? onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Issue Raised',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: DarkThemeColors.white,
                )),
        const BR(kBaseSize / 2),
        Column(
          children: issues.mapIndexed(
            (index, option) {
              return Padding(
                padding: EdgeInsets.only(top: index == 0 ? 0 : kBaseSize / 2),
                child: AppRadioListTile<int?>(
                  //titlePadding: const EdgeInsets.only(top: 10),
                  //isThreeLine: _subtitles == null ? false : true,
                  title: Text(
                    option,
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  // subtitle: widget.subtitles == null
                  //     ? null
                  //     : Text(
                  //         widget.subtitles![index],
                  //         style: textTheme.bodyMedium
                  //             ?.copyWith(color: DarkThemeColors.gray40),
                  //       ),
                  value: issues.indexOf(option),
                  groupValue: 0,
                  onChanged: (value) {},
                ),
              );
            },
          ).toList(),
        )
      ],
    );
  }
}
