import 'dart:io';
import 'package:component/component.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/core/services/scaffold_service.dart';
import 'package:miles_masterclass/data/repositories/auth_repository.dart';
import 'package:miles_masterclass/domain/entities/auth/app_user/app_user.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_category/helpdesk_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_sub_category/helpdesk_sub_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_question/helpdesk_question.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attach_file_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attached_image_card.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/models/helpdesk_image.dart';

// Data class to hold file information
class FileInfo {
  final File file;
  final int originalSize;
  final int? compressedSize;
  final bool isCompressed;
  final bool hasError;
  final String? errorMessage;

  const FileInfo({
    required this.file,
    required this.originalSize,
    this.compressedSize,
    this.isCompressed = false,
    this.hasError = false,
    this.errorMessage,
  });

  FileInfo copyWith({
    File? file,
    int? originalSize,
    int? compressedSize,
    bool? isCompressed,
    bool? hasError,
    String? errorMessage,
  }) {
    return FileInfo(
      file: file ?? this.file,
      originalSize: originalSize ?? this.originalSize,
      compressedSize: compressedSize ?? this.compressedSize,
      isCompressed: isCompressed ?? this.isCompressed,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class RaiseTicketComponent extends StatefulWidget {
  final VoidCallback? onTicketSubmitted;
  final VoidCallback? onDispose;

  const RaiseTicketComponent({
    super.key,
    this.onTicketSubmitted,
    this.onDispose,
  });

  @override
  State<RaiseTicketComponent> createState() => _RaiseTicketComponentState();
}

class _RaiseTicketComponentState extends State<RaiseTicketComponent> {
  late HelpdeskScreenState _helpdeskState;

  // Form state
  HelpdeskCategory? _selectedCategory;
  HelpdeskSubCategory? _selectedSubCategory;
  List<int> _selectedQuestionIds = [];
  String _additionalComment = '';
  List<File> _attachedImages = [];
  List<bool> _imageLoadingStates = []; // Track loading state for each image
  List<String> _uploadedImageUrls = []; // Store uploaded image URLs
  List<FileInfo> _fileInfos = []; // Store file information including original and compressed sizes

  // Image picker instance
  final ImagePicker _imagePicker = ImagePicker();
  final TextEditingController _commentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _helpdeskState = sl<HelpdeskScreenState>();
    // Categories should already be loaded from helpdesk screen init

    // Initialize the comment controller with existing text
    _commentController.text = _additionalComment;
    _commentController.addListener(() {
      _additionalComment = _commentController.text;
    });
  }


  @override
  void dispose() {
    _commentController.dispose();
    _helpdeskState.clearQuestions();
    _helpdeskState.clearSubCategories();
    // Call the dispose callback when the component is being disposed
    widget.onDispose?.call();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        return Container(
      padding: const EdgeInsets.all(kBaseSize),
      decoration: BoxDecoration(
        color: DarkThemeColors.backgroundBlackColor,
        borderRadius: BorderRadius.circular(kBaseSize / 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Raise Ticket',
                  style: Theme.of(context).textTheme.titleMedium),
              CircularIconBackground(
                padding: const EdgeInsets.all(kBaseSize / 2),
                onPressed: () {
                  context.pop();
                },
                child: Assets.icon.crossSvg.svg(
                  height: L.h(18),
                  width: L.w(18),
                ),
              ),
            ],
          ),
          const BR(kBaseSize + 2),
          Text('Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedCategory?.category ?? 'Select Category',
            onTap: _showCategoryBottomSheet
          ),
          const BR(kBaseSize),
          Text('Sub - Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedSubCategory?.question ?? 'Select Sub-Category',
            onTap: _selectedCategory != null ? _showSubCategoryBottomSheet : null
          ),
          const BR(kBaseSize),
          _helpdeskState.questions != null ? 
          _wIssuesList(
            context,
            questions: _helpdeskState.questions!,
            selectedQuestionIds: _selectedQuestionIds,
            onChanged: _onQuestionSelectionChanged,
          ) : const SizedBox.shrink(),
          // _wIssuesList(
          //   context,
          //   issues: const [
          //     'Incorrect Content',
          //     'Technical issue- Page didn’t load/is stuck',
          //     'Unable to navigate to different pages',
          //   ],
          //   onChanged: (value) {}, questions: [],
          // ),
          const BR(kBaseSize),

          // Additional Comment
          AppTextField(
            name: 'additionalComment',
            hintText: 'Not in the option type it here.',
            isMultiLine: true,
            minLines: 8,
            controller: _commentController,
            onChanged: (value) {
              _additionalComment = value;
            },
          ),
          const BR(kBaseSize),

          // Attach File
          GestureDetector(
            onTap: _pickImages,
            child: const AttachFileComponent(),
          ),
          const BR(kBaseSize),

          // Show attached images if any
          if (_attachedImages.isNotEmpty) ...[
            Text(
              'Attached Images (${_attachedImages.where((img) => img.path.isNotEmpty).length})',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DarkThemeColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            const BR(kBaseSize / 2),
            ...List.generate(_attachedImages.length, (index) {
              final image = _attachedImages[index];
              final isLoading = index < _imageLoadingStates.length ? _imageLoadingStates[index] : false;

              return Padding(
                padding: EdgeInsets.only(bottom: index < _attachedImages.length - 1 ? kBaseSize / 2 : 0),
                child: isLoading
                    ? _buildLoadingImageCard()
                    : _buildImageCard(image, index),
              );
            }),
            const BR(kBaseSize),
          ],

          // Confirm Button
          Center(
            child: SizedBox(
              width: 161,
              child: AppButton(
                text: 'Confirm',
                onPressed: _canSubmit() ? () => _submitTicket() : () {}
              ),
            ),
          ),
          const BR(kBaseSize)
        ],
      ),
    );
      },
    );
  }

  // Show category selection bottom sheet
  void _showCategoryBottomSheet() {
    if (_helpdeskState.categories == null || _helpdeskState.categories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Category',
        options: _helpdeskState.categories!.map((category) => category.category ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedCategory = _helpdeskState.categories![index];
            _selectedSubCategory = null; // Reset subcategory
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch subcategories for selected category
          if (_selectedCategory?.categoryId != null) {
            _helpdeskState.fetchSubCategories(_selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Show subcategory selection bottom sheet
  void _showSubCategoryBottomSheet() {
    if (_helpdeskState.subCategories == null || _helpdeskState.subCategories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Sub-Category',
        options: _helpdeskState.subCategories!.map((subCategory) => subCategory.question ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedSubCategory = _helpdeskState.subCategories![index];
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch questions for selected subcategory
          if (_selectedSubCategory?.questionId != null && _selectedCategory?.categoryId != null) {
            _helpdeskState.fetchQuestions(_selectedSubCategory!.questionId!, _selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Handle question selection (single-select)
  void _onQuestionSelectionChanged(int questionId, bool isSelected) {
    setState(() {
      if (isSelected) {
        // For single selection, clear all previous selections and add the new one
        _selectedQuestionIds.clear();
        _selectedQuestionIds.add(questionId);
      } else {
        _selectedQuestionIds.remove(questionId);
      }
    });
  }

  // Pick images from gallery with validation
  Future<void> _pickImages() async {
    try {
      // Show image source selection bottom sheet
      final ImageSource? source = await _showImageSourceBottomSheet();
      if (source == null) return;

      List<XFile> pickedFiles = [];

      if (source == ImageSource.gallery) {
        // Pick multiple images from gallery with compression
        pickedFiles = await _imagePicker.pickMultipleMedia(
          imageQuality: 70, // Reduced quality for smaller file size
          maxWidth: 1600,   // Reduced max width
          maxHeight: 1200,  // Reduced max height
        );
      } else {
        // Pick single image from camera with compression
        final XFile? pickedFile = await _imagePicker.pickImage(
          source: source,
          imageQuality: 70, // Reduced quality for smaller file size
          maxWidth: 1600,   // Reduced max width
          maxHeight: 1200,  // Reduced max height
        );
        if (pickedFile != null) {
          pickedFiles = [pickedFile];
        }
      }

      if (pickedFiles.isEmpty) return;

      // Process each file with loading states
      for (int i = 0; i < pickedFiles.length; i++) {
        final XFile pickedFile = pickedFiles[i];

        if (await _isValidImageFile(pickedFile)) {
          // Add loading state for this image
          setState(() {
            _imageLoadingStates.add(true);
            _attachedImages.add(File('')); // Placeholder for loading
            _uploadedImageUrls.add(''); // Placeholder for URL
          });

          // Process image to check size and handle compression
          final FileInfo? fileInfo = await _processImageFile(pickedFile);

          if (fileInfo != null) {
            final loadingIndex = _imageLoadingStates.length - 1;

            // Check if file has error (condition 3: too large even after compression)
            if (fileInfo.hasError) {
              setState(() {
                _attachedImages[loadingIndex] = fileInfo.file;
                _fileInfos.add(fileInfo);
                _imageLoadingStates[loadingIndex] = false;
              });
              // Don't upload, just show error state
            } else {
              // File is good to upload (conditions 1 & 2)
              final String? uploadedUrl = await _helpdeskState.uploadFile(fileInfo.file);

              if (uploadedUrl != null) {
                // Update with actual file, URL and remove loading state
                setState(() {
                  _attachedImages[loadingIndex] = fileInfo.file;
                  _uploadedImageUrls[loadingIndex] = uploadedUrl;
                  _fileInfos.add(fileInfo);
                  _imageLoadingStates[loadingIndex] = false;
                });
              } else {
                // Upload failed, remove the entry
                setState(() {
                  _attachedImages.removeLast();
                  _imageLoadingStates.removeLast();
                  _uploadedImageUrls.removeLast();
                });
                _showErrorMessage('Failed to upload image. Please try again.');
              }
            }
          } else {
            // Processing failed, remove the entry
            setState(() {
              _attachedImages.removeLast();
              _imageLoadingStates.removeLast();
              _uploadedImageUrls.removeLast();
            });
          }
        } else {
          _showErrorMessage('Invalid file type. Only JPEG and PNG images are allowed.');
        }
      }
    } catch (e) {
      _showErrorMessage('Failed to pick images: ${e.toString()}');
    }
  }

  // Show image source selection bottom sheet
  Future<ImageSource?> _showImageSourceBottomSheet() async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      backgroundColor: DarkThemeColors.backgroundBlackColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Image Source',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: DarkThemeColors.white,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.photo_library, color: DarkThemeColors.white),
              title: const Text('Gallery', style: TextStyle(color: DarkThemeColors.white)),
              subtitle: const Text('Select multiple images', style: TextStyle(color: DarkThemeColors.gray700)),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: DarkThemeColors.white),
              title: const Text('Camera', style: TextStyle(color: DarkThemeColors.white)),
              subtitle: const Text('Take a photo', style: TextStyle(color: DarkThemeColors.gray700)),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  // Validate if the file is a valid image (JPEG/PNG)
  Future<bool> _isValidImageFile(XFile file) async {
    final String extension = file.path.toLowerCase().split('.').last;
    final List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(extension)) {
      return false;
    }

    // Check if file exists and is readable
    final File imageFile = File(file.path);
    if (!await imageFile.exists()) {
      return false;
    }

    return true;
  }

  // Process and validate image file size (2MB limit) with compression
  Future<FileInfo?> _processImageFile(XFile pickedFile) async {
    try {
      final File originalFile = File(pickedFile.path);
      final int originalSize = await originalFile.length();
      const int maxSizeInBytes = 2 * 1024 * 1024; // 2MB

      // Condition 1: File is already under 2MB - no compression needed
      if (originalSize <= maxSizeInBytes) {
        return FileInfo(
          file: originalFile,
          originalSize: originalSize,
          isCompressed: false,
        );
      }

      // Condition 2: File is over 2MB - try compression
      final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
        originalFile.absolute.path,
        '${originalFile.parent.path}/compressed_${DateTime.now().millisecondsSinceEpoch}.jpg',
        quality: 70,
        minWidth: 1024,
        minHeight: 1024,
      );

      if (compressedFile != null) {
        final File compressedFileObj = File(compressedFile.path);
        final int compressedSize = await compressedFileObj.length();

        // Condition 2: Compressed file is under 2MB - success
        if (compressedSize <= maxSizeInBytes) {
          return FileInfo(
            file: compressedFileObj,
            originalSize: originalSize,
            compressedSize: compressedSize,
            isCompressed: true,
          );
        } else {
          // Condition 3: Even compressed file is over 2MB - error
          return FileInfo(
            file: originalFile,
            originalSize: originalSize,
            compressedSize: compressedSize,
            isCompressed: true,
            hasError: true,
            errorMessage: 'File too large even after compression',
          );
        }
      } else {
        // Compression failed
        return FileInfo(
          file: originalFile,
          originalSize: originalSize,
          hasError: true,
          errorMessage: 'Compression failed',
        );
      }

    } catch (e) {
      return FileInfo(
        file: File(pickedFile.path),
        originalSize: 0,
        hasError: true,
        errorMessage: 'Error processing image: ${e.toString()}',
      );
    }
  }

  // Format file size for display
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      double kb = bytes / 1024;
      return '${kb < 1 ? kb.toStringAsFixed(2) : kb.toStringAsFixed(1)} KB';
    } else {
      double mb = bytes / (1024 * 1024);
      return '${mb < 1 ? mb.toStringAsFixed(2) : mb.toStringAsFixed(1)} MB';
    }
  }



  // Remove image from attached list
  void _removeImage(int index) {
    setState(() {
      _attachedImages.removeAt(index);
      _imageLoadingStates.removeAt(index);
      _uploadedImageUrls.removeAt(index);
      if (index < _fileInfos.length) {
        _fileInfos.removeAt(index);
      }
    });
  }

  // Build loading shimmer card for image processing
  Widget _buildLoadingImageCard() {
    return AttachedImageCard(
      imageFile: File(''), // Empty file as placeholder
      imageName: 'Uploading...',
      fileSize: 'Processing...',
      isLoading: true,
      onClose: () {}, // No action during loading
    );
  }

  // Build image card with proper file size display
  Widget _buildImageCard(File image, int index) {
    final FileInfo? fileInfo = index < _fileInfos.length ? _fileInfos[index] : null;

    if (fileInfo?.hasError == true) {
      // Error state - file too large even after compression
      return AttachedImageCard(
        imageFile: image,
        imageName: image.path.split('/').last,
        fileSize: "Can't upload due to size",
        onClose: () => _removeImage(index),
        backgroundColor: Colors.red.withOpacity(0.1),
        borderColor: Colors.red,
      );
    } else if (fileInfo?.isCompressed == true) {
      // Compressed file - show both original and compressed sizes
      return AttachedImageCard(
        imageFile: image,
        imageName: image.path.split('/').last,
        fileSize: _buildCompressedSizeText(fileInfo!),
        onClose: () => _removeImage(index),
      );
    } else {
      // Normal file - show regular size
      return AttachedImageCard(
        imageFile: image,
        imageName: image.path.split('/').last,
        fileSize: _formatFileSize(fileInfo?.originalSize ?? image.lengthSync()),
        onClose: () => _removeImage(index),
      );
    }
  }

  // Build compressed size text with strikethrough original size
  String _buildCompressedSizeText(FileInfo fileInfo) {
    final originalSize = _formatFileSize(fileInfo.originalSize);
    final compressedSize = _formatFileSize(fileInfo.compressedSize ?? 0);
    return '$originalSize → $compressedSize';
  }



  // Show error message
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Check if form can be submitted
  bool _canSubmit() {
    return _selectedCategory != null &&
           _selectedSubCategory != null &&
           (_selectedQuestionIds.isNotEmpty || _additionalComment.isNotEmpty);
  }

  // Reset form state after successful submission
  void _resetFormState() {
    setState(() {
      _selectedCategory = null;
      _selectedSubCategory = null;
      _selectedQuestionIds.clear();
      _additionalComment = '';
      _attachedImages.clear();
      _imageLoadingStates.clear();
      _uploadedImageUrls.clear();
      _fileInfos.clear();
    });
    _commentController.clear();
    _helpdeskState.clearQuestions();
    _helpdeskState.clearSubCategories();
  }

  // Submit ticket
  void _submitTicket() async {
    if (!_canSubmit()) return;

    // Create HelpdeskImage objects from uploaded files
    final List<HelpdeskImage> imageObjects = [];

    for (int i = 0; i < _uploadedImageUrls.length; i++) {
      final String url = _uploadedImageUrls[i];
      if (url.isNotEmpty && i < _fileInfos.length) {
        final FileInfo fileInfo = _fileInfos[i];
        final File file = fileInfo.file;

        // Extract file information
        final String fileName = file.path.split('/').last;
        final String fileExtension = fileName.split('.').last.toLowerCase();
        final int fileSize = fileInfo.isCompressed
            ? (fileInfo.compressedSize ?? fileInfo.originalSize)
            : fileInfo.originalSize;

        // Create HelpdeskImage object
        final HelpdeskImage imageObject = HelpdeskImage(
          name: fileName,
          path: url, // Use the uploaded URL as path
          format: fileExtension,
          size: fileSize,
        );

        imageObjects.add(imageObject);
      }
    }

    // Show loading bottom sheet
    _showLoadingBottomSheet();

    try {
      // Call the raise query API
      final int? ticketId = await _helpdeskState.raiseQuery(
        categoryId: _selectedCategory?.categoryId ?? 0,
        questionId: _selectedSubCategory?.questionId ?? 0,
        subQuestionIds: _selectedQuestionIds,
        helpdesk: _additionalComment.isNotEmpty ? _additionalComment : 'Query raised from mobile app',
        imageObjects: imageObjects,
        // uniqueId: _helpdeskState.appUser?.id.toString() ?? 'NA',
        uniqueId: 'uniq001',
        name: _helpdeskState.appUser?.name  ?? 'NA',
        email: _helpdeskState.appUser?.email ?? 'NA',
        phoneNumber: _helpdeskState.appUser?.mobile ?? 'NA',
      );

      // Close loading sheet
      context.pop();

      if (ticketId != null) {
        // Show success bottom sheet with ticket ID
        _showSuccessBottomSheet(ticketId);
      } else {
        // Show error bottom sheet
        _showErrorBottomSheet('Failed to create ticket. Please try again.');
      }
    } catch (e) {
      // Close loading sheet
      context.pop();

      // Show error bottom sheet
      _showErrorBottomSheet('An error occurred: ${e.toString()}');
    }
  }

  // Show loading bottom sheet
  void _showLoadingBottomSheet() {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        padding: const EdgeInsets.all(kBaseSize * 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(DarkThemeColors.primaryColor),
            ),
            const SizedBox(height: kBaseSize),
            Text(
              'Submitting your query...',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: DarkThemeColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show success bottom sheet with ticket ID
  void _showSuccessBottomSheet(int ticketId) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        padding: const EdgeInsets.all(kBaseSize * 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: kBaseSize),
            Text(
              'Query Submitted Successfully!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DarkThemeColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: kBaseSize / 2),
            Text(
              'Your ticket ID is: $ticketId',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: DarkThemeColors.white,
              ),
            ),
            const SizedBox(height: kBaseSize * 2),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  context.pop(); // Close success sheet
                  context.pop(); // Close raise ticket sheet

                  // Reset form state before calling the callback
                  _resetFormState();

                  // Call the success callback to refresh tickets and dispose component
                  widget.onTicketSubmitted?.call();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: DarkThemeColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: kBaseSize),
                ),
                child: Text(
                  'Done',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show error bottom sheet
  void _showErrorBottomSheet(String errorMessage) {
    showModalBottomSheet(
      context: context,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Container(
        padding: const EdgeInsets.all(kBaseSize * 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: kBaseSize),
            Text(
              'Submission Failed',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: DarkThemeColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: kBaseSize / 2),
            Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DarkThemeColors.white,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: kBaseSize * 2),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => context.pop(), // Only close error sheet, preserve component state
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: kBaseSize),
                ),
                child: Text(
                  'Try Again',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _wDropDown(BuildContext context,
      {required String text, required VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: kBaseSize + 4, vertical: kBaseSize - 3),
          decoration: BoxDecoration(
            color: onTap != null
                ? DarkThemeColors.inputFieldBlackColor
                : DarkThemeColors.inputFieldBlackColor.withOpacity(0.5),
            borderRadius: BorderRadius.circular(kBaseSize / 2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                ),
              ),
              Assets.icon.chevonDown.svg(
                colorFilter: ColorFilter.mode(
                  onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                  BlendMode.srcIn,
                ),
              ),
            ],
          )),
    );
  }

  Widget _wIssuesList(BuildContext context,
      {required List<HelpdeskQuestion> questions, required List<int> selectedQuestionIds, required void Function(int, bool) onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Issue Raised',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: DarkThemeColors.white,
                )),
        const BR(kBaseSize / 2),
        Column(
          children: questions.mapIndexed(
            (index, question) {
              final questionId = question.id ?? index;

              return Padding(
                padding: EdgeInsets.only(top: index == 0 ? 0 : kBaseSize / 2),
                child: AppRadioListTile<int>(
                  title: Text(
                    question.subQuestion ?? 'Question ${index + 1}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  value: questionId,
                  groupValue: selectedQuestionIds.isNotEmpty ? selectedQuestionIds.first : null,
                  onChanged: (value) {
                    if (value != null) {
                      // For single selection, always select the new option
                      onChanged(value, true);
                    }
                  },
                ),
              );
            },
          ).toList(),
        )
      ],
    );
  }
}
