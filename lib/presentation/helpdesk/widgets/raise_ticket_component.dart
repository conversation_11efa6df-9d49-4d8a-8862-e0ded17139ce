import 'dart:io';
import 'package:component/component.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/core/services/scaffold_service.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_category/helpdesk_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_sub_category/helpdesk_sub_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_question/helpdesk_question.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attach_file_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/attached_image_card.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class RaiseTicketComponent extends StatefulWidget {
  const RaiseTicketComponent({super.key});

  @override
  State<RaiseTicketComponent> createState() => _RaiseTicketComponentState();
}

class _RaiseTicketComponentState extends State<RaiseTicketComponent> {
  late HelpdeskScreenState _helpdeskState;

  // Form state
  HelpdeskCategory? _selectedCategory;
  HelpdeskSubCategory? _selectedSubCategory;
  List<int> _selectedQuestionIds = [];
  String _additionalComment = '';
  List<File> _attachedImages = [];

  // Image picker instance
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _helpdeskState = sl<HelpdeskScreenState>();
    // Categories should already be loaded from helpdesk screen init
  }


  @override
  void dispose() {
    _helpdeskState.clearQuestions();
    _helpdeskState.clearSubCategories();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        return Container(
      padding: const EdgeInsets.all(kBaseSize),
      decoration: BoxDecoration(
        color: DarkThemeColors.backgroundBlackColor,
        borderRadius: BorderRadius.circular(kBaseSize / 2),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Raise Ticket',
                  style: Theme.of(context).textTheme.titleMedium),
              CircularIconBackground(
                padding: const EdgeInsets.all(kBaseSize / 2),
                onPressed: () {
                  context.pop();
                },
                child: Assets.icon.crossSvg.svg(
                  height: L.h(18),
                  width: L.w(18),
                ),
              ),
            ],
          ),
          const BR(kBaseSize + 2),
          Text('Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedCategory?.category ?? 'Select Category',
            onTap: _showCategoryBottomSheet
          ),
          const BR(kBaseSize),
          Text('Sub - Category',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                  )),
          const BR(kBaseSize / 2),
          _wDropDown(
            context,
            text: _selectedSubCategory?.question ?? 'Select Sub-Category',
            onTap: _selectedCategory != null ? _showSubCategoryBottomSheet : null
          ),
          const BR(kBaseSize),
          _helpdeskState.questions != null ? 
          _wIssuesList(
            context,
            questions: _helpdeskState.questions!,
            selectedQuestionIds: _selectedQuestionIds,
            onChanged: _onQuestionSelectionChanged,
          ) : const SizedBox.shrink(),
          // _wIssuesList(
          //   context,
          //   issues: const [
          //     'Incorrect Content',
          //     'Technical issue- Page didn’t load/is stuck',
          //     'Unable to navigate to different pages',
          //   ],
          //   onChanged: (value) {}, questions: [],
          // ),
          const BR(kBaseSize),

          // Additional Comment
          AppTextField(
            name: 'additionalComment',
            hintText: 'Not in the option type it here.',
            isMultiLine: true,
            minLines: 8,
            onChanged: (value) {
              _additionalComment = value;
            },
          ),
          const BR(kBaseSize),

          // Attach File
          GestureDetector(
            onTap: _pickImages,
            child: const AttachFileComponent(),
          ),
          const BR(kBaseSize),

          // Show attached images if any
          if (_attachedImages.isNotEmpty) ...[
            Text(
              'Attached Images (${_attachedImages.length})',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: DarkThemeColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            const BR(kBaseSize / 2),
            ...List.generate(_attachedImages.length, (index) {
              final image = _attachedImages[index];
              return Padding(
                padding: EdgeInsets.only(bottom: index < _attachedImages.length - 1 ? kBaseSize / 2 : 0),
                child: AttachedImageCard(
                  imageFile: image,
                  imageName: image.path.split('/').last,
                  onClose: () => _removeImage(index),
                ),
              );
            }),
            const BR(kBaseSize),
          ],

          // Confirm Button
          Center(
            child: SizedBox(
              width: 161,
              child: AppButton(
                text: 'Confirm',
                onPressed: _canSubmit() ? () => _submitTicket() : null
              ),
            ),
          ),
          const BR(kBaseSize)
        ],
      ),
    );
      },
    );
  }

  // Show category selection bottom sheet
  void _showCategoryBottomSheet() {
    if (_helpdeskState.categories == null || _helpdeskState.categories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Category',
        options: _helpdeskState.categories!.map((category) => category.category ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedCategory = _helpdeskState.categories![index];
            _selectedSubCategory = null; // Reset subcategory
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch subcategories for selected category
          if (_selectedCategory?.categoryId != null) {
            _helpdeskState.fetchSubCategories(_selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Show subcategory selection bottom sheet
  void _showSubCategoryBottomSheet() {
    if (_helpdeskState.subCategories == null || _helpdeskState.subCategories!.isEmpty) {
      return;
    }

    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: 'Select Sub-Category',
        options: _helpdeskState.subCategories!.map((subCategory) => subCategory.question ?? '').toList(),
        onApplyBtnPressed: (int index) {
          setState(() {
            _selectedSubCategory = _helpdeskState.subCategories![index];
            _selectedQuestionIds.clear(); // Reset questions
          });
          // Fetch questions for selected subcategory
          if (_selectedSubCategory?.questionId != null && _selectedCategory?.categoryId != null) {
            _helpdeskState.fetchQuestions(_selectedSubCategory!.questionId!, _selectedCategory!.categoryId!);
          }
        },
      ),
    );
  }

  // Handle question selection (single-select)
  void _onQuestionSelectionChanged(int questionId, bool isSelected) {
    setState(() {
      if (isSelected) {
        // For single selection, clear all previous selections and add the new one
        _selectedQuestionIds.clear();
        _selectedQuestionIds.add(questionId);
      } else {
        _selectedQuestionIds.remove(questionId);
      }
    });
  }

  // Pick images from gallery with validation
  Future<void> _pickImages() async {
    try {
      // Show image source selection bottom sheet
      final ImageSource? source = await _showImageSourceBottomSheet();
      if (source == null) return;

      List<XFile> pickedFiles = [];

      if (source == ImageSource.gallery) {
        // Pick multiple images from gallery
        pickedFiles = await _imagePicker.pickMultipleMedia(
          imageQuality: 80,
          maxWidth: 1920,
          maxHeight: 1080,
        );
      } else {
        // Pick single image from camera
        final XFile? pickedFile = await _imagePicker.pickImage(
          source: source,
          imageQuality: 80,
          maxWidth: 1920,
          maxHeight: 1080,
        );
        if (pickedFile != null) {
          pickedFiles = [pickedFile];
        }
      }

      if (pickedFiles.isEmpty) return;

      // Validate and process picked files
      final List<File> validImages = [];
      for (final XFile pickedFile in pickedFiles) {
        if (await _isValidImageFile(pickedFile)) {
          validImages.add(File(pickedFile.path));
        } else {
          _showErrorMessage('Invalid file type. Only JPEG and PNG images are allowed.');
        }
      }

      if (validImages.isNotEmpty) {
        setState(() {
          _attachedImages.addAll(validImages);
        });

        _showSuccessMessage('${validImages.length} image(s) added successfully');
      }
    } catch (e) {
      _showErrorMessage('Failed to pick images: ${e.toString()}');
    }
  }

  // Show image source selection bottom sheet
  Future<ImageSource?> _showImageSourceBottomSheet() async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      backgroundColor: DarkThemeColors.backgroundBlackColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Select Image Source',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: DarkThemeColors.white,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.photo_library, color: DarkThemeColors.white),
              title: const Text('Gallery', style: TextStyle(color: DarkThemeColors.white)),
              subtitle: const Text('Select multiple images', style: TextStyle(color: DarkThemeColors.gray700)),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: DarkThemeColors.white),
              title: const Text('Camera', style: TextStyle(color: DarkThemeColors.white)),
              subtitle: const Text('Take a photo', style: TextStyle(color: DarkThemeColors.gray700)),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            const SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  // Validate if the file is a valid image (JPEG/PNG)
  Future<bool> _isValidImageFile(XFile file) async {
    final String extension = file.path.toLowerCase().split('.').last;
    final List<String> allowedExtensions = ['jpg', 'jpeg', 'png'];

    if (!allowedExtensions.contains(extension)) {
      return false;
    }

    // Additional validation: check file size (max 10MB)
    final int fileSize = await File(file.path).length();
    const int maxSizeInBytes = 10 * 1024 * 1024; // 10MB

    return fileSize <= maxSizeInBytes;
  }

  // Remove image from attached list
  void _removeImage(int index) {
    setState(() {
      _attachedImages.removeAt(index);
    });
    _showSuccessMessage('Image removed successfully');
  }

  // Show success message
  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Show error message
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // Check if form can be submitted
  bool _canSubmit() {
    return _selectedCategory != null &&
           _selectedSubCategory != null &&
           (_selectedQuestionIds.isNotEmpty || _additionalComment.isNotEmpty);
  }

  // Submit ticket
  void _submitTicket() {
    if (!_canSubmit()) return;

    // TODO: Implement ticket submission
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Ticket submission functionality will be implemented'),
        duration: Duration(seconds: 2),
      ),
    );

    // Close the bottom sheet
    context.pop();
  }

  Widget _wDropDown(BuildContext context,
      {required String text, required VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(
              horizontal: kBaseSize + 4, vertical: kBaseSize - 3),
          decoration: BoxDecoration(
            color: onTap != null
                ? DarkThemeColors.inputFieldBlackColor
                : DarkThemeColors.inputFieldBlackColor.withOpacity(0.5),
            borderRadius: BorderRadius.circular(kBaseSize / 2),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                ),
              ),
              Assets.icon.chevonDown.svg(
                colorFilter: ColorFilter.mode(
                  onTap != null
                      ? DarkThemeColors.white
                      : DarkThemeColors.white.withOpacity(0.5),
                  BlendMode.srcIn,
                ),
              ),
            ],
          )),
    );
  }

  Widget _wIssuesList(BuildContext context,
      {required List<HelpdeskQuestion> questions, required List<int> selectedQuestionIds, required void Function(int, bool) onChanged}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Issue Raised',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: DarkThemeColors.white,
                )),
        const BR(kBaseSize / 2),
        Column(
          children: questions.mapIndexed(
            (index, question) {
              final questionId = question.id ?? index;

              return Padding(
                padding: EdgeInsets.only(top: index == 0 ? 0 : kBaseSize / 2),
                child: AppRadioListTile<int>(
                  title: Text(
                    question.subQuestion ?? 'Question ${index + 1}',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  value: questionId,
                  groupValue: selectedQuestionIds.isNotEmpty ? selectedQuestionIds.first : null,
                  onChanged: (value) {
                    if (value != null) {
                      // For single selection, always select the new option
                      onChanged(value, true);
                    }
                  },
                ),
              );
            },
          ).toList(),
        )
      ],
    );
  }
}
