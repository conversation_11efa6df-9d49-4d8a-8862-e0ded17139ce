import 'dart:ui';
import 'package:component/placeholders/bh.dart';
import 'package:component/placeholders/circular_icon_background.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';


class SliverCustomAppBar extends StatelessWidget{
  final Widget _child;
  const SliverCustomAppBar._(this._child);



   @override
  Widget build(BuildContext context) {
    return _child;
  }

   /// Factory constructor for Ticket on Helpdesk screen when there are no open tickets.
  factory SliverCustomAppBar.helpdeskAppBar({required String title}) {
    return  SliverCustomAppBar._(HelpdeskAppBar(
      title: title,
    ));
  }

   factory SliverCustomAppBar.helpdeskDetailsScreenAppBar(
    {bool? isReOpenTicket,
    bool? isEscalateTicket,}
   ) {
    return SliverCustomAppBar._(HelpdeskDetailsScreenAppBar(
      isEscalateTicket: isEscalateTicket,
      isReOpenTicket: isReOpenTicket,
    ));
  }
}




class HelpdeskAppBar extends StatelessWidget  {
  final String title;
  const HelpdeskAppBar({super.key,
  required this.title
  });
  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      stretch: true,
      pinned: true,
      floating: true,
      snap: true,
      automaticallyImplyLeading: false,
      flexibleSpace: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 30,
            sigmaY: 30,
          ),
          child: Container(
          
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Assets.icon.helpdeskIcon.svg(),
                const BH(10),
                Text(
                 title,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                CircularIconBackground(
                  padding: const EdgeInsets.all(kBaseSize / 2),
                  onPressed: () {
                    context.pop();
                  },
                  child: Assets.icon.crossSvg.svg(
                    height: L.h(18),
                    width: L.w(18),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}



class HelpdeskDetailsScreenAppBar extends StatelessWidget  {
  final bool? isReOpenTicket;
  final bool? isEscalateTicket;

  const HelpdeskDetailsScreenAppBar({super.key,
  this.isEscalateTicket = false,
  this.isReOpenTicket = false,
  });
  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      stretch: true,
      pinned: true,
      floating: true,
      snap: true,
      automaticallyImplyLeading: false,
      flexibleSpace: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 30,
            sigmaY: 30,
          ),
          child: Container(
          
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
               GestureDetector(
                onTap: context.pop,
                child:  Assets.icon.back.svg(),
               ),
                const BH(10),
                Text(
                  'Back',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
              
              ],
            ),
          ),
        ),
      ],
    );
  }
}