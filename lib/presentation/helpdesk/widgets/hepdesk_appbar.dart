import 'dart:ui';
import 'package:component/placeholders/bh.dart';
import 'package:component/placeholders/circular_icon_background.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';



class SliverCustomAppBar extends StatelessWidget{
  final Widget _child;
  const SliverCustomAppBar._(this._child);



   @override
  Widget build(BuildContext context) {
    return _child;
  }

   /// Factory constructor for Ticket on Helpdesk screen when there are no open tickets.
  factory SliverCustomAppBar.helpdeskAppBar({required String title}) {
    return  SliverCustomAppBar._(HelpdeskAppBar(
      title: title,
    ));
  }

   factory SliverCustomAppBar.helpdeskDetailsScreenAppBar({
    bool? isReOpenTicket,
    bool? isEscalateTicket,
    VoidCallback? onReOpenTicket,
    VoidCallback? onEscalateTicket,
   }) {
    return SliverCustomAppBar._(HelpdeskDetailsScreenAppBar(
      isEscalateTicket: isEscalateTicket,
      isReOpenTicket: isReOpenTicket,
      onReOpenTicket: onReOpenTicket,
      onEscalateTicket: onEscalateTicket,
    ));
  }
}




class HelpdeskAppBar extends StatelessWidget  {
  final String title;
  const HelpdeskAppBar({super.key,
  required this.title
  });
  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      stretch: true,
      pinned: true,
      floating: true,
      snap: true,
      automaticallyImplyLeading: false,
      flexibleSpace: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 30,
            sigmaY: 30,
          ),
          child: Container(
          
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Assets.icon.helpdeskIcon.svg(),
                const BH(10),
                Text(
                 title,
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                CircularIconBackground(
                  padding: const EdgeInsets.all(kBaseSize / 2),
                  onPressed: () {
                    context.pop();
                  },
                  child: Assets.icon.crossSvg.svg(
                    height: L.h(18),
                    width: L.w(18),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}



class HelpdeskDetailsScreenAppBar extends StatelessWidget  {
  final bool? isReOpenTicket;
  final bool? isEscalateTicket;
  final VoidCallback? onReOpenTicket;
  final VoidCallback? onEscalateTicket;

  const HelpdeskDetailsScreenAppBar({
    super.key,
    this.isEscalateTicket = false,
    this.isReOpenTicket = false,
    this.onReOpenTicket,
    this.onEscalateTicket,
  });
  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      stretch: true,
      pinned: true,
      floating: true,
      snap: true,
      automaticallyImplyLeading: false,
      flexibleSpace: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 30,
            sigmaY: 30,
          ),
          child: Container(
          
          ),
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: SizedBox(
            width: MediaQuery.of(context).size.width - 32,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
               GestureDetector(
                onTap: context.pop,
                child:  Assets.icon.back.svg(),
               ),
                const BH(10),
                Text(
                  'Back',
                  style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if(isReOpenTicket! || isEscalateTicket!)
                _buildPopupMenu(context),


                

                // if (isReOpenTicket!)
                // Text(
                //   'Re-Open Ticket',
                //   style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                //     color: DarkThemeColors.white,
                //     fontWeight: FontWeight.bold,
                //   ),
                // ),
                // if (isEscalateTicket!)
                // Text(
                //   'Escalate Ticket',
                //   style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                //     color: DarkThemeColors.white, 
                //     fontWeight: FontWeight.bold,
                //   ),
                // ),
                
              
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPopupMenu(BuildContext context) {
    return 
    PopupMenuButton<String>(
      icon: Assets.icon.moreDotIcon.svg(),
      color: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      offset: const Offset(-120, 50), // Adjusted for better positioning with containers
      position: PopupMenuPosition.under,
      // , dy)

      itemBuilder: (BuildContext context) {
        List<PopupMenuEntry<String>> items = [];

        if (isReOpenTicket == true) {
          items.add(
            PopupMenuItem<String>(
              value: 'reopen',
              padding: EdgeInsets.zero, // Remove default padding
              child: Container(
                margin: const EdgeInsets.all(8), // Add margin around the container
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2D2D), // Dark gray background like in Figma
                  borderRadius: BorderRadius.circular(16), // Rounded corners like in the image
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Text(
                  'Re-Open',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFFFF6B6B), // Red color like in the image
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          );
        }

        if (isEscalateTicket == true) {
          items.add(
            PopupMenuItem<String>(
              value: 'escalate',
              padding: EdgeInsets.zero, // Remove default padding
              child: Container(
                margin: const EdgeInsets.all(8), // Add margin around the container
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
                decoration: BoxDecoration(
                  color: const Color(0xFF2D2D2D), // Dark gray background
                  borderRadius: BorderRadius.circular(16), // Rounded corners
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Text(
                  'Escalate Ticket',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          );
        }

        return items;
      },
      onSelected: (String value) {
        switch (value) {
          case 'reopen':
            onReOpenTicket?.call();
            break;
          case 'escalate':
            onEscalateTicket?.call();
            break;
        }
      },
    );
  }
}


class ChatBubblePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.black // Set the color of the container
      ..style = PaintingStyle.fill; // Fill the shape with color

    Path path = Path()
      ..moveTo(0, 0) // Start at the top-left corner
      ..lineTo(size.width - 20, 0) // Draw the top line to leave space for the triangle
      ..lineTo(size.width, size.height / 2) // Draw to the peak of the triangle
      ..lineTo(size.width - 20, size.height) // Draw the bottom line
      ..lineTo(0, size.height) // Draw the left bottom line
      ..close(); // Close the path to create the shape

    canvas.drawPath(path, paint); // Draw the path with the paint
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}