import 'package:flutter/material.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

enum TicketStatusEnum {
 all,
  open,
  closed,
  escalated,
 
}

extension TicketStatusEnumExtension on TicketStatusEnum {
  String get ticketStatusForFilters => switch (this) {
    TicketStatusEnum.all => 'All Tickets',
        TicketStatusEnum.open => 'Open Tickets',
        TicketStatusEnum.closed => 'Closed Tickets',
        TicketStatusEnum.escalated => 'Escalated Tickets',
        
      };

        int get ticketStatusIdForFilters => switch (this) {
          TicketStatusEnum.all => 0,
        TicketStatusEnum.open => 1,
        TicketStatusEnum.closed => 2,
        TicketStatusEnum.escalated => 3,
        
      };

}



class TicketStatusComponent extends StatelessWidget{
final Widget _child;

const TicketStatusComponent._(this._child);

factory TicketStatusComponent.status({required TicketStatusEnum ticketStatusEnum}){
  switch (ticketStatusEnum) {
    case TicketStatusEnum.open:
      return TicketStatusComponent.open();
    case TicketStatusEnum.closed:
      return TicketStatusComponent.closed();
    case TicketStatusEnum.escalated:
      return TicketStatusComponent.escalated();
      case TicketStatusEnum.all:
      return TicketStatusComponent.all();
  }
}

factory TicketStatusComponent.open(){
  return const TicketStatusComponent._(TicketStatusComponentOpen());
}

factory TicketStatusComponent.closed(){
  return const TicketStatusComponent._(TicketStatusComponentClosed());
}

factory TicketStatusComponent.escalated(){
  return const TicketStatusComponent._(TicketStatusComponentEscalated());
}

factory TicketStatusComponent.all(){
  return const TicketStatusComponent._(SizedBox.shrink());
}

@override
Widget build(BuildContext context) {
  return _child;
}

}

class TicketStatusComponentOpen extends StatelessWidget {
  const TicketStatusComponentOpen({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: DarkThemeColors.yellowChip.withOpacity(0.38),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: DarkThemeColors.yellowChip, width: 1),
      ),
      child: Text(
        'Open',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:  DarkThemeColors.yellowChip
            ),
      ),
    );
    
  }
}


class TicketStatusComponentClosed extends StatelessWidget {
  const TicketStatusComponentClosed({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: DarkThemeColors.greenChip.withOpacity(0.38),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: DarkThemeColors.greenChip, width: 1),
      ),
      child: Text(
        'Closed',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color:  DarkThemeColors.greenChip
            ),
      ),
    );
    
  }
}


class TicketStatusComponentEscalated extends StatelessWidget {
  const TicketStatusComponentEscalated({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: DarkThemeColors.redChip.withOpacity(0.38),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color:DarkThemeColors.redChip, width: 1),
      ),
      child: Text(
        'Escalated',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: DarkThemeColors.redChip,
            ),
      ),
    );
    
  }
}
