import 'package:component/placeholders/br.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/ticket_status_component.dart';
import 'package:resources/resources.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';

class TicketData {
  final int id;
  final String category;
  final String subCategory;
  final String issueRelated;
  final String raisedOn;
  final String updatedOn;
  final String lastComment;

  TicketData({
    required this.id,
    required this.category,
    required this.subCategory,
    required this.issueRelated,
    required this.raisedOn,
    required this.updatedOn,
    required this.lastComment,
  });
}

class TicketComponent extends StatelessWidget {
  final Widget _child;

  // Private Constructor
  const TicketComponent._(this._child);

  /// Factory constructor for Ticket on Helpdesk screen when there are no open tickets.
  factory TicketComponent.noOpenTicket({required VoidCallback onViewPreviousTickets}) {
    return TicketComponent._(NoOpenticket(
      onViewPreviousTickets: onViewPreviousTickets,
    ));
  }

  
  /// Factory constructor for Ticket on All tickets List Screen
  factory TicketComponent.allTickets({ 
    required TicketData ticketData,
    required VoidCallback onViewDetails,
    required TicketStatusEnum ticketStatusEnum,
  }) {
    return TicketComponent._(AllTickets(
      ticketData: ticketData,
      onViewDetails: onViewDetails,
      ticketStatusEnum: ticketStatusEnum,
    ));
  }

  /// Factory constructor for Ticket on Ticket Details Screen
  factory TicketComponent.ticketDetails({
    required TicketData ticketData,
    required TicketStatusEnum ticketStatusEnum,
  }) {
    return TicketComponent._(TicketDetails(
      ticketData: ticketData,
      ticketStatusEnum: ticketStatusEnum,
    ));
  }


  /// Factory constructor for Ticket on Recent tickets on Helpdesk Screen
   factory TicketComponent.recentTicket({
    required TicketData ticketData,
    required VoidCallback onViewDetails,
    required TicketStatusEnum ticketStatusEnum,
  }) {
    return TicketComponent._(RecentTicket(
      ticketData: ticketData,
      ticketStatusEnum: ticketStatusEnum,
      onViewDetails: onViewDetails,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return _child;
  }
}


class NoOpenticket extends StatelessWidget {
  final VoidCallback onViewPreviousTickets;

  const NoOpenticket({
    super.key,
    required this.onViewPreviousTickets,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      clipBehavior: Clip.antiAlias,
      decoration: ShapeDecoration(
        color: DarkThemeColors.cardBlueColor,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Row(children: [
        Assets.icon.helpdeskTicketIcon.svg(),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('No open Tickets',
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.copyWith(color: DarkThemeColors.white)),
                const BR(kBaseSize / 4),
                Text(
                  "Looks like you don't have any tickets right now. Need help? Feel free to create one!",
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
                const BR(kBaseSize - 8),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: onViewPreviousTickets,
                    splashColor: DarkThemeColors.primaryColor.withOpacity(0.1),
                    highlightColor: DarkThemeColors.primaryColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(4),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 2),
                      child: Container(
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        child: Text(
                          'View Previous Tickets',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: DarkThemeColors.primaryColor,
                              decoration: TextDecoration.underline,
                              decorationColor: DarkThemeColors.primaryColor),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ]),
    );
  }
}

class AllTickets extends StatelessWidget {
  final TicketData ticketData;
  final VoidCallback onViewDetails;
    final TicketStatusEnum ticketStatusEnum;

  const AllTickets({
    super.key,
    required this.ticketStatusEnum,
    required this.ticketData,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onViewDetails,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: DarkThemeColors.cardBlueColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Unique ID',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const BR(kBaseSize/4),
                    Text(
                      '${ticketData.id}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                           color: DarkThemeColors.white,
                          ),
                    ),
                  ],
                ),
                TicketStatusComponent.status(ticketStatusEnum: ticketStatusEnum),
              ],
            ),
            const BR(kBaseSize),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category',
                        style: Theme.of(context).textTheme.bodySmall
                      ),
                      const  BR(kBaseSize/4),
                      Text(
                        ticketData.category,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white,
                            ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sub-Category',
                         style: Theme.of(context).textTheme.bodySmall
                      ),
                      const  BR(kBaseSize/4),
                      Text(
                        ticketData.subCategory,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const BR(kBaseSize),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Issue Raised',
                  style: Theme.of(context).textTheme.bodySmall
                ),
                const BR(kBaseSize/4),
                Text(
                  ticketData.issueRelated,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DarkThemeColors.white
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
           const BR(kBaseSize),
            Divider(color: DarkThemeColors.white.withOpacity(0.12), height: 1),
            const BR(kBaseSize),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Raised on',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: DarkThemeColors.blueTextColor
                            ),
                      ),
                      const BR(kBaseSize/4),
                      Text(
                        ticketData.raisedOn,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Updated on',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: DarkThemeColors.blueTextColor
                            ),
                      ),
                      const BR(kBaseSize/4),
                      Text(
                        ticketData.updatedOn,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                             color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const BR(kBaseSize),
            Divider(color: DarkThemeColors.white.withOpacity(0.12), height: 1),
           const BR(kBaseSize),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Last Comment',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const BR(kBaseSize/4),
                Text(
                  ticketData.lastComment,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DarkThemeColors.white
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class TicketDetails extends StatelessWidget {
  final TicketData ticketData;
    final TicketStatusEnum ticketStatusEnum;

  const TicketDetails({
    super.key,
    required this.ticketData,
      required this.ticketStatusEnum,

  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: DarkThemeColors.cardBlueColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Category',
                      style: Theme.of(context).textTheme.bodySmall
                    ),
                    const  BR(kBaseSize/4),
                    Text(
                      ticketData.category,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: DarkThemeColors.white,
                          ),
                    ),
                  ],
                ),
             TicketStatusComponent.status(ticketStatusEnum: ticketStatusEnum),
            ],
          ),
          const BR(kBaseSize),
          Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sub-Category',
                       style: Theme.of(context).textTheme.bodySmall
                    ),
                    const  BR(kBaseSize/4),
                    Text(
                      ticketData.subCategory,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: DarkThemeColors.white
                          ),
                    ),
                  ],
                ),
         const BR(kBaseSize),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Issue Raised',
                style: Theme.of(context).textTheme.bodySmall
              ),
              const BR(kBaseSize/4),
              Text(
                ticketData.issueRelated,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: DarkThemeColors.white
                    ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
         const BR(kBaseSize),
          Divider(color: DarkThemeColors.white.withOpacity(0.12), height: 1),
          const BR(kBaseSize),
           Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Raised on',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: DarkThemeColors.blueTextColor
                          ),
                    ),
                    const BR(kBaseSize/4),
                    Text(
                      ticketData.raisedOn,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: DarkThemeColors.white
                          ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Updated on',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: DarkThemeColors.blueTextColor
                          ),
                    ),
                    const BR(kBaseSize/4),
                    Text(
                      ticketData.updatedOn,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                           color: DarkThemeColors.white
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}


class RecentTicket extends StatelessWidget {
  final TicketData ticketData;
  final VoidCallback onViewDetails;
  final TicketStatusEnum ticketStatusEnum;

  const RecentTicket({
    super.key,
    required this.ticketData,
    required this.onViewDetails,
      required this.ticketStatusEnum,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onViewDetails,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: DarkThemeColors.cardBlueColor,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Unique ID',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const BR(kBaseSize/4),
                    Text(
                      '${ticketData.id}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                           color: DarkThemeColors.white,
                          ),
                    ),
                  ],
                ),
               TicketStatusComponent.status(ticketStatusEnum: ticketStatusEnum),
              ],
            ),
            const BR(kBaseSize),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Category',
                        style: Theme.of(context).textTheme.bodySmall
                      ),
                      const  BR(kBaseSize/4),
                      Text(
                        ticketData.category,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white,
                            ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Sub-Category',
                         style: Theme.of(context).textTheme.bodySmall
                      ),
                      const  BR(kBaseSize/4),
                      Text(
                        ticketData.subCategory,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const BR(kBaseSize),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Issue Raised',
                  style: Theme.of(context).textTheme.bodySmall
                ),
                const BR(kBaseSize/4),
                Text(
                  ticketData.issueRelated,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: DarkThemeColors.white
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
           const BR(kBaseSize),
            Divider(color: DarkThemeColors.white.withOpacity(0.12), height: 1),
            const BR(kBaseSize),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Raised on',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: DarkThemeColors.blueTextColor
                            ),
                      ),
                      const BR(kBaseSize/4),
                      Text(
                        ticketData.raisedOn,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Updated on',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: DarkThemeColors.blueTextColor
                            ),
                      ),
                      const BR(kBaseSize/4),
                      Text(
                        ticketData.updatedOn,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                             color: DarkThemeColors.white
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}