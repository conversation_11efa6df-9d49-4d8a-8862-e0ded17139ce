import 'dart:ui';
import 'package:component/background/app_scaffold.dart';
import 'package:component/component.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/hepdesk_appbar.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/chat_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/commets_components.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/helpdesk_ticket_componet.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';
import 'package:dependencies/dependencies.dart';
import 'package:resources/utils/extensions.dart';

class TicketDetailsScreen extends StatefulWidget {
  const TicketDetailsScreen({super.key});

  @override
  State<TicketDetailsScreen> createState() => _TicketDetailsScreenState();
}

class _TicketDetailsScreenState extends State<TicketDetailsScreen> {
  final _helpdeskState = sl<HelpdeskScreenState>();

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: DarkThemeColors.scaffoldBackgroundColor,
      body: Stack(
        children: [
          _wBody(context),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child:
            ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(
            sigmaX: 30,
            sigmaY: 30,
          ),
          child:_wReplyBox(context),
        ),
      )
            
             
          ),
        ],
      ),
    );
  }

  Widget _wBody(BuildContext context) {
    return Observer(builder: (context) {
      return CustomScrollView(
        slivers: [
           SliverCustomAppBar.helpdeskDetailsScreenAppBar(
            isEscalateTicket: _helpdeskState.selectedTicketForDetails?.isEscalate == 1, 
            isReOpenTicket: _helpdeskState.selectedTicketForDetails?.ticketStatus == 2, // 2 is closed
           ),


          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(kBaseSize),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Assets.icon.ticketDetailsIcon.svg(),
                      const BH(kBaseSize - 6),
                      Text(
                          'Unique ID ${_helpdeskState.getSelectedTicketDetails?.helpdeskId}',
                          style: Theme.of(context).textTheme.headlineLarge),
                    ],
                  ),
                  const BR(kBaseSize),
                  _wTicketDetailsCard(context),
                  const BR(kBaseSize),
                  _wCommets(context),
                  const BR(kBaseSize),
                  _wReplies(context),
                ],
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _wReplies(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Reply', style: Theme.of(context).textTheme.bodyMedium),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount:
              (_helpdeskState.getSelectedTicketDetails?.comment?.length ?? 0) -
                  1,
          separatorBuilder: (context, index) =>
              const SizedBox(height: kBaseSize * 1.5),
          itemBuilder: (context, index) {
            return ChatComponent(
              dateString: _helpdeskState.getSelectedTicketDetails?.comment
                      ?.elementAt(index + 1)
                      .createdDate
                      ?.toDateStrNumberFormat() ??
                  'NA',
              name: 'NA',
              timeString: _helpdeskState.getSelectedTicketDetails?.comment
                      ?.elementAt(index + 1)
                      .createdDate
                      ?.toTimeShortString() ??
                  'NA',
              message: _helpdeskState.getSelectedTicketDetails?.comment
                      ?.elementAt(index + 1)
                      .adminComment ??
                  _helpdeskState.getSelectedTicketDetails?.comment
                      ?.elementAt(index + 1)
                      .studentReason ??
                  'NA',
              isSelf: _helpdeskState.getSelectedTicketDetails?.comment
                      ?.elementAt(index + 1)
                      .studentReason !=
                  null,
            );
          },
        )
      ],
    );
  }

  Widget _wTicketDetailsCard(BuildContext context) {
    return _helpdeskState.getSelectedTicketDetails == null
        ? const Center(child: Text('Error in loading more info'))
        : TicketComponent.ticketDetails(
            ticketData: _helpdeskState
                .convertToTicketData(_helpdeskState.getSelectedTicketDetails!),
            ticketStatusEnum:
                _helpdeskState.getSelectedTicketDetails!.getTicketStatus(),
          );
  }

  Widget _wCommets(BuildContext context) {
    return CommetsComponents(
        comment: _helpdeskState
                .getSelectedTicketDetails?.comment?.first.studentReason ??
            'NA',
        dateString: _helpdeskState
                .getSelectedTicketDetails?.comment?.first.createdDate
                ?.toDateStrNumberFormat() ??
            'NA',
        timeString: _helpdeskState
                .getSelectedTicketDetails?.comment?.first.createdDate
                ?.toTimeShortString() ??
            'NA');
  }

  Widget _wReplyBox(BuildContext context) {
    return 
    
    
    
    Container(
      color: DarkThemeColors.scaffoldBackgroundColor,
      child: Column(
        children: [
          Container(
              margin: const EdgeInsets.symmetric(horizontal: kBaseSize, vertical: 0),
             
              //margin: const EdgeInsets.all(kBaseSize),
              height: 48,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                      child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(8.0)),
                      color: DarkThemeColors.white.withOpacity(0.07),
                    ),
                    child: Stack(
                    
                      children: [TextFormField(
                          decoration: InputDecoration(
                            prefixIcon: const SizedBox(height: 20, width: 20),
                        fillColor: Colors.yellow,
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: DarkThemeColors.white.withOpacity(0.48),
                              width: 0.4),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8.0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: DarkThemeColors.white.withOpacity(0.48),
                              width: 0.8),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8.0)),
                        ),
                        border: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: DarkThemeColors.white.withOpacity(0.48),
                              width: 0.4),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8.0)),
                        ),
                      ),
                      ),
                      GestureDetector(
                        onTap: () {},
                        child: Container(
                        height: 48,
                        width: 48,
                        alignment: Alignment.center,
                        child: Assets.icon.attachmentIcon.svg(
                          height: 20, width: 20
                        ),
                      ),
                      )
                      ]
                    ),
                  )),
                  const BH(kBaseSize - 5), // 11

                  Container(
                    height: 48,
                    width: 48,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: DarkThemeColors.white.withOpacity(0.07),
                    ),
                    child: Center(
                      child: Assets.icon.sendReplyIcon.svg(),
                    ),
                  )
                ],
              )),
          const BR(kBaseSize)
        ],
      ),
    );
  }
}
