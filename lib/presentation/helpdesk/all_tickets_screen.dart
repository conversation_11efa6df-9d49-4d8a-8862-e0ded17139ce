import 'package:component/background/app_scaffold.dart';
import 'package:component/bottom_sheets/single_select_bottom_sheet.dart';
import 'package:component/placeholders/bh.dart';
import 'package:component/placeholders/br.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/core/app_router/routes.dart';
import 'package:miles_masterclass/core/services/scaffold_service.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/filter_drop_down.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/hepdesk_appbar.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/helpdesk_ticket_componet.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_ticket/helpdesk_ticket.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/ticket_status_component.dart';
import 'package:resources/constant/l.dart';
import 'package:resources/constant/size_constant.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';
import 'package:resources/utils/extensions.dart';
import 'package:dependencies/dependencies.dart';

class AllTicketsScreen extends StatefulWidget {
  const AllTicketsScreen({super.key});

  @override
  State<AllTicketsScreen> createState() => _AllTicketsScreenState();
}

class _AllTicketsScreenState extends State<AllTicketsScreen> {
  late HelpdeskScreenState _helpdeskState;
  // final List<String> _ticketStatusOptions = [
  //   'All Tickets',
  //   'Open Ticket',
  //   'Closed Ticket',
  //   'Escalated Ticket',
  // ];

  final List<String> _ticketStatusOptions = TicketStatusEnum.values.map((element) => element.ticketStatusForFilters).toList();

  final List<String> _durationOptions = [
    '3 Months',
    '6 Months',
    '1 Year',
  ];

  @override
  void initState() {
    super.initState();
    _helpdeskState = sl<HelpdeskScreenState>();
    // Fetch tickets if not already loaded
    // if (_helpdeskState.tickets == null || _helpdeskState.tickets!.isEmpty) {
    //   _fetchAllTickets();
    // }
  }

  void _fetchAllTickets() {
    // Example payload - you can modify these values as needed
    _helpdeskState.fetchAllTickets(0, 'D-35024', 'mobileapp');
  }

  void _onTapTicket(BuildContext context, HelpdeskTicket ticket) {
    _helpdeskState.setSelectedTicketForDetails(ticket);
                  context.push(Routes.helpdeskTicketDetails);
  }

    void _showDurationFilterBottomSheet() {
    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: L.t(''),
        options: _durationOptions,
        onApplyBtnPressed: (int value) {
          _helpdeskState.setSelectedDuration(value);
        },
        selectedValueIndex: _helpdeskState.selectedDuration + 1,
      ),
    );
  }


  void _showTicketStatusBottomSheet() {
    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: L.t(''),
        options: _ticketStatusOptions,
        onApplyBtnPressed: (int value) {
          _helpdeskState.setSelectedTicketStatusForAllTicketsScreen(value);
        },
        selectedValueIndex: _helpdeskState.selectedTicketStatus + 1,

      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: DarkThemeColors.scaffoldBackgroundColor,
      body: _wBody(context),
    );
  }

  Widget _wBody(BuildContext context) {
    return Observer(
      builder: (context) {
        return CustomScrollView(
          slivers: [
            SliverCustomAppBar.helpdeskAppBar(title: 'All Tickets'),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(kBaseSize),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'All Tickets',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: DarkThemeColors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const BR(kBaseSize),
                    Row(children: [FilterDropDown(text: _ticketStatusOptions.elementAt(_helpdeskState.selectedTicketStatus), onTap: _showTicketStatusBottomSheet),
                    const BH(kBaseSize),
                    
                    // FilterDropDown(text: _durationOptions.elementAt(_helpdeskState.selectedDuration), onTap: _showDurationFilterBottomSheet), // DO NOT REMOVE
                    ],
                    
                    ),
                    //const SizedBox(height: kBaseSize),
                   _wTicketsList(),
                  ],
                ),
              ),
            ),
          ],
        );
      }
    );
  }

  Widget _wTicketsList() {
    return Observer(
      builder: (context) {
        // Use filtered tickets for all_tickets_screen, fallback to main tickets
        final tickets = _helpdeskState.filteredTickets ?? _helpdeskState.tickets;

        if (tickets == null) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (tickets.isEmpty) {
          return Center(
            child: Column(
              children: [
                const SizedBox(height: kBaseSize * 2),
                Text(
                  'No tickets found',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: DarkThemeColors.white,
                  ),
                ),
                const SizedBox(height: kBaseSize),
                Text(
                  'You haven\'t raised any tickets yet.',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: DarkThemeColors.blueTextColor,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: tickets.length,
          separatorBuilder: (context, index) => const SizedBox(height: kBaseSize),
          itemBuilder: (context, index) {
            final ticket = tickets[index];
            return _wTicketItem(ticket);
          },
        );
      },
    );
  }

  Widget _wTicketItem(HelpdeskTicket ticket) {
    return TicketComponent.allTickets(
      ticketData: _convertToTicketData(ticket),
      onViewDetails:() => _onTapTicket(context, ticket),
      ticketStatusEnum: ticket.getTicketStatus(),
    );
  }

  TicketData _convertToTicketData(HelpdeskTicket ticket) {
    return TicketData(
      id: ticket.helpdeskId ?? 0,
      category: ticket.category ?? 'NA',
      subCategory: ticket.subCategory ?? 'NA',
      issueRelated: ticket.issueRaised ?? 'NA',
      raisedOn: ticket.issueRaisedDate?.toDateStrNumberFormat() ?? 'NA',
      updatedOn: ticket.updatedDate?.toDateStrNumberFormat() ?? 'NA',
      lastComment: ticket.comment?.isNotEmpty == true
          ? (ticket.comment!.last.adminComment ?? ticket.comment!.last.studentAdditionalComment ?? 'NA')
          : 'NA',
    );
  }
}
