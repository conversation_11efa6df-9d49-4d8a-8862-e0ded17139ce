import 'package:component/background/app_scaffold.dart';
import 'package:component/component.dart';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/material.dart';
import 'package:miles_masterclass/core/app_router/routes.dart';
import 'package:miles_masterclass/core/services/scaffold_service.dart';
import 'package:miles_masterclass/presentation/helpdesk/all_tickets_screen.dart';
import 'package:miles_masterclass/presentation/helpdesk/store/helpdesk_screen_state.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/chat_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/commets_components.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/dropdown_example_usage_delete.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/filter_drop_down.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/helpdesk_ticket_componet.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/raise_ticket_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/ticket_status_component.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/ticket_timeline_component.dart';
import 'package:resources/assets/assets.gen.dart';
import 'package:resources/constant/index.dart';
import 'package:resources/themes/colors/dark_theme_colors.dart';
import 'package:resources/utils/extensions.dart';

class HelpdeskScreen extends StatefulWidget {
  const HelpdeskScreen({super.key});

  @override
  State<HelpdeskScreen> createState() => _HelpdeskScreenState();
}

class _HelpdeskScreenState extends State<HelpdeskScreen> {
  final HelpdeskScreenState _state = sl<HelpdeskScreenState>();
  RaiseTicketComponent? _raiseTicketComponent;

  @override
  void initState() {
    super.initState();
    _state.init();
  }

  @override
  void dispose() {
    _state.dispose();
    // Dispose the raise ticket component when leaving the helpdesk screen
    _raiseTicketComponent = null;
    super.dispose();
  }

  void _showDurationFilterBottomSheet() {
    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: L.t(''),
        options: const [
          '3 Months',
          '6 Months',
          '1 Year',
        ],
        onApplyBtnPressed: (int value) {},
      
      ),
    );
  }

  void _showTicketStatusBottomSheet() {
    sl<ScaffoldService>().showBottomSheet(
      context,
      SingleSelectBottomSheet(
        heading: L.t(''),
        options: const [
          'All Tickets',
          'Open Ticket',
          'Closed Ticket',
          'Escalated Ticket',
        ],
        onApplyBtnPressed: (int value) {},
      ),
    );
  }

  void _showRaiseTicketBottomSheet() {
    // Create the component only once and reuse it to preserve state
    _raiseTicketComponent ??= RaiseTicketComponent(
      onTicketSubmitted: _onTicketSubmitted,
      onDispose: _onRaiseTicketDispose,
    );

    sl<ScaffoldService>().showBottomSheet(
      context,
      _raiseTicketComponent!,
    );
  }

  // Called when ticket is successfully submitted
  void _onTicketSubmitted() {
    // Refresh the tickets to update the recent ticket component
    _state.fetchAllTickets(0, 'D-35024', 'mobileapp'); // TODO: Get actual canId from user service

    // Dispose the raise ticket component to reset its state
    _raiseTicketComponent = null;
  }

  // Called when the raise ticket component should be disposed
  void _onRaiseTicketDispose() {
    _raiseTicketComponent = null;
  }

  @override
  Widget build(BuildContext context) {
    return Observer(
      builder: (context) {
        return AppScaffold(
          appBar: PreferredSize(
            preferredSize: const Size(double.infinity, 98),
            child: Container(
              padding: const EdgeInsets.only(
                  left: kBaseSize, right: kBaseSize, top: 50),
              //height: 100,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Assets.icon.helpdeskIcon.svg(),
                      const BH(kBaseSize - 6),
                      Text(
                        L.t('Helpdesk'),
                        style: Theme.of(context).textTheme.headlineLarge,
                      ),
                    ],
                  ),
                  CircularIconBackground(
                    padding: const EdgeInsets.all(kBaseSize / 2),
                    onPressed: () {
                      context.pop();
                    },
                    child: Assets.icon.crossSvg.svg(
                      height: L.h(18),
                      width: L.w(18),
                    ),
                  ),
                ],
              ),
            ),
          ),
          backgroundColor: DarkThemeColors.scaffoldBackgroundColor,
          body: _wNewBody(context),
          // body: _wBuildExample(context),
        );
      },
    );
  }

  Widget _wNewBody(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.all(kBaseSize),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  children: [
                    _state.tickets == null ? const Center(child: CircularProgressIndicator()) :
                    _wRecentTicket(context,  _state.recentTicket != null),
                    const BR(kBaseSize * 1.5),
                    _wHowItWorks(context),
                  ],
                ),
              ],
            ),
            _wRaiseATicket(context)
          ],
        ));
  }

  Widget _wBody(BuildContext context) {
    return CustomScrollView(slivers: [
      _buildSliverAppBar(context),
      SliverToBoxAdapter(
        child: Padding(
            padding: const EdgeInsets.all(kBaseSize),
            child: Stack(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      children: [
                        _wRecentTicket(context, _state.recentTicket != null),
                        const BR(kBaseSize * 1.5),
                        _wHowItWorks(context),
                      ],
                    ),
                  ],
                ),
                Positioned(
                  bottom: 10,
                  child: _wRaiseATicket(context),
                )
              ],
            )),
      )
    ]);
  }

  Widget _wRecentTicket(BuildContext context, bool isRecentTicketExists) {
    return isRecentTicketExists
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Recent Ticket',
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(color: DarkThemeColors.gray30)),
                  Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8.0),
                        splashColor: DarkThemeColors.gray30.withOpacity(0.05),
                        onTap: () {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      const AllTicketsScreen()));
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.0)),
                          margin: const EdgeInsets.all(8),
                          child: Text(
                            'View All',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                    color: DarkThemeColors.blueTextColor),
                          ),
                        ),
                      )),
                ],
              ),
              TicketComponent.recentTicket(
                ticketStatusEnum: _state.recentTicket?.getTicketStatus() ?? TicketStatusEnum.escalated,
                onViewDetails: () {
                  _state.setSelectedTicketForDetails(_state.recentTicket);
                  context.push(Routes.helpdeskTicketDetails);
                },
                ticketData: TicketData(
                  id: _state.recentTicket?.helpdeskId ?? 0,
                  category: _state.recentTicket?.category ?? 'NA',
                  subCategory: _state.recentTicket?.subCategory ?? 'NA',
                  issueRelated: _state.recentTicket?.issueRaised ?? 'NA',
                  raisedOn: _state.recentTicket?.issueRaisedDate
                          ?.toDateStrNumberFormat() ??
                      'NA',
                  updatedOn:
                      _state.recentTicket?.updatedDate?.toDateStrNumberFormat() ??
                          'NA',
                  lastComment:
                      _state.recentTicket?.comment?.last.adminComment ?? 'NA',
                ),
              )
            ],
          )
        : TicketComponent.noOpenTicket(
            onViewPreviousTickets: () {
              context.push(Routes.helpdeskAllTickets);
            },
          );
  }

  Widget _wHowItWorks(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('How it Works',
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(color: DarkThemeColors.gray30)),
        TicketTimelineComponent(),
      ],
    );
  }

  Widget _wRaiseATicket(BuildContext context) {
    return AppButton(
        text: 'Raise a Ticket', onPressed: _showRaiseTicketBottomSheet);
  }

  Widget _wBuildExample(BuildContext context) {
    return CustomScrollView(
      slivers: [
        _buildSliverAppBar(context),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(kBaseSize),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    FilterDropDown(
                      text: 'All Tickets',
                      onTap: () => _showTicketStatusBottomSheet(),
                    ),
                    const BH(kBaseSize / 2),
                    FilterDropDown(
                      text: 'Past 3 Month',
                      onTap: () => _showDurationFilterBottomSheet(),
                    ),
                  ],
                ),
                const BR(kBaseSize / 2),
                TicketComponent.noOpenTicket(
                  onViewPreviousTickets: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                const DropdownExampleUsage()));
                  },
                ),
                const BR(kBaseSize),
                AppButton(
                    text: 'Raise a Ticket',
                    onPressed: _showRaiseTicketBottomSheet),
                const BR(kBaseSize),
                Text('All Tickets'),

                TicketComponent.allTickets(
                  ticketData: TicketData(
                      id: 0,
                      category: 'something',
                      subCategory: 'something',
                      issueRelated:
                          'Not able to attend masterclass which is already paid',
                      raisedOn: '12 May 2024',
                      updatedOn: '12 May 2024',
                      lastComment:
                          'Yes, There was a glitch and the problem is being looked into, Thanks for your.......'),
                  onViewDetails: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) =>
                                const DropdownExampleUsage()));
                  },
                  ticketStatusEnum: TicketStatusEnum.open,
                ),

                const BR(kBaseSize),
                Text('Ticket Details'),
                TicketComponent.ticketDetails(
                  ticketData: TicketData(
                      id: 0,
                      category: 'something',
                      subCategory: 'something',
                      issueRelated:
                          'Not able to attend masterclass which is already paid',
                      raisedOn: '12 May 2024',
                      updatedOn: '12 May 2024',
                      lastComment:
                          'Yes, There was a glitch and the problem is being looked into, Thanks for your.......'),
                  ticketStatusEnum: TicketStatusEnum.closed,
                ),
                const BR(kBaseSize),
                Text('Recent Ticket View'),
                TicketComponent.recentTicket(
                  ticketStatusEnum: TicketStatusEnum.escalated,
                  onViewDetails: () {},
                  ticketData: TicketData(
                      id: 0,
                      category: 'something',
                      subCategory: 'something',
                      issueRelated:
                          'Not able to attend masterclass which is already paid',
                      raisedOn: '12 May 2024',
                      updatedOn: '12 May 2024',
                      lastComment:
                          'Yes, There was a glitch and the problem is being looked into, Thanks for your.......'),
                ),

                TicketTimelineComponent(),
                const BR(kBaseSize),
                const CommetsComponents(
                    comment:
                        'Yes, There was a glitch and the problem is being looked into, Thanks for your patience.',
                    dateString: '22/02/2024',
                    timeString: '11:10'),
                const BR(kBaseSize),
                const ChatComponent(
                    name: 'Sanajan',
                    dateString: '22/02/2024',
                    timeString: '11:10',
                    message:
                        'Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.'),
                const BR(kBaseSize),
                const ChatComponent(
                    isSelf: true,
                    name: 'NA',
                    dateString: '22/02/2024',
                    timeString: '11:10',
                    message:
                        'Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.')

                // const HelpdeskSearchBar(),
                // const SizedBox(height: kBaseSize),
                // const HelpdeskHeaderComponent(),
                // const SizedBox(height: kBaseSize),
                // const HelpdeskCategoryList(),
                // const SizedBox(height: kBaseSize * 2),
                // const RecentTicketsComponent(),
                // const SizedBox(height: kBaseSize * 4),
              ],
            ),
          ),
        ),
      ],
    );
  }

  SliverAppBar _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      //backgroundColor: DarkThemeColors.scaffoldBackgroundColor,
      elevation: 0,
      stretch: true,
      pinned: true,
      floating: true,
      snap: true,
      automaticallyImplyLeading: false,
      actions: [
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: SizedBox(
              width: MediaQuery.of(context).size.width - 32,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Assets.icon.helpdeskIcon.svg(),
                  const BH(10),
                  Text('Helpdesk',
                      style: Theme.of(context).textTheme.headlineLarge),
                  const Spacer(),
                  CircularIconBackground(
                    padding: const EdgeInsets.all(kBaseSize / 2),
                    onPressed: () {
                      context.pop();
                    },
                    child: Assets.icon.crossSvg.svg(
                      height: L.h(18),
                      width: L.w(18),
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }
}
