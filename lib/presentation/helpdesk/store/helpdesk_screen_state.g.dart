// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'helpdesk_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$HelpdeskScreenState on _HelpdeskScreenState, Store {
  Computed<HelpdeskTicket?>? _$getSelectedTicketDetailsComputed;

  @override
  HelpdeskTicket? get getSelectedTicketDetails =>
      (_$getSelectedTicketDetailsComputed ??= Computed<HelpdeskTicket?>(
              () => super.getSelectedTicketDetails,
              name: '_HelpdeskScreenState.getSelectedTicketDetails'))
          .value;
  Computed<HelpdeskTicket?>? _$recentTicketComputed;

  @override
  HelpdeskTicket? get recentTicket => (_$recentTicketComputed ??=
          Computed<HelpdeskTicket?>(() => super.recentTicket,
              name: '_HelpdeskScreenState.recentTicket'))
      .value;

  late final _$_appUserAtom =
      Atom(name: '_HelpdeskScreenState._appUser', context: context);

  AppUser? get appUser {
    _$_appUserAtom.reportRead();
    return super._appUser;
  }

  @override
  AppUser? get _appUser => appUser;

  @override
  set _appUser(AppUser? value) {
    _$_appUserAtom.reportWrite(value, super._appUser, () {
      super._appUser = value;
    });
  }

  late final _$_categoriesAtom =
      Atom(name: '_HelpdeskScreenState._categories', context: context);

  List<HelpdeskCategory>? get categories {
    _$_categoriesAtom.reportRead();
    return super._categories;
  }

  @override
  List<HelpdeskCategory>? get _categories => categories;

  @override
  set _categories(List<HelpdeskCategory>? value) {
    _$_categoriesAtom.reportWrite(value, super._categories, () {
      super._categories = value;
    });
  }

  late final _$_subCategoriesAtom =
      Atom(name: '_HelpdeskScreenState._subCategories', context: context);

  List<HelpdeskSubCategory>? get subCategories {
    _$_subCategoriesAtom.reportRead();
    return super._subCategories;
  }

  @override
  List<HelpdeskSubCategory>? get _subCategories => subCategories;

  @override
  set _subCategories(List<HelpdeskSubCategory>? value) {
    _$_subCategoriesAtom.reportWrite(value, super._subCategories, () {
      super._subCategories = value;
    });
  }

  late final _$_questionsAtom =
      Atom(name: '_HelpdeskScreenState._questions', context: context);

  List<HelpdeskQuestion>? get questions {
    _$_questionsAtom.reportRead();
    return super._questions;
  }

  @override
  List<HelpdeskQuestion>? get _questions => questions;

  @override
  set _questions(List<HelpdeskQuestion>? value) {
    _$_questionsAtom.reportWrite(value, super._questions, () {
      super._questions = value;
    });
  }

  late final _$_ticketsAtom =
      Atom(name: '_HelpdeskScreenState._tickets', context: context);

  List<HelpdeskTicket>? get tickets {
    _$_ticketsAtom.reportRead();
    return super._tickets;
  }

  @override
  List<HelpdeskTicket>? get _tickets => tickets;

  @override
  set _tickets(List<HelpdeskTicket>? value) {
    _$_ticketsAtom.reportWrite(value, super._tickets, () {
      super._tickets = value;
    });
  }

  late final _$_selectedTicketForDetailsAtom = Atom(
      name: '_HelpdeskScreenState._selectedTicketForDetails', context: context);

  HelpdeskTicket? get selectedTicketForDetails {
    _$_selectedTicketForDetailsAtom.reportRead();
    return super._selectedTicketForDetails;
  }

  @override
  HelpdeskTicket? get _selectedTicketForDetails => selectedTicketForDetails;

  @override
  set _selectedTicketForDetails(HelpdeskTicket? value) {
    _$_selectedTicketForDetailsAtom
        .reportWrite(value, super._selectedTicketForDetails, () {
      super._selectedTicketForDetails = value;
    });
  }

  late final _$_selectedTicketStatusAtom = Atom(
      name: '_HelpdeskScreenState._selectedTicketStatus', context: context);

  int get selectedTicketStatus {
    _$_selectedTicketStatusAtom.reportRead();
    return super._selectedTicketStatus;
  }

  @override
  int get _selectedTicketStatus => selectedTicketStatus;

  @override
  set _selectedTicketStatus(int value) {
    _$_selectedTicketStatusAtom.reportWrite(value, super._selectedTicketStatus,
        () {
      super._selectedTicketStatus = value;
    });
  }

  late final _$_selectedDurationAtom =
      Atom(name: '_HelpdeskScreenState._selectedDuration', context: context);

  int get selectedDuration {
    _$_selectedDurationAtom.reportRead();
    return super._selectedDuration;
  }

  @override
  int get _selectedDuration => selectedDuration;

  @override
  set _selectedDuration(int value) {
    _$_selectedDurationAtom.reportWrite(value, super._selectedDuration, () {
      super._selectedDuration = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_HelpdeskScreenState.init', context: context);

  @override
  Future<void> init() {
    return _$initAsyncAction.run(() => super.init());
  }

  late final _$_fetchUserProfileAsyncAction =
      AsyncAction('_HelpdeskScreenState._fetchUserProfile', context: context);

  @override
  Future<void> _fetchUserProfile() {
    return _$_fetchUserProfileAsyncAction.run(() => super._fetchUserProfile());
  }

  late final _$_fetchHelpdeskCategoriesAsyncAction = AsyncAction(
      '_HelpdeskScreenState._fetchHelpdeskCategories',
      context: context);

  @override
  Future<void> _fetchHelpdeskCategories() {
    return _$_fetchHelpdeskCategoriesAsyncAction
        .run(() => super._fetchHelpdeskCategories());
  }

  late final _$fetchSubCategoriesAsyncAction =
      AsyncAction('_HelpdeskScreenState.fetchSubCategories', context: context);

  @override
  Future<void> fetchSubCategories(int categoryId) {
    return _$fetchSubCategoriesAsyncAction
        .run(() => super.fetchSubCategories(categoryId));
  }

  late final _$fetchQuestionsAsyncAction =
      AsyncAction('_HelpdeskScreenState.fetchQuestions', context: context);

  @override
  Future<void> fetchQuestions(int questionId, int categoryId) {
    return _$fetchQuestionsAsyncAction
        .run(() => super.fetchQuestions(questionId, categoryId));
  }

  late final _$fetchAllTicketsAsyncAction =
      AsyncAction('_HelpdeskScreenState.fetchAllTickets', context: context);

  @override
  Future<void> fetchAllTickets(int ticketType, String canId, String course) {
    return _$fetchAllTicketsAsyncAction
        .run(() => super.fetchAllTickets(ticketType, canId, course));
  }

  late final _$reopenTicketAsyncAction =
      AsyncAction('_HelpdeskScreenState.reopenTicket', context: context);

  @override
  Future<void> reopenTicket(String canId, int helpdeskId, String reason) {
    return _$reopenTicketAsyncAction
        .run(() => super.reopenTicket(canId, helpdeskId, reason));
  }

  late final _$escalateTicketAsyncAction =
      AsyncAction('_HelpdeskScreenState.escalateTicket', context: context);

  @override
  Future<void> escalateTicket(String canId, int helpdeskId) {
    return _$escalateTicketAsyncAction
        .run(() => super.escalateTicket(canId, helpdeskId));
  }

  late final _$uploadFileAsyncAction =
      AsyncAction('_HelpdeskScreenState.uploadFile', context: context);

  @override
  Future<String?> uploadFile(File file) {
    return _$uploadFileAsyncAction.run(() => super.uploadFile(file));
  }

  late final _$raiseQueryAsyncAction =
      AsyncAction('_HelpdeskScreenState.raiseQuery', context: context);

  @override
  Future<int?> raiseQuery(
      {required int categoryId,
      required int questionId,
      required List<int> subQuestionIds,
      required String helpdesk,
      required List<String> imageUrls,
      required String uniqueId,
      required String name,
      required String email,
      int? cityId}) {
    return _$raiseQueryAsyncAction.run(() => super.raiseQuery(
        categoryId: categoryId,
        questionId: questionId,
        subQuestionIds: subQuestionIds,
        helpdesk: helpdesk,
        imageUrls: imageUrls,
        uniqueId: uniqueId,
        name: name,
        email: email,
        cityId: cityId));
  }

  late final _$_HelpdeskScreenStateActionController =
      ActionController(name: '_HelpdeskScreenState', context: context);

  @override
  void _setAppUser(AppUser appUser) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState._setAppUser');
    try {
      return super._setAppUser(appUser);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setHelpdeskCategories(List<HelpdeskCategory> categories) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState._setHelpdeskCategories');
    try {
      return super._setHelpdeskCategories(categories);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setSubCategories(List<HelpdeskSubCategory> subCategories) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState._setSubCategories');
    try {
      return super._setSubCategories(subCategories);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearSubCategories() {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.clearSubCategories');
    try {
      return super.clearSubCategories();
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setQuestions(List<HelpdeskQuestion> questions) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState._setQuestions');
    try {
      return super._setQuestions(questions);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearQuestions() {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.clearQuestions');
    try {
      return super.clearQuestions();
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setTickets(List<HelpdeskTicket> tickets) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState._setTickets');
    try {
      return super._setTickets(tickets);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void clearTickets() {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.clearTickets');
    try {
      return super.clearTickets();
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSelectedTicketForDetails(HelpdeskTicket? ticket) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.setSelectedTicketForDetails');
    try {
      return super.setSelectedTicketForDetails(ticket);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSelectedTicketStatus(int status) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.setSelectedTicketStatus');
    try {
      return super.setSelectedTicketStatus(status);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSelectedDuration(int duration) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.setSelectedDuration');
    try {
      return super.setSelectedDuration(duration);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  TicketData convertToTicketData(HelpdeskTicket ticket) {
    final _$actionInfo = _$_HelpdeskScreenStateActionController.startAction(
        name: '_HelpdeskScreenState.convertToTicketData');
    try {
      return super.convertToTicketData(ticket);
    } finally {
      _$_HelpdeskScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
getSelectedTicketDetails: ${getSelectedTicketDetails},
recentTicket: ${recentTicket}
    ''';
  }
}
