import 'package:dependencies/dependencies.dart';
import 'package:flutter/foundation.dart';
import 'package:miles_masterclass/core/utils/async_progress_manager.dart';
import 'package:miles_masterclass/core/stores/base_store.dart';
import 'package:miles_masterclass/data/repositories/auth_repository.dart';
import 'package:miles_masterclass/data/repositories/helpdesk_repository.dart';
import 'package:miles_masterclass/domain/entities/auth/app_user/app_user.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_category/helpdesk_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_sub_category/helpdesk_sub_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_question/helpdesk_question.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_ticket/helpdesk_ticket.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/category_by_course_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/sub_category_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/questions_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/all_tickets_request.dart';
import 'package:miles_masterclass/presentation/helpdesk/widgets/helpdesk_ticket_componet.dart';
import 'package:resources/utils/extensions.dart';
part 'helpdesk_screen_state.g.dart';


class HelpdeskScreenState = _HelpdeskScreenState with _$HelpdeskScreenState;

abstract class _HelpdeskScreenState extends BaseStore
    with Store, AsyncProgressManager {
  final _authRepository = sl<AuthRepository>();
  final _helpdeskRepository = sl<HelpdeskRepository>();

  @readonly
  AppUser? _appUser;

  @readonly
  List<HelpdeskCategory>? _categories;

  @readonly
  List<HelpdeskSubCategory>? _subCategories;

  @readonly
  List<HelpdeskQuestion>? _questions;

  @readonly
  List<HelpdeskTicket>? _tickets;

  @readonly
  HelpdeskTicket? _selectedTicketForDetails;

  @readonly
  int _selectedTicketStatus = 0;

  @readonly
  int _selectedDuration = 0;





  @computed
  HelpdeskTicket? get getSelectedTicketDetails => _selectedTicketForDetails;

  @computed
  HelpdeskTicket? get recentTicket {
    if (_tickets == null) return null;

    if(_tickets!.isEmpty) return null;
    
    final validTickets = _tickets!
        .where((ticket) => ticket.issueRaisedDate != null)
        .toList()
      ..sort((a, b) => b.issueRaisedDate!.compareTo(a.issueRaisedDate!));
    
    return validTickets.isNotEmpty ? validTickets.first : null;
  }

  @action
  Future<void> init() async {
     if (kDebugMode) {
      print('HelpdeskScreenState Created');
    }
    _fetchUserProfile();
    _fetchHelpdeskCategories();
    // fetchQuestions(171, 24);
    fetchAllTickets(0, 'D-35024', 'mobileapp');
  }

 @override
  void dispose() {
    if (kDebugMode) {
      print('HelpdeskScreenState disposed');
    }
    super.dispose();
  }

  @action
  Future<void> _fetchUserProfile() async {
    return handleFutureEvent(
      future: _authRepository.getAppUser(),
      eventKey: 'fetch-user-profile',
      onData: _setAppUser,
    );
  }

  @action
  void _setAppUser(AppUser appUser) {
    _appUser = appUser;
  }

  @action
  Future<void> _fetchHelpdeskCategories() async {
    return handleFutureEvent(
      future: _helpdeskRepository.categoryByCourse(const CategoryByCourseRequest(course: ['mobileapp'])),
      eventKey: 'fetch-helpdesk-categories',
      onData: (categories) => _setHelpdeskCategories(categories)
    );
  }

  @action
  void _setHelpdeskCategories(List<HelpdeskCategory> categories) {
    _categories = categories;
  }

  @action
  Future<void> fetchSubCategories(int categoryId) async {
    return handleFutureEvent(
      future: _helpdeskRepository.subCategory(SubCategoryRequest(categoryId: [categoryId])),
      eventKey: 'fetch-sub-categories',
      onData: (subCategories) => _setSubCategories(subCategories),
    );
  }

  @action
  void _setSubCategories(List<HelpdeskSubCategory> subCategories) {
    _subCategories = subCategories;
  }

  @action
  void clearSubCategories() {
    _subCategories = null;
  }

  @action
  Future<void> fetchQuestions(int questionId, int categoryId) async {
    return handleFutureEvent(
      future: _helpdeskRepository.questions(QuestionsRequest(
        questionId: questionId,
        categoryId: categoryId,
      )),
      eventKey: 'fetch-questions',
      onData: (questions) => _setQuestions(questions),
    );
  }

  @action
  void _setQuestions(List<HelpdeskQuestion> questions) {
    _questions = questions;
  }

  @action
  void clearQuestions() {
    _questions = null;
  }

  @action
  Future<void> fetchAllTickets(int ticketType, String canId, String course) async {
    return handleFutureEvent(
      future: _helpdeskRepository.allTickets(AllTicketsRequest(
        ticketType: ticketType,
        canId: canId,
        course: course,
      )),
      eventKey: 'fetch-all-tickets',
      onData: (tickets) => _setTickets(tickets),
    );
  }

  @action
  void _setTickets(List<HelpdeskTicket> tickets) {
    _tickets = tickets;
  }

  @action
  void clearTickets() {
    _tickets = null;
  }

  @action
  void setSelectedTicketForDetails(HelpdeskTicket? ticket) {
    _selectedTicketForDetails = ticket;
  }

  @action
  void setSelectedTicketStatus(int status) {
    clearTickets();
    fetchAllTickets(status, 'D-35024', 'mobileapp');
    _selectedTicketStatus = status;

  }

  @action
  void setSelectedDuration(int duration) {
    _selectedDuration = duration;
  }

  @action
  TicketData convertToTicketData(HelpdeskTicket ticket) {
    return TicketData(
      id: ticket.helpdeskId ?? 0,
      category: ticket.category ?? 'NA',
      subCategory: ticket.subCategory ?? 'NA',
      issueRelated: ticket.issueRaised ?? 'NA',
      raisedOn: ticket.issueRaisedDate?.toDateStrNumberFormat() ?? 'NA',
      updatedOn: ticket.updatedDate?.toDateStrNumberFormat() ?? 'NA',
      lastComment: ticket.comment?.isNotEmpty == true
          ? (ticket.comment!.last.adminComment ?? ticket.comment!.last.studentAdditionalComment ?? 'NA')
          : 'NA',
    );
  }
}
