// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$LoginScreenState on _LoginScreenState, Store {
  late final _$_sessionIdAtom =
      Atom(name: '_LoginScreenState._sessionId', context: context);

  int? get sessionId {
    _$_sessionIdAtom.reportRead();
    return super._sessionId;
  }

  @override
  int? get _sessionId => sessionId;

  @override
  set _sessionId(int? value) {
    _$_sessionIdAtom.reportWrite(value, super._sessionId, () {
      super._sessionId = value;
    });
  }

  late final _$_emailAtom =
      Atom(name: '_LoginScreenState._email', context: context);

  String get email {
    _$_emailAtom.reportRead();
    return super._email;
  }

  @override
  String get _email => email;

  @override
  set _email(String value) {
    _$_emailAtom.reportWrite(value, super._email, () {
      super._email = value;
    });
  }

  late final _$_phoneAtom =
      Atom(name: '_LoginScreenState._phone', context: context);

  String get phone {
    _$_phoneAtom.reportRead();
    return super._phone;
  }

  @override
  String get _phone => phone;

  @override
  set _phone(String value) {
    _$_phoneAtom.reportWrite(value, super._phone, () {
      super._phone = value;
    });
  }

  late final _$_countriesAtom =
      Atom(name: '_LoginScreenState._countries', context: context);

  List<CountryDetail> get countries {
    _$_countriesAtom.reportRead();
    return super._countries;
  }

  @override
  List<CountryDetail> get _countries => countries;

  @override
  set _countries(List<CountryDetail> value) {
    _$_countriesAtom.reportWrite(value, super._countries, () {
      super._countries = value;
    });
  }

  late final _$_selectedCountryAtom =
      Atom(name: '_LoginScreenState._selectedCountry', context: context);

  CountryDetail get selectedCountry {
    _$_selectedCountryAtom.reportRead();
    return super._selectedCountry;
  }

  @override
  CountryDetail get _selectedCountry => selectedCountry;

  @override
  set _selectedCountry(CountryDetail value) {
    _$_selectedCountryAtom.reportWrite(value, super._selectedCountry, () {
      super._selectedCountry = value;
    });
  }

  late final _$_isNewUserAtom =
      Atom(name: '_LoginScreenState._isNewUser', context: context);

  bool get isNewUser {
    _$_isNewUserAtom.reportRead();
    return super._isNewUser;
  }

  @override
  bool get _isNewUser => isNewUser;

  @override
  set _isNewUser(bool value) {
    _$_isNewUserAtom.reportWrite(value, super._isNewUser, () {
      super._isNewUser = value;
    });
  }

  late final _$_isSendingLoginRequestAtom =
      Atom(name: '_LoginScreenState._isSendingLoginRequest', context: context);

  bool get isSendingLoginRequest {
    _$_isSendingLoginRequestAtom.reportRead();
    return super._isSendingLoginRequest;
  }

  @override
  bool get _isSendingLoginRequest => isSendingLoginRequest;

  @override
  set _isSendingLoginRequest(bool value) {
    _$_isSendingLoginRequestAtom
        .reportWrite(value, super._isSendingLoginRequest, () {
      super._isSendingLoginRequest = value;
    });
  }

  late final _$_loginMethodAtom =
      Atom(name: '_LoginScreenState._loginMethod', context: context);

  LoginMethod get loginMethod {
    _$_loginMethodAtom.reportRead();
    return super._loginMethod;
  }

  @override
  LoginMethod get _loginMethod => loginMethod;

  @override
  set _loginMethod(LoginMethod value) {
    _$_loginMethodAtom.reportWrite(value, super._loginMethod, () {
      super._loginMethod = value;
    });
  }

  late final _$_isConsentGivenByTheUserAtom = Atom(
      name: '_LoginScreenState._isConsentGivenByTheUser', context: context);

  bool get isConsentGivenByTheUser {
    _$_isConsentGivenByTheUserAtom.reportRead();
    return super._isConsentGivenByTheUser;
  }

  @override
  bool get _isConsentGivenByTheUser => isConsentGivenByTheUser;

  @override
  set _isConsentGivenByTheUser(bool value) {
    _$_isConsentGivenByTheUserAtom
        .reportWrite(value, super._isConsentGivenByTheUser, () {
      super._isConsentGivenByTheUser = value;
    });
  }

  late final _$sendOtpViaEmailAsyncAction =
      AsyncAction('_LoginScreenState.sendOtpViaEmail', context: context);

  @override
  Future<void> sendOtpViaEmail(String email) {
    return _$sendOtpViaEmailAsyncAction.run(() => super.sendOtpViaEmail(email));
  }

  late final _$sendOtpViaPhoneAsyncAction =
      AsyncAction('_LoginScreenState.sendOtpViaPhone', context: context);

  @override
  Future<void> sendOtpViaPhone(String phone, String countryCode) {
    return _$sendOtpViaPhoneAsyncAction
        .run(() => super.sendOtpViaPhone(phone, countryCode));
  }

  late final _$fetchCountriesAsyncAction =
      AsyncAction('_LoginScreenState.fetchCountries', context: context);

  @override
  Future<void> fetchCountries() {
    return _$fetchCountriesAsyncAction.run(() => super.fetchCountries());
  }

  late final _$_LoginScreenStateActionController =
      ActionController(name: '_LoginScreenState', context: context);

  @override
  void setSelectedCountry(int idx) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState.setSelectedCountry');
    try {
      return super.setSelectedCountry(idx);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setEmail(String email) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState.setEmail');
    try {
      return super.setEmail(email);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setPhoneNumber(String phone) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState.setPhoneNumber');
    try {
      return super.setPhoneNumber(phone);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setLoginMethod(LoginMethod method) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState.setLoginMethod');
    try {
      return super.setLoginMethod(method);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setLoginResultData(SendOTPResultData result) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState._setLoginResultData');
    try {
      return super._setLoginResultData(result);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setCountries(List<CountryDetail> countries) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState._setCountries');
    try {
      return super._setCountries(countries);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setUserConsent(bool? value) {
    final _$actionInfo = _$_LoginScreenStateActionController.startAction(
        name: '_LoginScreenState.setUserConsent');
    try {
      return super.setUserConsent(value);
    } finally {
      _$_LoginScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''

    ''';
  }
}
