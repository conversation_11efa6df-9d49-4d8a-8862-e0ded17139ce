// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'otp_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$OtpScreenState on _OtpScreenState, Store {
  late final _$_sessionIdAtom =
      Atom(name: '_OtpScreenState._sessionId', context: context);

  int get sessionId {
    _$_sessionIdAtom.reportRead();
    return super._sessionId;
  }

  @override
  int get _sessionId => sessionId;

  bool __sessionIdIsInitialized = false;

  @override
  set _sessionId(int value) {
    _$_sessionIdAtom.reportWrite(
        value, __sessionIdIsInitialized ? super._sessionId : null, () {
      super._sessionId = value;
      __sessionIdIsInitialized = true;
    });
  }

  late final _$_otpAtom = Atom(name: '_OtpScreenState._otp', context: context);

  String get otp {
    _$_otpAtom.reportRead();
    return super._otp;
  }

  @override
  String get _otp => otp;

  @override
  set _otp(String value) {
    _$_otpAtom.reportWrite(value, super._otp, () {
      super._otp = value;
    });
  }

  late final _$_loginMethodAtom =
      Atom(name: '_OtpScreenState._loginMethod', context: context);

  LoginMethod get loginMethod {
    _$_loginMethodAtom.reportRead();
    return super._loginMethod;
  }

  @override
  LoginMethod get _loginMethod => loginMethod;

  bool __loginMethodIsInitialized = false;

  @override
  set _loginMethod(LoginMethod value) {
    _$_loginMethodAtom.reportWrite(
        value, __loginMethodIsInitialized ? super._loginMethod : null, () {
      super._loginMethod = value;
      __loginMethodIsInitialized = true;
    });
  }

  late final _$_isSendingOtpAtom =
      Atom(name: '_OtpScreenState._isSendingOtp', context: context);

  bool get isSendingOtp {
    _$_isSendingOtpAtom.reportRead();
    return super._isSendingOtp;
  }

  @override
  bool get _isSendingOtp => isSendingOtp;

  @override
  set _isSendingOtp(bool value) {
    _$_isSendingOtpAtom.reportWrite(value, super._isSendingOtp, () {
      super._isSendingOtp = value;
    });
  }

  late final _$_areUserDetailsIncompleteAtom =
      Atom(name: '_OtpScreenState._areUserDetailsIncomplete', context: context);

  bool get areUserDetailsIncomplete {
    _$_areUserDetailsIncompleteAtom.reportRead();
    return super._areUserDetailsIncomplete;
  }

  @override
  bool get _areUserDetailsIncomplete => areUserDetailsIncomplete;

  @override
  set _areUserDetailsIncomplete(bool value) {
    _$_areUserDetailsIncompleteAtom
        .reportWrite(value, super._areUserDetailsIncomplete, () {
      super._areUserDetailsIncomplete = value;
    });
  }

  late final _$_emailAtom =
      Atom(name: '_OtpScreenState._email', context: context);

  String? get email {
    _$_emailAtom.reportRead();
    return super._email;
  }

  @override
  String? get _email => email;

  @override
  set _email(String? value) {
    _$_emailAtom.reportWrite(value, super._email, () {
      super._email = value;
    });
  }

  late final _$_phoneAtom =
      Atom(name: '_OtpScreenState._phone', context: context);

  String? get phone {
    _$_phoneAtom.reportRead();
    return super._phone;
  }

  @override
  String? get _phone => phone;

  @override
  set _phone(String? value) {
    _$_phoneAtom.reportWrite(value, super._phone, () {
      super._phone = value;
    });
  }

  late final _$_countryCodeAtom =
      Atom(name: '_OtpScreenState._countryCode', context: context);

  String? get countryCode {
    _$_countryCodeAtom.reportRead();
    return super._countryCode;
  }

  @override
  String? get _countryCode => countryCode;

  @override
  set _countryCode(String? value) {
    _$_countryCodeAtom.reportWrite(value, super._countryCode, () {
      super._countryCode = value;
    });
  }

  late final _$onResendOtpAsyncAction =
      AsyncAction('_OtpScreenState.onResendOtp', context: context);

  @override
  Future<void> onResendOtp() {
    return _$onResendOtpAsyncAction.run(() => super.onResendOtp());
  }

  late final _$_sendOtpViaEmailAsyncAction =
      AsyncAction('_OtpScreenState._sendOtpViaEmail', context: context);

  @override
  Future<void> _sendOtpViaEmail(String email) {
    return _$_sendOtpViaEmailAsyncAction
        .run(() => super._sendOtpViaEmail(email));
  }

  late final _$_sendOtpViaPhoneAsyncAction =
      AsyncAction('_OtpScreenState._sendOtpViaPhone', context: context);

  @override
  Future<void> _sendOtpViaPhone(String phone, String countryCode) {
    return _$_sendOtpViaPhoneAsyncAction
        .run(() => super._sendOtpViaPhone(phone, countryCode));
  }

  late final _$verifyOtpAsyncAction =
      AsyncAction('_OtpScreenState.verifyOtp', context: context);

  @override
  Future<void> verifyOtp() {
    return _$verifyOtpAsyncAction.run(() => super.verifyOtp());
  }

  late final _$_OtpScreenStateActionController =
      ActionController(name: '_OtpScreenState', context: context);

  @override
  void init(
      {required int sessionId,
      String? email,
      String? phone,
      String? countryCode}) {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState.init');
    try {
      return super.init(
          sessionId: sessionId,
          email: email,
          phone: phone,
          countryCode: countryCode);
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _initLoginMethodData(
      {String? email, String? phone, String? countryCode}) {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState._initLoginMethodData');
    try {
      return super._initLoginMethodData(
          email: email, phone: phone, countryCode: countryCode);
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  Future<void> resendOtp() {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState.resendOtp');
    try {
      return super.resendOtp();
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setOtp(String value) {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState.setOtp');
    try {
      return super.setOtp(value);
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _checkNewUser(LoginResult data) {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState._checkNewUser');
    try {
      return super._checkNewUser(data);
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _updateSessionId(SendOTPResultData data) {
    final _$actionInfo = _$_OtpScreenStateActionController.startAction(
        name: '_OtpScreenState._updateSessionId');
    try {
      return super._updateSessionId(data);
    } finally {
      _$_OtpScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''

    ''';
  }
}
