// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'onboarding_screen_state.dart';

// **************************************************************************
// StoreGenerator
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, unnecessary_brace_in_string_interps, unnecessary_lambdas, prefer_expression_function_bodies, lines_longer_than_80_chars, avoid_as, avoid_annotating_with_dynamic, no_leading_underscores_for_local_identifiers

mixin _$OnboardingScreenState on _OnboardingScreenState, Store {
  Computed<bool>? _$hasErrorComputed;

  @override
  bool get hasError =>
      (_$hasErrorComputed ??= Computed<bool>(() => super.hasError,
              name: '_OnboardingScreenState.hasError'))
          .value;
  Computed<bool>? _$shouldEnableSubmitBtnComputed;

  @override
  bool get shouldEnableSubmitBtn => (_$shouldEnableSubmitBtnComputed ??=
          Computed<bool>(() => super.shouldEnableSubmitBtn,
              name: '_OnboardingScreenState.shouldEnableSubmitBtn'))
      .value;

  late final _$_countriesAtom =
      Atom(name: '_OnboardingScreenState._countries', context: context);

  List<CountryDetail> get countries {
    _$_countriesAtom.reportRead();
    return super._countries;
  }

  @override
  List<CountryDetail> get _countries => countries;

  @override
  set _countries(List<CountryDetail> value) {
    _$_countriesAtom.reportWrite(value, super._countries, () {
      super._countries = value;
    });
  }

  late final _$firstNameAtom =
      Atom(name: '_OnboardingScreenState.firstName', context: context);

  @override
  String get firstName {
    _$firstNameAtom.reportRead();
    return super.firstName;
  }

  @override
  set firstName(String value) {
    _$firstNameAtom.reportWrite(value, super.firstName, () {
      super.firstName = value;
    });
  }

  late final _$lastNameAtom =
      Atom(name: '_OnboardingScreenState.lastName', context: context);

  @override
  String get lastName {
    _$lastNameAtom.reportRead();
    return super.lastName;
  }

  @override
  set lastName(String value) {
    _$lastNameAtom.reportWrite(value, super.lastName, () {
      super.lastName = value;
    });
  }

  late final _$loginMethodAtom =
      Atom(name: '_OnboardingScreenState.loginMethod', context: context);

  @override
  LoginMethod get loginMethod {
    _$loginMethodAtom.reportRead();
    return super.loginMethod;
  }

  @override
  set loginMethod(LoginMethod value) {
    _$loginMethodAtom.reportWrite(value, super.loginMethod, () {
      super.loginMethod = value;
    });
  }

  late final _$emailAtom =
      Atom(name: '_OnboardingScreenState.email', context: context);

  @override
  String get email {
    _$emailAtom.reportRead();
    return super.email;
  }

  @override
  set email(String value) {
    _$emailAtom.reportWrite(value, super.email, () {
      super.email = value;
    });
  }

  late final _$phoneNumberAtom =
      Atom(name: '_OnboardingScreenState.phoneNumber', context: context);

  @override
  String get phoneNumber {
    _$phoneNumberAtom.reportRead();
    return super.phoneNumber;
  }

  @override
  set phoneNumber(String value) {
    _$phoneNumberAtom.reportWrite(value, super.phoneNumber, () {
      super.phoneNumber = value;
    });
  }

  late final _$_countryCodeAtom =
      Atom(name: '_OnboardingScreenState._countryCode', context: context);

  String get countryCode {
    _$_countryCodeAtom.reportRead();
    return super._countryCode;
  }

  @override
  String get _countryCode => countryCode;

  @override
  set _countryCode(String value) {
    _$_countryCodeAtom.reportWrite(value, super._countryCode, () {
      super._countryCode = value;
    });
  }

  late final _$locationAtom =
      Atom(name: '_OnboardingScreenState.location', context: context);

  @override
  String get location {
    _$locationAtom.reportRead();
    return super.location;
  }

  @override
  set location(String value) {
    _$locationAtom.reportWrite(value, super.location, () {
      super.location = value;
    });
  }

  late final _$termsAcceptedAtom =
      Atom(name: '_OnboardingScreenState.termsAccepted', context: context);

  @override
  bool get termsAccepted {
    _$termsAcceptedAtom.reportRead();
    return super.termsAccepted;
  }

  @override
  set termsAccepted(bool value) {
    _$termsAcceptedAtom.reportWrite(value, super.termsAccepted, () {
      super.termsAccepted = value;
    });
  }

  late final _$currentlyWorkingAtom =
      Atom(name: '_OnboardingScreenState.currentlyWorking', context: context);

  @override
  bool get currentlyWorking {
    _$currentlyWorkingAtom.reportRead();
    return super.currentlyWorking;
  }

  @override
  set currentlyWorking(bool value) {
    _$currentlyWorkingAtom.reportWrite(value, super.currentlyWorking, () {
      super.currentlyWorking = value;
    });
  }

  late final _$_companiesAtom =
      Atom(name: '_OnboardingScreenState._companies', context: context);

  List<CompanyDetails> get companies {
    _$_companiesAtom.reportRead();
    return super._companies;
  }

  @override
  List<CompanyDetails> get _companies => companies;

  @override
  set _companies(List<CompanyDetails> value) {
    _$_companiesAtom.reportWrite(value, super._companies, () {
      super._companies = value;
    });
  }

  late final _$_selectedCompanyAtom =
      Atom(name: '_OnboardingScreenState._selectedCompany', context: context);

  CompanyDetails? get selectedCompany {
    _$_selectedCompanyAtom.reportRead();
    return super._selectedCompany;
  }

  @override
  CompanyDetails? get _selectedCompany => selectedCompany;

  @override
  set _selectedCompany(CompanyDetails? value) {
    _$_selectedCompanyAtom.reportWrite(value, super._selectedCompany, () {
      super._selectedCompany = value;
    });
  }

  late final _$_selectedProfessionalCourseAtom = Atom(
      name: '_OnboardingScreenState._selectedProfessionalCourse',
      context: context);

  ProfessionalCourse? get selectedProfessionalCourse {
    _$_selectedProfessionalCourseAtom.reportRead();
    return super._selectedProfessionalCourse;
  }

  @override
  ProfessionalCourse? get _selectedProfessionalCourse =>
      selectedProfessionalCourse;

  @override
  set _selectedProfessionalCourse(ProfessionalCourse? value) {
    _$_selectedProfessionalCourseAtom
        .reportWrite(value, super._selectedProfessionalCourse, () {
      super._selectedProfessionalCourse = value;
    });
  }

  late final _$_selectedStateBoardAtom = Atom(
      name: '_OnboardingScreenState._selectedStateBoard', context: context);

  StateBoard? get selectedStateBoard {
    _$_selectedStateBoardAtom.reportRead();
    return super._selectedStateBoard;
  }

  @override
  StateBoard? get _selectedStateBoard => selectedStateBoard;

  @override
  set _selectedStateBoard(StateBoard? value) {
    _$_selectedStateBoardAtom.reportWrite(value, super._selectedStateBoard, () {
      super._selectedStateBoard = value;
    });
  }

  late final _$_professionalCoursesAtom = Atom(
      name: '_OnboardingScreenState._professionalCourses', context: context);

  List<ProfessionalCourse> get professionalCourses {
    _$_professionalCoursesAtom.reportRead();
    return super._professionalCourses;
  }

  @override
  List<ProfessionalCourse> get _professionalCourses => professionalCourses;

  @override
  set _professionalCourses(List<ProfessionalCourse> value) {
    _$_professionalCoursesAtom.reportWrite(value, super._professionalCourses,
        () {
      super._professionalCourses = value;
    });
  }

  late final _$_stateBoardListAtom =
      Atom(name: '_OnboardingScreenState._stateBoardList', context: context);

  List<StateBoard> get stateBoardList {
    _$_stateBoardListAtom.reportRead();
    return super._stateBoardList;
  }

  @override
  List<StateBoard> get _stateBoardList => stateBoardList;

  @override
  set _stateBoardList(List<StateBoard> value) {
    _$_stateBoardListAtom.reportWrite(value, super._stateBoardList, () {
      super._stateBoardList = value;
    });
  }

  late final _$_updatingUserProfileAtom = Atom(
      name: '_OnboardingScreenState._updatingUserProfile', context: context);

  bool get updatingUserProfile {
    _$_updatingUserProfileAtom.reportRead();
    return super._updatingUserProfile;
  }

  @override
  bool get _updatingUserProfile => updatingUserProfile;

  @override
  set _updatingUserProfile(bool value) {
    _$_updatingUserProfileAtom.reportWrite(value, super._updatingUserProfile,
        () {
      super._updatingUserProfile = value;
    });
  }

  late final _$_experienceAtom =
      Atom(name: '_OnboardingScreenState._experience', context: context);

  String? get experience {
    _$_experienceAtom.reportRead();
    return super._experience;
  }

  @override
  String? get _experience => experience;

  @override
  set _experience(String? value) {
    _$_experienceAtom.reportWrite(value, super._experience, () {
      super._experience = value;
    });
  }

  late final _$_duplicatePhoneEmailErrorMessageAtom = Atom(
      name: '_OnboardingScreenState._duplicatePhoneEmailErrorMessage',
      context: context);

  String? get duplicatePhoneEmailErrorMessage {
    _$_duplicatePhoneEmailErrorMessageAtom.reportRead();
    return super._duplicatePhoneEmailErrorMessage;
  }

  @override
  String? get _duplicatePhoneEmailErrorMessage =>
      duplicatePhoneEmailErrorMessage;

  @override
  set _duplicatePhoneEmailErrorMessage(String? value) {
    _$_duplicatePhoneEmailErrorMessageAtom
        .reportWrite(value, super._duplicatePhoneEmailErrorMessage, () {
      super._duplicatePhoneEmailErrorMessage = value;
    });
  }

  late final _$_pageNumberAtom =
      Atom(name: '_OnboardingScreenState._pageNumber', context: context);

  int get pageNumber {
    _$_pageNumberAtom.reportRead();
    return super._pageNumber;
  }

  @override
  int get _pageNumber => pageNumber;

  @override
  set _pageNumber(int value) {
    _$_pageNumberAtom.reportWrite(value, super._pageNumber, () {
      super._pageNumber = value;
    });
  }

  late final _$initAsyncAction =
      AsyncAction('_OnboardingScreenState.init', context: context);

  @override
  Future<void> init({String? email, String? phoneNumber, String? countryCode}) {
    return _$initAsyncAction.run(() => super.init(
        email: email, phoneNumber: phoneNumber, countryCode: countryCode));
  }

  late final _$_fetchCountriesListAsyncAction = AsyncAction(
      '_OnboardingScreenState._fetchCountriesList',
      context: context);

  @override
  Future<void> _fetchCountriesList() {
    return _$_fetchCountriesListAsyncAction
        .run(() => super._fetchCountriesList());
  }

  late final _$_fetchProfessionalCoursesAsyncAction = AsyncAction(
      '_OnboardingScreenState._fetchProfessionalCourses',
      context: context);

  @override
  Future<void> _fetchProfessionalCourses() {
    return _$_fetchProfessionalCoursesAsyncAction
        .run(() => super._fetchProfessionalCourses());
  }

  late final _$_fetchStateBoardListAsyncAction = AsyncAction(
      '_OnboardingScreenState._fetchStateBoardList',
      context: context);

  @override
  Future<void> _fetchStateBoardList() {
    return _$_fetchStateBoardListAsyncAction
        .run(() => super._fetchStateBoardList());
  }

  late final _$searchCompaniesAsyncAction =
      AsyncAction('_OnboardingScreenState.searchCompanies', context: context);

  @override
  Future<void> searchCompanies(String searchTerm) {
    return _$searchCompaniesAsyncAction
        .run(() => super.searchCompanies(searchTerm));
  }

  late final _$_OnboardingScreenStateActionController =
      ActionController(name: '_OnboardingScreenState', context: context);

  @override
  void _initLoginMethodData(
      {required String? emailId,
      required String? phone,
      required String? countryCode}) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._initLoginMethodData');
    try {
      return super._initLoginMethodData(
          emailId: emailId, phone: phone, countryCode: countryCode);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setSearchQuery(String query) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setSearchQuery');
    try {
      return super.setSearchQuery(query);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _debounceSearch(String query) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._debounceSearch');
    try {
      return super._debounceSearch(query);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  Future<void> updateProfile() {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.updateProfile');
    try {
      return super.updateProfile();
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setLocation(String value) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setLocation');
    try {
      return super.setLocation(value);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setStateBoard(int idx) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setStateBoard');
    try {
      return super.setStateBoard(idx);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setExperience(int idx) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setExperience');
    try {
      return super.setExperience(idx);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setProfessionalCourse(int idx) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setProfessionalCourse');
    try {
      return super.setProfessionalCourse(idx);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCompany(int idx) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setCompany');
    try {
      return super.setCompany(idx);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void setCountryCode(int idx) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState.setCountryCode');
    try {
      return super.setCountryCode(idx);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setStateBoardList(List<StateBoard> stateBoards) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._setStateBoardList');
    try {
      return super._setStateBoardList(stateBoards);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setProfessionalCourses(List<ProfessionalCourse> courses) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._setProfessionalCourses');
    try {
      return super._setProfessionalCourses(courses);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setCompanies(List<CompanyDetails> companies) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._setCompanies');
    try {
      return super._setCompanies(companies);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  void _setCountries(List<CountryDetail> countries) {
    final _$actionInfo = _$_OnboardingScreenStateActionController.startAction(
        name: '_OnboardingScreenState._setCountries');
    try {
      return super._setCountries(countries);
    } finally {
      _$_OnboardingScreenStateActionController.endAction(_$actionInfo);
    }
  }

  @override
  String toString() {
    return '''
firstName: ${firstName},
lastName: ${lastName},
loginMethod: ${loginMethod},
email: ${email},
phoneNumber: ${phoneNumber},
location: ${location},
termsAccepted: ${termsAccepted},
currentlyWorking: ${currentlyWorking},
hasError: ${hasError},
shouldEnableSubmitBtn: ${shouldEnableSubmitBtn}
    ''';
  }
}
