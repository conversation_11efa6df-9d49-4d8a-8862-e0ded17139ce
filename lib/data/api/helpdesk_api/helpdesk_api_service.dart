import 'dart:async';

import 'package:dependencies/dependencies.dart';
import 'package:miles_masterclass/core/logger/logger.dart';
import 'package:miles_masterclass/core/utils/constants/exceptions.dart';
import 'package:miles_masterclass/data/api/helpdesk_api/helpdesk_api_client.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/category_by_course_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/category_by_course_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/sub_category_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/questions_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/fourth_api_call_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/all_tickets_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/reopen_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/escalate_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/upload_file_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/sub_category_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/questions_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/fourth_api_call_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/all_tickets_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/reopen_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/escalate_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/upload_file_response.dart';

/// Interface for helpdesk API operations
abstract class IHelpdeskApiService {
  // The original API calls
  Future<Either<Exception, CategoryByCourseResponse>> categoryByCourse(CategoryByCourseRequest request);
  Future<Either<Exception, SubCategoryResponse>> subCategory(SubCategoryRequest request);
  Future<Either<Exception, QuestionsResponse>> questions(QuestionsRequest request);
  Future<Either<Exception, FourthApiCallResponse>> fourthApiCall(FourthApiCallRequest request);
  Future<Either<Exception, AllTicketsResponse>> allTickets(AllTicketsRequest request);

  // New ticket action API calls
  Future<Either<Exception, ReopenTicketResponse>> reopenTicket(ReopenTicketRequest request);
  Future<Either<Exception, EscalateTicketResponse>> escalateTicket(EscalateTicketRequest request);

  // File upload API call
  Future<Either<Exception, UploadFileResponse>> uploadFile(UploadFileRequest request);
}

class HelpdeskApiService implements IHelpdeskApiService {
  final HelpdeskApiClient _apiClient;
  final Dio _dio;

  HelpdeskApiService({
    required HelpdeskApiClient apiClient,
    required Dio dio,
  })  : _apiClient = apiClient,
        _dio = dio;

  /// Generic method to handle API responses and errors
Future<Either<Exception, T>> _handleResponse<T>(
      Future<T> Function() request) async {
    try {
      return Right(await request());
    } on DioException catch (e, s) {
      logger.e(
        e.message,
        stackTrace: s,
        error: e,
        time: DateTime.now(),
      );
      return Left(ServerException.dioError(e));
    } catch (e, s) {
      logger.e(
        e.toString(),
        stackTrace: s,
        error: e,
        time: DateTime.now(),
      );
      throw ServerException.unknown(e);
    }
  }

  // The 5 API calls implementation
  @override
  Future<Either<Exception, CategoryByCourseResponse>> categoryByCourse(CategoryByCourseRequest request) {
    return _handleResponse(() => _apiClient.categoryByCourse(request));
  }

  @override
  Future<Either<Exception, SubCategoryResponse>> subCategory(SubCategoryRequest request) {
    return _handleResponse(() => _apiClient.subCategory(request));
  }

  @override
  Future<Either<Exception, QuestionsResponse>> questions(QuestionsRequest request) {
    return _handleResponse(() => _apiClient.questions(request));
  }

  @override
  Future<Either<Exception, FourthApiCallResponse>> fourthApiCall(FourthApiCallRequest request) {
    return _handleResponse(() => _apiClient.fourthApiCall(request));
  }

  @override
  Future<Either<Exception, AllTicketsResponse>> allTickets(AllTicketsRequest request) {
    return _handleResponse(() => _apiClient.allTickets(request));
  }

  @override
  Future<Either<Exception, ReopenTicketResponse>> reopenTicket(ReopenTicketRequest request) {
    return _handleResponse(() => _apiClient.reopenTicket(request));
  }

  @override
  Future<Either<Exception, EscalateTicketResponse>> escalateTicket(EscalateTicketRequest request) {
    return _handleResponse(() => _apiClient.escalateTicket(request));
  }

  @override
  Future<Either<Exception, UploadFileResponse>> uploadFile(UploadFileRequest request) {
    if (request.file == null) {
      return Future.value(Left(Exception('File is required for upload')));
    }

    return _handleResponse(() => _apiClient.uploadFile(
      request.uploadPreset,
      request.folder,
      request.file!,
    ));
  }
}


