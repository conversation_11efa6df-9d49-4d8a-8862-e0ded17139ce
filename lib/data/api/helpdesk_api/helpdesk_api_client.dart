import 'dart:io';
import 'package:dependencies/dependencies.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/category_by_course_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/sub_category_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/questions_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/fourth_api_call_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/raise_query_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/all_tickets_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/reopen_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/escalate_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/upload_file_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/category_by_course_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/sub_category_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/questions_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/fourth_api_call_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/raise_query_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/all_tickets_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/reopen_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/escalate_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/upload_file_response.dart';

part 'helpdesk_api_client.g.dart';

/// Dedicated API client for helpdesk operations
/// 
/// This client is specifically designed for helpdesk backend operations
/// and is separate from the main API client to allow for:
/// - Different base URLs
/// - Different authentication mechanisms
/// - Different error handling
/// - Independent configuration
@RestApi()
abstract class HelpdeskApiClient {
  factory HelpdeskApiClient(Dio dio, {String? baseUrl}) =>
      _HelpdeskApiClient(dio, baseUrl: baseUrl);

  /// HELPDESK API CALLS ///
  
  /// Get categories by course
  @POST('/getCategorybyCourse')
  Future<CategoryByCourseResponse> categoryByCourse(@Body() CategoryByCourseRequest request);

  /// Get sub categories by category ID
  @POST('/getSubCategorybyCategory')
  Future<SubCategoryResponse> subCategory(@Body() SubCategoryRequest request);

  /// Get questions by question ID and category ID
  @POST('/getSubQuestionsbySubCategory')
  Future<QuestionsResponse> questions(@Body() QuestionsRequest request);

  /// Fourth API Call - Replace with your actual endpoint
  @POST('/helpdesk/fourth-endpoint/')
  Future<FourthApiCallResponse> fourthApiCall(@Body() FourthApiCallRequest request);

  /// Raise a new helpdesk query
  @POST('/feedback/updateHelpdeskData')
  Future<RaiseQueryResponse> raiseQuery(@Body() RaiseQueryRequest request);

  /// Get all tickets for a user
  @POST('/feedback/getAppStudentHelpdeskList')
  Future<AllTicketsResponse> allTickets(@Body() AllTicketsRequest request);

  /// Reopen a helpdesk ticket
  @POST('/helpdesk/updateAppStudentReopenStatus')
  Future<ReopenTicketResponse> reopenTicket(@Body() ReopenTicketRequest request);

  /// Escalate a helpdesk ticket
  @POST('/helpdesk/escalateAppStudentHelpdeskData')
  Future<EscalateTicketResponse> escalateTicket(@Body() EscalateTicketRequest request);

  /// Upload a file to the server
  @POST('/student/uploadFile')
  @MultiPart()
  Future<UploadFileResponse> uploadFile(
    @Part(name: 'upload_preset') String uploadPreset,
    @Part(name: 'folder') String folder,
    @Part(name: 'file') File file,
  );
}
