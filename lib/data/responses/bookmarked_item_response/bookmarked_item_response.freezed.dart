// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'bookmarked_item_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BookmarkedItemResponse _$BookmarkedItemResponseFromJson(
    Map<String, dynamic> json) {
  return _BookmarkedItemResponse.fromJson(json);
}

/// @nodoc
mixin _$BookmarkedItemResponse {
  MasterclassSummary get masterclassDetails =>
      throw _privateConstructorUsedError;
  int get masterClass => throw _privateConstructorUsedError;
  int get user => throw _privateConstructorUsedError;

  /// Serializes this BookmarkedItemResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BookmarkedItemResponseCopyWith<BookmarkedItemResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BookmarkedItemResponseCopyWith<$Res> {
  factory $BookmarkedItemResponseCopyWith(BookmarkedItemResponse value,
          $Res Function(BookmarkedItemResponse) then) =
      _$BookmarkedItemResponseCopyWithImpl<$Res, BookmarkedItemResponse>;
  @useResult
  $Res call({MasterclassSummary masterclassDetails, int masterClass, int user});

  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
}

/// @nodoc
class _$BookmarkedItemResponseCopyWithImpl<$Res,
        $Val extends BookmarkedItemResponse>
    implements $BookmarkedItemResponseCopyWith<$Res> {
  _$BookmarkedItemResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? masterclassDetails = null,
    Object? masterClass = null,
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails {
    return $MasterclassSummaryCopyWith<$Res>(_value.masterclassDetails,
        (value) {
      return _then(_value.copyWith(masterclassDetails: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BookmarkedItemResponseImplCopyWith<$Res>
    implements $BookmarkedItemResponseCopyWith<$Res> {
  factory _$$BookmarkedItemResponseImplCopyWith(
          _$BookmarkedItemResponseImpl value,
          $Res Function(_$BookmarkedItemResponseImpl) then) =
      __$$BookmarkedItemResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MasterclassSummary masterclassDetails, int masterClass, int user});

  @override
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
}

/// @nodoc
class __$$BookmarkedItemResponseImplCopyWithImpl<$Res>
    extends _$BookmarkedItemResponseCopyWithImpl<$Res,
        _$BookmarkedItemResponseImpl>
    implements _$$BookmarkedItemResponseImplCopyWith<$Res> {
  __$$BookmarkedItemResponseImplCopyWithImpl(
      _$BookmarkedItemResponseImpl _value,
      $Res Function(_$BookmarkedItemResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? masterclassDetails = null,
    Object? masterClass = null,
    Object? user = null,
  }) {
    return _then(_$BookmarkedItemResponseImpl(
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$BookmarkedItemResponseImpl implements _BookmarkedItemResponse {
  const _$BookmarkedItemResponseImpl(
      {required this.masterclassDetails,
      required this.masterClass,
      required this.user});

  factory _$BookmarkedItemResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$BookmarkedItemResponseImplFromJson(json);

  @override
  final MasterclassSummary masterclassDetails;
  @override
  final int masterClass;
  @override
  final int user;

  @override
  String toString() {
    return 'BookmarkedItemResponse(masterclassDetails: $masterclassDetails, masterClass: $masterClass, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BookmarkedItemResponseImpl &&
            (identical(other.masterclassDetails, masterclassDetails) ||
                other.masterclassDetails == masterclassDetails) &&
            (identical(other.masterClass, masterClass) ||
                other.masterClass == masterClass) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, masterclassDetails, masterClass, user);

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BookmarkedItemResponseImplCopyWith<_$BookmarkedItemResponseImpl>
      get copyWith => __$$BookmarkedItemResponseImplCopyWithImpl<
          _$BookmarkedItemResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BookmarkedItemResponseImplToJson(
      this,
    );
  }
}

abstract class _BookmarkedItemResponse implements BookmarkedItemResponse {
  const factory _BookmarkedItemResponse(
      {required final MasterclassSummary masterclassDetails,
      required final int masterClass,
      required final int user}) = _$BookmarkedItemResponseImpl;

  factory _BookmarkedItemResponse.fromJson(Map<String, dynamic> json) =
      _$BookmarkedItemResponseImpl.fromJson;

  @override
  MasterclassSummary get masterclassDetails;
  @override
  int get masterClass;
  @override
  int get user;

  /// Create a copy of BookmarkedItemResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BookmarkedItemResponseImplCopyWith<_$BookmarkedItemResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
