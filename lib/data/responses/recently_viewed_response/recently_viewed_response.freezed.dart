// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'recently_viewed_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecentlyViewedResponse _$RecentlyViewedResponseFromJson(
    Map<String, dynamic> json) {
  return _RecentlyViewedResponse.fromJson(json);
}

/// @nodoc
mixin _$RecentlyViewedResponse {
  @JsonKey(defaultValue: [])
  List<RecentlyViewedData> get recentlyViewed =>
      throw _privateConstructorUsedError;
  RecentlyViewedData? get lastViewed => throw _privateConstructorUsedError;

  /// Serializes this RecentlyViewedResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecentlyViewedResponseCopyWith<RecentlyViewedResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecentlyViewedResponseCopyWith<$Res> {
  factory $RecentlyViewedResponseCopyWith(RecentlyViewedResponse value,
          $Res Function(RecentlyViewedResponse) then) =
      _$RecentlyViewedResponseCopyWithImpl<$Res, RecentlyViewedResponse>;
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<RecentlyViewedData> recentlyViewed,
      RecentlyViewedData? lastViewed});

  $RecentlyViewedDataCopyWith<$Res>? get lastViewed;
}

/// @nodoc
class _$RecentlyViewedResponseCopyWithImpl<$Res,
        $Val extends RecentlyViewedResponse>
    implements $RecentlyViewedResponseCopyWith<$Res> {
  _$RecentlyViewedResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recentlyViewed = null,
    Object? lastViewed = freezed,
  }) {
    return _then(_value.copyWith(
      recentlyViewed: null == recentlyViewed
          ? _value.recentlyViewed
          : recentlyViewed // ignore: cast_nullable_to_non_nullable
              as List<RecentlyViewedData>,
      lastViewed: freezed == lastViewed
          ? _value.lastViewed
          : lastViewed // ignore: cast_nullable_to_non_nullable
              as RecentlyViewedData?,
    ) as $Val);
  }

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $RecentlyViewedDataCopyWith<$Res>? get lastViewed {
    if (_value.lastViewed == null) {
      return null;
    }

    return $RecentlyViewedDataCopyWith<$Res>(_value.lastViewed!, (value) {
      return _then(_value.copyWith(lastViewed: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecentlyViewedResponseImplCopyWith<$Res>
    implements $RecentlyViewedResponseCopyWith<$Res> {
  factory _$$RecentlyViewedResponseImplCopyWith(
          _$RecentlyViewedResponseImpl value,
          $Res Function(_$RecentlyViewedResponseImpl) then) =
      __$$RecentlyViewedResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(defaultValue: []) List<RecentlyViewedData> recentlyViewed,
      RecentlyViewedData? lastViewed});

  @override
  $RecentlyViewedDataCopyWith<$Res>? get lastViewed;
}

/// @nodoc
class __$$RecentlyViewedResponseImplCopyWithImpl<$Res>
    extends _$RecentlyViewedResponseCopyWithImpl<$Res,
        _$RecentlyViewedResponseImpl>
    implements _$$RecentlyViewedResponseImplCopyWith<$Res> {
  __$$RecentlyViewedResponseImplCopyWithImpl(
      _$RecentlyViewedResponseImpl _value,
      $Res Function(_$RecentlyViewedResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? recentlyViewed = null,
    Object? lastViewed = freezed,
  }) {
    return _then(_$RecentlyViewedResponseImpl(
      recentlyViewed: null == recentlyViewed
          ? _value._recentlyViewed
          : recentlyViewed // ignore: cast_nullable_to_non_nullable
              as List<RecentlyViewedData>,
      lastViewed: freezed == lastViewed
          ? _value.lastViewed
          : lastViewed // ignore: cast_nullable_to_non_nullable
              as RecentlyViewedData?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$RecentlyViewedResponseImpl implements _RecentlyViewedResponse {
  const _$RecentlyViewedResponseImpl(
      {@JsonKey(defaultValue: [])
      required final List<RecentlyViewedData> recentlyViewed,
      this.lastViewed})
      : _recentlyViewed = recentlyViewed;

  factory _$RecentlyViewedResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecentlyViewedResponseImplFromJson(json);

  final List<RecentlyViewedData> _recentlyViewed;
  @override
  @JsonKey(defaultValue: [])
  List<RecentlyViewedData> get recentlyViewed {
    if (_recentlyViewed is EqualUnmodifiableListView) return _recentlyViewed;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentlyViewed);
  }

  @override
  final RecentlyViewedData? lastViewed;

  @override
  String toString() {
    return 'RecentlyViewedResponse(recentlyViewed: $recentlyViewed, lastViewed: $lastViewed)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecentlyViewedResponseImpl &&
            const DeepCollectionEquality()
                .equals(other._recentlyViewed, _recentlyViewed) &&
            (identical(other.lastViewed, lastViewed) ||
                other.lastViewed == lastViewed));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_recentlyViewed), lastViewed);

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecentlyViewedResponseImplCopyWith<_$RecentlyViewedResponseImpl>
      get copyWith => __$$RecentlyViewedResponseImplCopyWithImpl<
          _$RecentlyViewedResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecentlyViewedResponseImplToJson(
      this,
    );
  }
}

abstract class _RecentlyViewedResponse implements RecentlyViewedResponse {
  const factory _RecentlyViewedResponse(
      {@JsonKey(defaultValue: [])
      required final List<RecentlyViewedData> recentlyViewed,
      final RecentlyViewedData? lastViewed}) = _$RecentlyViewedResponseImpl;

  factory _RecentlyViewedResponse.fromJson(Map<String, dynamic> json) =
      _$RecentlyViewedResponseImpl.fromJson;

  @override
  @JsonKey(defaultValue: [])
  List<RecentlyViewedData> get recentlyViewed;
  @override
  RecentlyViewedData? get lastViewed;

  /// Create a copy of RecentlyViewedResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecentlyViewedResponseImplCopyWith<_$RecentlyViewedResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
