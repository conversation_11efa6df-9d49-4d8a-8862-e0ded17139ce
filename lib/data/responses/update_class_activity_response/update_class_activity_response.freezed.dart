// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_class_activity_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UpdateClassActivityResponse _$UpdateClassActivityResponseFromJson(
    Map<String, dynamic> json) {
  return _UpdateClassActivityResponse.fromJson(json);
}

/// @nodoc
mixin _$UpdateClassActivityResponse {
  int get id => throw _privateConstructorUsedError;
  MasterclassSummary get masterclassDetails =>
      throw _privateConstructorUsedError;
  ChapterStatus get chapterStatus => throw _privateConstructorUsedError;
  int get timeStatus => throw _privateConstructorUsedError;
  int get masterClass => throw _privateConstructorUsedError;
  int get chapter => throw _privateConstructorUsedError;
  int get user => throw _privateConstructorUsedError;

  /// Serializes this UpdateClassActivityResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UpdateClassActivityResponseCopyWith<UpdateClassActivityResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UpdateClassActivityResponseCopyWith<$Res> {
  factory $UpdateClassActivityResponseCopyWith(
          UpdateClassActivityResponse value,
          $Res Function(UpdateClassActivityResponse) then) =
      _$UpdateClassActivityResponseCopyWithImpl<$Res,
          UpdateClassActivityResponse>;
  @useResult
  $Res call(
      {int id,
      MasterclassSummary masterclassDetails,
      ChapterStatus chapterStatus,
      int timeStatus,
      int masterClass,
      int chapter,
      int user});

  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
  $ChapterStatusCopyWith<$Res> get chapterStatus;
}

/// @nodoc
class _$UpdateClassActivityResponseCopyWithImpl<$Res,
        $Val extends UpdateClassActivityResponse>
    implements $UpdateClassActivityResponseCopyWith<$Res> {
  _$UpdateClassActivityResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? masterclassDetails = null,
    Object? chapterStatus = null,
    Object? timeStatus = null,
    Object? masterClass = null,
    Object? chapter = null,
    Object? user = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      chapterStatus: null == chapterStatus
          ? _value.chapterStatus
          : chapterStatus // ignore: cast_nullable_to_non_nullable
              as ChapterStatus,
      timeStatus: null == timeStatus
          ? _value.timeStatus
          : timeStatus // ignore: cast_nullable_to_non_nullable
              as int,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      chapter: null == chapter
          ? _value.chapter
          : chapter // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails {
    return $MasterclassSummaryCopyWith<$Res>(_value.masterclassDetails,
        (value) {
      return _then(_value.copyWith(masterclassDetails: value) as $Val);
    });
  }

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ChapterStatusCopyWith<$Res> get chapterStatus {
    return $ChapterStatusCopyWith<$Res>(_value.chapterStatus, (value) {
      return _then(_value.copyWith(chapterStatus: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UpdateClassActivityResponseImplCopyWith<$Res>
    implements $UpdateClassActivityResponseCopyWith<$Res> {
  factory _$$UpdateClassActivityResponseImplCopyWith(
          _$UpdateClassActivityResponseImpl value,
          $Res Function(_$UpdateClassActivityResponseImpl) then) =
      __$$UpdateClassActivityResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      MasterclassSummary masterclassDetails,
      ChapterStatus chapterStatus,
      int timeStatus,
      int masterClass,
      int chapter,
      int user});

  @override
  $MasterclassSummaryCopyWith<$Res> get masterclassDetails;
  @override
  $ChapterStatusCopyWith<$Res> get chapterStatus;
}

/// @nodoc
class __$$UpdateClassActivityResponseImplCopyWithImpl<$Res>
    extends _$UpdateClassActivityResponseCopyWithImpl<$Res,
        _$UpdateClassActivityResponseImpl>
    implements _$$UpdateClassActivityResponseImplCopyWith<$Res> {
  __$$UpdateClassActivityResponseImplCopyWithImpl(
      _$UpdateClassActivityResponseImpl _value,
      $Res Function(_$UpdateClassActivityResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? masterclassDetails = null,
    Object? chapterStatus = null,
    Object? timeStatus = null,
    Object? masterClass = null,
    Object? chapter = null,
    Object? user = null,
  }) {
    return _then(_$UpdateClassActivityResponseImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      masterclassDetails: null == masterclassDetails
          ? _value.masterclassDetails
          : masterclassDetails // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      chapterStatus: null == chapterStatus
          ? _value.chapterStatus
          : chapterStatus // ignore: cast_nullable_to_non_nullable
              as ChapterStatus,
      timeStatus: null == timeStatus
          ? _value.timeStatus
          : timeStatus // ignore: cast_nullable_to_non_nullable
              as int,
      masterClass: null == masterClass
          ? _value.masterClass
          : masterClass // ignore: cast_nullable_to_non_nullable
              as int,
      chapter: null == chapter
          ? _value.chapter
          : chapter // ignore: cast_nullable_to_non_nullable
              as int,
      user: null == user
          ? _value.user
          : user // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$UpdateClassActivityResponseImpl
    implements _UpdateClassActivityResponse {
  const _$UpdateClassActivityResponseImpl(
      {required this.id,
      required this.masterclassDetails,
      required this.chapterStatus,
      required this.timeStatus,
      required this.masterClass,
      required this.chapter,
      required this.user});

  factory _$UpdateClassActivityResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$UpdateClassActivityResponseImplFromJson(json);

  @override
  final int id;
  @override
  final MasterclassSummary masterclassDetails;
  @override
  final ChapterStatus chapterStatus;
  @override
  final int timeStatus;
  @override
  final int masterClass;
  @override
  final int chapter;
  @override
  final int user;

  @override
  String toString() {
    return 'UpdateClassActivityResponse(id: $id, masterclassDetails: $masterclassDetails, chapterStatus: $chapterStatus, timeStatus: $timeStatus, masterClass: $masterClass, chapter: $chapter, user: $user)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UpdateClassActivityResponseImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.masterclassDetails, masterclassDetails) ||
                other.masterclassDetails == masterclassDetails) &&
            (identical(other.chapterStatus, chapterStatus) ||
                other.chapterStatus == chapterStatus) &&
            (identical(other.timeStatus, timeStatus) ||
                other.timeStatus == timeStatus) &&
            (identical(other.masterClass, masterClass) ||
                other.masterClass == masterClass) &&
            (identical(other.chapter, chapter) || other.chapter == chapter) &&
            (identical(other.user, user) || other.user == user));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, masterclassDetails,
      chapterStatus, timeStatus, masterClass, chapter, user);

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UpdateClassActivityResponseImplCopyWith<_$UpdateClassActivityResponseImpl>
      get copyWith => __$$UpdateClassActivityResponseImplCopyWithImpl<
          _$UpdateClassActivityResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UpdateClassActivityResponseImplToJson(
      this,
    );
  }
}

abstract class _UpdateClassActivityResponse
    implements UpdateClassActivityResponse {
  const factory _UpdateClassActivityResponse(
      {required final int id,
      required final MasterclassSummary masterclassDetails,
      required final ChapterStatus chapterStatus,
      required final int timeStatus,
      required final int masterClass,
      required final int chapter,
      required final int user}) = _$UpdateClassActivityResponseImpl;

  factory _UpdateClassActivityResponse.fromJson(Map<String, dynamic> json) =
      _$UpdateClassActivityResponseImpl.fromJson;

  @override
  int get id;
  @override
  MasterclassSummary get masterclassDetails;
  @override
  ChapterStatus get chapterStatus;
  @override
  int get timeStatus;
  @override
  int get masterClass;
  @override
  int get chapter;
  @override
  int get user;

  /// Create a copy of UpdateClassActivityResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UpdateClassActivityResponseImplCopyWith<_$UpdateClassActivityResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
