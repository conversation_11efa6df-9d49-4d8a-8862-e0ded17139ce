import 'dart:async';
import 'dart:convert';
import 'package:dependencies/dependencies.dart';
import 'package:flutter/services.dart';
import 'package:miles_masterclass/data/api/helpdesk_api/helpdesk_api_service.dart';
import 'package:miles_masterclass/data/cache/i_cache_service.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_category/helpdesk_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/category_by_course_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/sub_category_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/questions_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/fourth_api_call_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/raise_query_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/all_tickets_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/reopen_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/escalate_ticket_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/requests/upload_file_request.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/category_by_course_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/sub_category_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_sub_category/helpdesk_sub_category.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/questions_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_question/helpdesk_question.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/fourth_api_call_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/raise_query_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/all_tickets_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/reopen_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/escalate_ticket_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/responses/upload_file_response.dart';
import 'package:miles_masterclass/domain/entities/helpdesk/helpdesk_ticket/helpdesk_ticket.dart';

class HelpdeskRepository {
  final IHelpdeskApiService _helpdeskApi;
  final ICacheService _cache;

  HelpdeskRepository({
    required IHelpdeskApiService helpdeskApi,
    required ICacheService cache,
  })  : _helpdeskApi = helpdeskApi,
        _cache = cache;

  // Get categories by course
  Future<Either<Exception, List<HelpdeskCategory>>> categoryByCourse(
      CategoryByCourseRequest request) async {
    final result = await _helpdeskApi.categoryByCourse(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Extract the data from the response
        if (r.data != null) {
          return Right(r.data!);
        } else {
          return const Right([]);
        }
      },
    );
  }

  // Get sub categories by category ID
  Future<Either<Exception, List<HelpdeskSubCategory>>> subCategory(
      SubCategoryRequest request) async {
    final result = await _helpdeskApi.subCategory(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Extract the data from the response
        if (r.data != null) {
          return Right(r.data!);
        } else {
          return const Right([]);
        }
      },
    );
  }

  // Get questions by question ID and category ID
  Future<Either<Exception, List<HelpdeskQuestion>>> questions(
      QuestionsRequest request) async {
    final result = await _helpdeskApi.questions(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Extract the data from the response
        if (r.data != null) {
          return Right(r.data!);
        } else {
          return const Right([]);
        }
      },
    );
  }

  // Fourth API call
  Future<Either<Exception, FourthApiCallResponse>> fourthApiCall(
      FourthApiCallRequest request) async {
    return await _helpdeskApi.fourthApiCall(request);
  }

  // Get all tickets for a user
  Future<Either<Exception, List<HelpdeskTicket>>> allTickets(
      AllTicketsRequest request) async {

    // TODO: TEMPORARY MOCK DATA - Remove this when backend is working
    // Set this flag to false to use real API
    const bool useMockData = false;

    if (useMockData) {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 5000));

      try {
        // Load mock data from assets
        const String mockDataPath = 'packages/resources/mock_data/helpdesk_tickets_mock.json';
        final String mockDataString = await rootBundle.loadString(mockDataPath);
        final Map<String, dynamic> mockResponse = json.decode(mockDataString);

        // Parse the mock data into HelpdeskTicket objects
        final List<dynamic> ticketsData = mockResponse['data'] as List<dynamic>;
        final List<HelpdeskTicket> tickets = ticketsData
            .map((ticketJson) => HelpdeskTicket.fromJson(ticketJson as Map<String, dynamic>))
            .toList();

        return Right(tickets);
      } catch (e) {
        return Left(Exception('Failed to load mock data: $e'));
      }
    }

    // Original API call - will be used when useMockData = false
    final result = await _helpdeskApi.allTickets(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Extract the data from the response
        if (r.data != null) {
          return Right(r.data!);
        } else {
          return const Right([]);
        }
      },
    );
  }

  // Reopen a helpdesk ticket
  Future<Either<Exception, bool>> reopenTicket(
      ReopenTicketRequest request) async {
    final result = await _helpdeskApi.reopenTicket(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Check if the operation was successful
        if (r.status == true && r.code == 200) {
          return const Right(true);
        } else {
          return Left(Exception('Failed to reopen ticket: ${r.message ?? 'Unknown error'}'));
        }
      },
    );
  }

  // Escalate a helpdesk ticket
  Future<Either<Exception, bool>> escalateTicket(
      EscalateTicketRequest request) async {
    final result = await _helpdeskApi.escalateTicket(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Check if the operation was successful
        if (r.status == true && r.code == 200) {
          return const Right(true);
        } else {
          return Left(Exception('Failed to escalate ticket: ${r.message ?? 'Unknown error'}'));
        }
      },
    );
  }

  // Upload a file to the server
  Future<Either<Exception, String>> uploadFile(UploadFileRequest request) async {
    final result = await _helpdeskApi.uploadFile(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Check if the upload was successful and return the secure URL
        if (r.status == true && r.code == 200 && r.secureUrl != null) {
          return Right(r.secureUrl!);
        } else {
          return Left(Exception('Failed to upload file: ${r.message ?? 'Unknown error'}'));
        }
      },
    );
  }

  // Raise a new helpdesk query
  Future<Either<Exception, int>> raiseQuery(RaiseQueryRequest request) async {
    final result = await _helpdeskApi.raiseQuery(request);

    return result.fold(
      (l) => Left(l),
      (r) {
        // Check if the operation was successful and return the ticket ID
        if (r.status == true && r.code == 200 && r.ticketId != null) {
          return Right(r.ticketId!);
        } else {
          return Left(Exception('Failed to raise query: ${r.message ?? 'Unknown error'}'));
        }
      },
    );
  }
}
