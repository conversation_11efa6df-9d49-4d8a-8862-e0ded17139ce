// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'masterclass_cache_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MasterclassCacheData _$MasterclassCacheDataFromJson(Map<String, dynamic> json) {
  return _MasterclassCacheData.fromJson(json);
}

/// @nodoc
mixin _$MasterclassCacheData {
  MasterclassSummary get summary => throw _privateConstructorUsedError;
  MasterclassDetails? get details => throw _privateConstructorUsedError;

  /// Serializes this MasterclassCacheData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MasterclassCacheDataCopyWith<MasterclassCacheData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MasterclassCacheDataCopyWith<$Res> {
  factory $MasterclassCacheDataCopyWith(MasterclassCacheData value,
          $Res Function(MasterclassCacheData) then) =
      _$MasterclassCacheDataCopyWithImpl<$Res, MasterclassCacheData>;
  @useResult
  $Res call({MasterclassSummary summary, MasterclassDetails? details});

  $MasterclassSummaryCopyWith<$Res> get summary;
  $MasterclassDetailsCopyWith<$Res>? get details;
}

/// @nodoc
class _$MasterclassCacheDataCopyWithImpl<$Res,
        $Val extends MasterclassCacheData>
    implements $MasterclassCacheDataCopyWith<$Res> {
  _$MasterclassCacheDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? summary = null,
    Object? details = freezed,
  }) {
    return _then(_value.copyWith(
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as MasterclassDetails?,
    ) as $Val);
  }

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MasterclassSummaryCopyWith<$Res> get summary {
    return $MasterclassSummaryCopyWith<$Res>(_value.summary, (value) {
      return _then(_value.copyWith(summary: value) as $Val);
    });
  }

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $MasterclassDetailsCopyWith<$Res>? get details {
    if (_value.details == null) {
      return null;
    }

    return $MasterclassDetailsCopyWith<$Res>(_value.details!, (value) {
      return _then(_value.copyWith(details: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$MasterclassCacheDataImplCopyWith<$Res>
    implements $MasterclassCacheDataCopyWith<$Res> {
  factory _$$MasterclassCacheDataImplCopyWith(_$MasterclassCacheDataImpl value,
          $Res Function(_$MasterclassCacheDataImpl) then) =
      __$$MasterclassCacheDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({MasterclassSummary summary, MasterclassDetails? details});

  @override
  $MasterclassSummaryCopyWith<$Res> get summary;
  @override
  $MasterclassDetailsCopyWith<$Res>? get details;
}

/// @nodoc
class __$$MasterclassCacheDataImplCopyWithImpl<$Res>
    extends _$MasterclassCacheDataCopyWithImpl<$Res, _$MasterclassCacheDataImpl>
    implements _$$MasterclassCacheDataImplCopyWith<$Res> {
  __$$MasterclassCacheDataImplCopyWithImpl(_$MasterclassCacheDataImpl _value,
      $Res Function(_$MasterclassCacheDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? summary = null,
    Object? details = freezed,
  }) {
    return _then(_$MasterclassCacheDataImpl(
      summary: null == summary
          ? _value.summary
          : summary // ignore: cast_nullable_to_non_nullable
              as MasterclassSummary,
      details: freezed == details
          ? _value.details
          : details // ignore: cast_nullable_to_non_nullable
              as MasterclassDetails?,
    ));
  }
}

/// @nodoc

@JsonSerializable(fieldRename: FieldRename.snake)
class _$MasterclassCacheDataImpl implements _MasterclassCacheData {
  const _$MasterclassCacheDataImpl({required this.summary, this.details});

  factory _$MasterclassCacheDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$MasterclassCacheDataImplFromJson(json);

  @override
  final MasterclassSummary summary;
  @override
  final MasterclassDetails? details;

  @override
  String toString() {
    return 'MasterclassCacheData(summary: $summary, details: $details)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MasterclassCacheDataImpl &&
            (identical(other.summary, summary) || other.summary == summary) &&
            (identical(other.details, details) || other.details == details));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, summary, details);

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MasterclassCacheDataImplCopyWith<_$MasterclassCacheDataImpl>
      get copyWith =>
          __$$MasterclassCacheDataImplCopyWithImpl<_$MasterclassCacheDataImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MasterclassCacheDataImplToJson(
      this,
    );
  }
}

abstract class _MasterclassCacheData implements MasterclassCacheData {
  const factory _MasterclassCacheData(
      {required final MasterclassSummary summary,
      final MasterclassDetails? details}) = _$MasterclassCacheDataImpl;

  factory _MasterclassCacheData.fromJson(Map<String, dynamic> json) =
      _$MasterclassCacheDataImpl.fromJson;

  @override
  MasterclassSummary get summary;
  @override
  MasterclassDetails? get details;

  /// Create a copy of MasterclassCacheData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MasterclassCacheDataImplCopyWith<_$MasterclassCacheDataImpl>
      get copyWith => throw _privateConstructorUsedError;
}
