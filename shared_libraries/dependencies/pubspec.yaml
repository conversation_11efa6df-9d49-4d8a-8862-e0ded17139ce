name: dependencies
description: A new Flutter package project.
version: 0.0.1
homepage: null

environment:
  sdk: ^3.5.1

dependencies:
  # stripe_android: "3.0.0"
  cached_network_image: ^3.4.1
  collection: ^1.18.0
  dartz: ^0.10.1
  dio: ^5.6.0
  equatable: ^2.0.3
  flutter:
    sdk: flutter
  flutter_mobx: ^2.2.1+1
  flutter_screenutil: ^5.9.3
  modal_bottom_sheet: ^3.0.0
  syncfusion_flutter_pdfviewer: ^27.2.5
  flutter_svg: ^2.0.10+1
  freezed_annotation: ^2.4.4
  flutter_widget_from_html_core: ^0.15.2
  get_it: ^7.7.0
  go_router: ^14.2.7
  carousel_slider: ^5.0.0
  helpers: ^1.2.3
  intl: ^0.19.0
  json_annotation: ^4.9.0
  logger: ^2.4.0
  mobx: 2.3.3+2
  overlay_support: ^2.1.0
  youtube_player_flutter: ^9.1.1
  package_info_plus: ^8.0.2
  flutter_rating_bar: ^4.0.1
  permission_handler: ^11.3.1
  pretty_dio_logger:
  retrofit: ^4.2.0
  lottie: ^3.1.3
  rxdart: ^0.27.2
  share_plus: ^10.1.4
  shared_preferences: ^2.3.2
  shimmer: ^3.0.0
  url_launcher: ^6.3.0
  fl_chart: ^0.69.0
  video_player: ^2.9.1
  chewie: ^1.8.5
  pinput: ^5.0.0
  crypto: ^3.0.6
  device_info_plus: ^10.1.0
  webview_flutter: ^4.10.0
  app_tracking_transparency: ^2.0.6+1
  path_provider: ^2.1.5
  flutter_typeahead: ^5.2.0
  flutter_stripe: ^11.4.0
  flutter_dotenv: ^5.2.1
  pdf: ^3.11.2
  open_file: ^3.5.10
  printing: ^5.14.2
  in_app_purchase: ^3.2.1
  archive: ^3.3.8
  htmltopdfwidgets: ^1.0.9
  http:
  app_links: ^6.4.0
  timezone: ^0.10.0
  image_picker: ^1.1.2
  flutter_image_compress: ^2.3.0
  




dependency_overrides:
  webview_flutter_android: ^4.2.0
  archive: ^4.0.4






dev_dependencies:
  build_runner: null
  json_serializable: ^6.8.0
  retrofit_generator: ^8.2.1

flutter: null
