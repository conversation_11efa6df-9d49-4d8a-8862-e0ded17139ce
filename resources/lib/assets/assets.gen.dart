/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $MockDataGen {
  const $MockDataGen();

  /// File path: mock_data/helpdesk_tickets_mock.json
  String get helpdeskTicketsMock => 'mock_data/helpdesk_tickets_mock.json';

  /// List of all assets
  List<String> get values => [helpdeskTicketsMock];
}

class $AssetsBgGen {
  const $AssetsBgGen();

  /// File path: assets/bg/app_bg_grid.png
  AssetGenImage get appBgGrid => const AssetGenImage('assets/bg/app_bg_grid.png');

  /// File path: assets/bg/app_bg_image.png
  AssetGenImage get appBgImage => const AssetGenImage('assets/bg/app_bg_image.png');

  /// List of all assets
  List<AssetGenImage> get values => [appBgGrid, appBgImage];
}

class $AssetsChatIconsGen {
  const $AssetsChatIconsGen();

  /// File path: assets/chat_icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/chat_icons/search.svg');

  /// List of all assets
  List<SvgGenImage> get values => [search];
}

class $AssetsCpeTrackerGen {
  const $AssetsCpeTrackerGen();

  /// File path: assets/cpe_tracker/accounting.svg
  SvgGenImage get accounting => const SvgGenImage('assets/cpe_tracker/accounting.svg');

  /// File path: assets/cpe_tracker/cpe_tab_animation.json
  LottieGenImage get cpeTabAnimation => const LottieGenImage('assets/cpe_tracker/cpe_tab_animation.json');

  /// File path: assets/cpe_tracker/ethics.svg
  SvgGenImage get ethics => const SvgGenImage('assets/cpe_tracker/ethics.svg');

  /// File path: assets/cpe_tracker/others.svg
  SvgGenImage get others => const SvgGenImage('assets/cpe_tracker/others.svg');

  /// List of all assets
  List<dynamic> get values => [accounting, cpeTabAnimation, ethics, others];
}

class $AssetsEnvGen {
  const $AssetsEnvGen();

  /// File path: assets/env/.env.development
  String get aEnvDevelopment => 'assets/env/.env.development';

  /// File path: assets/env/.env.prod
  String get aEnvProd => 'assets/env/.env.prod';

  /// File path: assets/env/.env.uat
  String get aEnvUat => 'assets/env/.env.uat';

  /// List of all assets
  List<String> get values => [aEnvDevelopment, aEnvProd, aEnvUat];
}

class $AssetsFinalExamGen {
  const $AssetsFinalExamGen();

  /// File path: assets/final_exam/exam_passed_animation.json
  LottieGenImage get examPassedAnimation => const LottieGenImage('assets/final_exam/exam_passed_animation.json');

  /// List of all assets
  List<LottieGenImage> get values => [examPassedAnimation];
}

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/OpenSans-Bold.ttf
  String get openSansBold => 'assets/fonts/OpenSans-Bold.ttf';

  /// File path: assets/fonts/OpenSans-BoldItalic.ttf
  String get openSansBoldItalic => 'assets/fonts/OpenSans-BoldItalic.ttf';

  /// File path: assets/fonts/OpenSans-ExtraBold.ttf
  String get openSansExtraBold => 'assets/fonts/OpenSans-ExtraBold.ttf';

  /// File path: assets/fonts/OpenSans-ExtraBoldItalic.ttf
  String get openSansExtraBoldItalic => 'assets/fonts/OpenSans-ExtraBoldItalic.ttf';

  /// File path: assets/fonts/OpenSans-Italic.ttf
  String get openSansItalic => 'assets/fonts/OpenSans-Italic.ttf';

  /// File path: assets/fonts/OpenSans-Light.ttf
  String get openSansLight => 'assets/fonts/OpenSans-Light.ttf';

  /// File path: assets/fonts/OpenSans-LightItalic.ttf
  String get openSansLightItalic => 'assets/fonts/OpenSans-LightItalic.ttf';

  /// File path: assets/fonts/OpenSans-Medium.ttf
  String get openSansMedium => 'assets/fonts/OpenSans-Medium.ttf';

  /// File path: assets/fonts/OpenSans-MediumItalic.ttf
  String get openSansMediumItalic => 'assets/fonts/OpenSans-MediumItalic.ttf';

  /// File path: assets/fonts/OpenSans-Regular.ttf
  String get openSansRegular => 'assets/fonts/OpenSans-Regular.ttf';

  /// File path: assets/fonts/OpenSans-SemiBold.ttf
  String get openSansSemiBold => 'assets/fonts/OpenSans-SemiBold.ttf';

  /// File path: assets/fonts/OpenSans-SemiBoldItalic.ttf
  String get openSansSemiBoldItalic => 'assets/fonts/OpenSans-SemiBoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-Bold.ttf
  String get openSansCondensedBold => 'assets/fonts/OpenSans_Condensed-Bold.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-BoldItalic.ttf
  String get openSansCondensedBoldItalic => 'assets/fonts/OpenSans_Condensed-BoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-ExtraBold.ttf
  String get openSansCondensedExtraBold => 'assets/fonts/OpenSans_Condensed-ExtraBold.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-ExtraBoldItalic.ttf
  String get openSansCondensedExtraBoldItalic => 'assets/fonts/OpenSans_Condensed-ExtraBoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-Italic.ttf
  String get openSansCondensedItalic => 'assets/fonts/OpenSans_Condensed-Italic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-Light.ttf
  String get openSansCondensedLight => 'assets/fonts/OpenSans_Condensed-Light.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-LightItalic.ttf
  String get openSansCondensedLightItalic => 'assets/fonts/OpenSans_Condensed-LightItalic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-Medium.ttf
  String get openSansCondensedMedium => 'assets/fonts/OpenSans_Condensed-Medium.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-MediumItalic.ttf
  String get openSansCondensedMediumItalic => 'assets/fonts/OpenSans_Condensed-MediumItalic.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-Regular.ttf
  String get openSansCondensedRegular => 'assets/fonts/OpenSans_Condensed-Regular.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-SemiBold.ttf
  String get openSansCondensedSemiBold => 'assets/fonts/OpenSans_Condensed-SemiBold.ttf';

  /// File path: assets/fonts/OpenSans_Condensed-SemiBoldItalic.ttf
  String get openSansCondensedSemiBoldItalic => 'assets/fonts/OpenSans_Condensed-SemiBoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-Bold.ttf
  String get openSansSemiCondensedBold => 'assets/fonts/OpenSans_SemiCondensed-Bold.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-BoldItalic.ttf
  String get openSansSemiCondensedBoldItalic => 'assets/fonts/OpenSans_SemiCondensed-BoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-ExtraBold.ttf
  String get openSansSemiCondensedExtraBold => 'assets/fonts/OpenSans_SemiCondensed-ExtraBold.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-ExtraBoldItalic.ttf
  String get openSansSemiCondensedExtraBoldItalic => 'assets/fonts/OpenSans_SemiCondensed-ExtraBoldItalic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-Italic.ttf
  String get openSansSemiCondensedItalic => 'assets/fonts/OpenSans_SemiCondensed-Italic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-Light.ttf
  String get openSansSemiCondensedLight => 'assets/fonts/OpenSans_SemiCondensed-Light.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-LightItalic.ttf
  String get openSansSemiCondensedLightItalic => 'assets/fonts/OpenSans_SemiCondensed-LightItalic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-Medium.ttf
  String get openSansSemiCondensedMedium => 'assets/fonts/OpenSans_SemiCondensed-Medium.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-MediumItalic.ttf
  String get openSansSemiCondensedMediumItalic => 'assets/fonts/OpenSans_SemiCondensed-MediumItalic.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-Regular.ttf
  String get openSansSemiCondensedRegular => 'assets/fonts/OpenSans_SemiCondensed-Regular.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-SemiBold.ttf
  String get openSansSemiCondensedSemiBold => 'assets/fonts/OpenSans_SemiCondensed-SemiBold.ttf';

  /// File path: assets/fonts/OpenSans_SemiCondensed-SemiBoldItalic.ttf
  String get openSansSemiCondensedSemiBoldItalic => 'assets/fonts/OpenSans_SemiCondensed-SemiBoldItalic.ttf';

  /// List of all assets
  List<String> get values => [
        openSansBold,
        openSansBoldItalic,
        openSansExtraBold,
        openSansExtraBoldItalic,
        openSansItalic,
        openSansLight,
        openSansLightItalic,
        openSansMedium,
        openSansMediumItalic,
        openSansRegular,
        openSansSemiBold,
        openSansSemiBoldItalic,
        openSansCondensedBold,
        openSansCondensedBoldItalic,
        openSansCondensedExtraBold,
        openSansCondensedExtraBoldItalic,
        openSansCondensedItalic,
        openSansCondensedLight,
        openSansCondensedLightItalic,
        openSansCondensedMedium,
        openSansCondensedMediumItalic,
        openSansCondensedRegular,
        openSansCondensedSemiBold,
        openSansCondensedSemiBoldItalic,
        openSansSemiCondensedBold,
        openSansSemiCondensedBoldItalic,
        openSansSemiCondensedExtraBold,
        openSansSemiCondensedExtraBoldItalic,
        openSansSemiCondensedItalic,
        openSansSemiCondensedLight,
        openSansSemiCondensedLightItalic,
        openSansSemiCondensedMedium,
        openSansSemiCondensedMediumItalic,
        openSansSemiCondensedRegular,
        openSansSemiCondensedSemiBold,
        openSansSemiCondensedSemiBoldItalic
      ];
}

class $AssetsIconGen {
  const $AssetsIconGen();

  /// File path: assets/icon/.svg
  SvgGenImage get aSvg => const SvgGenImage('assets/icon/.svg');

  /// File path: assets/icon/Congrats Popup.png
  AssetGenImage get congratsPopup => const AssetGenImage('assets/icon/Congrats Popup.png');

  /// File path: assets/icon/add.svg
  SvgGenImage get add => const SvgGenImage('assets/icon/add.svg');

  /// File path: assets/icon/arrow_right_ios.svg
  SvgGenImage get arrowRightIos => const SvgGenImage('assets/icon/arrow_right_ios.svg');

  /// File path: assets/icon/attachment_icon.svg
  SvgGenImage get attachmentIcon => const SvgGenImage('assets/icon/attachment_icon.svg');

  /// File path: assets/icon/back.svg
  SvgGenImage get back => const SvgGenImage('assets/icon/back.svg');

  /// File path: assets/icon/cart_outlined.svg
  SvgGenImage get cartOutlined => const SvgGenImage('assets/icon/cart_outlined.svg');

  /// File path: assets/icon/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icon/check.svg');

  /// File path: assets/icon/check_mark.svg
  SvgGenImage get checkMark => const SvgGenImage('assets/icon/check_mark.svg');

  /// File path: assets/icon/chevon_down.svg
  SvgGenImage get chevonDown => const SvgGenImage('assets/icon/chevon_down.svg');

  /// File path: assets/icon/chevron_up.svg
  SvgGenImage get chevronUp => const SvgGenImage('assets/icon/chevron_up.svg');

  /// File path: assets/icon/corrective_action_icon.svg
  SvgGenImage get correctiveActionIcon => const SvgGenImage('assets/icon/corrective_action_icon.svg');

  /// File path: assets/icon/coupon_left_side_gradient.svg
  SvgGenImage get couponLeftSideGradient => const SvgGenImage('assets/icon/coupon_left_side_gradient.svg');

  /// File path: assets/icon/cross.png
  AssetGenImage get crossPng => const AssetGenImage('assets/icon/cross.png');

  /// File path: assets/icon/cross.svg
  SvgGenImage get crossSvg => const SvgGenImage('assets/icon/cross.svg');

  /// File path: assets/icon/cross_2.svg
  SvgGenImage get cross2 => const SvgGenImage('assets/icon/cross_2.svg');

  /// File path: assets/icon/customer_support_icon.svg
  SvgGenImage get customerSupportIcon => const SvgGenImage('assets/icon/customer_support_icon.svg');

  /// File path: assets/icon/danger.svg
  SvgGenImage get danger => const SvgGenImage('assets/icon/danger.svg');

  /// File path: assets/icon/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/icon/delete.svg');

  /// File path: assets/icon/download.svg
  SvgGenImage get download => const SvgGenImage('assets/icon/download.svg');

  /// File path: assets/icon/edit.svg
  SvgGenImage get edit => const SvgGenImage('assets/icon/edit.svg');

  /// File path: assets/icon/empty_cart.svg
  SvgGenImage get emptyCart => const SvgGenImage('assets/icon/empty_cart.svg');

  /// File path: assets/icon/exam_failed.svg
  SvgGenImage get examFailed => const SvgGenImage('assets/icon/exam_failed.svg');

  /// File path: assets/icon/feedback.svg
  SvgGenImage get feedback => const SvgGenImage('assets/icon/feedback.svg');

  /// File path: assets/icon/filter_icon.svg
  SvgGenImage get filterIcon => const SvgGenImage('assets/icon/filter_icon.svg');

  /// File path: assets/icon/helpdesk_icon.svg
  SvgGenImage get helpdeskIcon => const SvgGenImage('assets/icon/helpdesk_icon.svg');

  /// File path: assets/icon/helpdesk_ticket_icon.svg
  SvgGenImage get helpdeskTicketIcon => const SvgGenImage('assets/icon/helpdesk_ticket_icon.svg');

  /// File path: assets/icon/link.svg
  SvgGenImage get link => const SvgGenImage('assets/icon/link.svg');

  /// File path: assets/icon/linked_in.svg
  SvgGenImage get linkedIn => const SvgGenImage('assets/icon/linked_in.svg');

  /// File path: assets/icon/live_now.svg
  SvgGenImage get liveNow => const SvgGenImage('assets/icon/live_now.svg');

  /// File path: assets/icon/lock.svg
  SvgGenImage get lock => const SvgGenImage('assets/icon/lock.svg');

  /// File path: assets/icon/more_dot_icon.svg
  SvgGenImage get moreDotIcon => const SvgGenImage('assets/icon/more_dot_icon.svg');

  /// File path: assets/icon/no_data.svg
  SvgGenImage get noData => const SvgGenImage('assets/icon/no_data.svg');

  /// File path: assets/icon/notifications.svg
  SvgGenImage get notifications => const SvgGenImage('assets/icon/notifications.svg');

  /// File path: assets/icon/paid.svg
  SvgGenImage get paid => const SvgGenImage('assets/icon/paid.svg');

  /// File path: assets/icon/pdf.svg
  SvgGenImage get pdf => const SvgGenImage('assets/icon/pdf.svg');

  /// File path: assets/icon/person.svg
  SvgGenImage get person => const SvgGenImage('assets/icon/person.svg');

  /// File path: assets/icon/person2.svg
  SvgGenImage get person2 => const SvgGenImage('assets/icon/person2.svg');

  /// File path: assets/icon/phone_2.png
  AssetGenImage get phone2 => const AssetGenImage('assets/icon/phone_2.png');

  /// File path: assets/icon/phone_gradient.svg
  SvgGenImage get phoneGradient => const SvgGenImage('assets/icon/phone_gradient.svg');

  /// File path: assets/icon/pickup.svg
  SvgGenImage get pickup => const SvgGenImage('assets/icon/pickup.svg');

  /// File path: assets/icon/placeholder_image.png
  AssetGenImage get placeholderImage => const AssetGenImage('assets/icon/placeholder_image.png');

  /// File path: assets/icon/plan.svg
  SvgGenImage get plan => const SvgGenImage('assets/icon/plan.svg');

  /// File path: assets/icon/play.png
  AssetGenImage get playPng => const AssetGenImage('assets/icon/play.png');

  /// File path: assets/icon/play.svg
  SvgGenImage get playSvg => const SvgGenImage('assets/icon/play.svg');

  /// File path: assets/icon/play_circle.svg
  SvgGenImage get playCircle => const SvgGenImage('assets/icon/play_circle.svg');

  /// File path: assets/icon/play_circle_filled.png
  AssetGenImage get playCircleFilled => const AssetGenImage('assets/icon/play_circle_filled.png');

  /// File path: assets/icon/play_fill.png
  AssetGenImage get playFill => const AssetGenImage('assets/icon/play_fill.png');

  /// File path: assets/icon/play_rounded.svg
  SvgGenImage get playRounded => const SvgGenImage('assets/icon/play_rounded.svg');

  /// File path: assets/icon/popular_pick.svg
  SvgGenImage get popularPick => const SvgGenImage('assets/icon/popular_pick.svg');

  /// File path: assets/icon/post_copy.svg
  SvgGenImage get postCopy => const SvgGenImage('assets/icon/post_copy.svg');

  /// File path: assets/icon/post_play.svg
  SvgGenImage get postPlay => const SvgGenImage('assets/icon/post_play.svg');

  /// File path: assets/icon/premier.svg
  SvgGenImage get premier => const SvgGenImage('assets/icon/premier.svg');

  /// File path: assets/icon/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/icon/profile.svg');

  /// File path: assets/icon/profile_circle.svg
  SvgGenImage get profileCircle => const SvgGenImage('assets/icon/profile_circle.svg');

  /// File path: assets/icon/profile_filled.svg
  SvgGenImage get profileFilled => const SvgGenImage('assets/icon/profile_filled.svg');

  /// File path: assets/icon/profile_tab.svg
  SvgGenImage get profileTab => const SvgGenImage('assets/icon/profile_tab.svg');

  /// File path: assets/icon/profile_tab_bold.svg
  SvgGenImage get profileTabBold => const SvgGenImage('assets/icon/profile_tab_bold.svg');

  /// File path: assets/icon/raise_ticket_icon.svg
  SvgGenImage get raiseTicketIcon => const SvgGenImage('assets/icon/raise_ticket_icon.svg');

  /// File path: assets/icon/recipe.svg
  SvgGenImage get recipe => const SvgGenImage('assets/icon/recipe.svg');

  /// File path: assets/icon/recipe_icon.svg
  SvgGenImage get recipeIcon => const SvgGenImage('assets/icon/recipe_icon.svg');

  /// File path: assets/icon/refresh.svg
  SvgGenImage get refresh => const SvgGenImage('assets/icon/refresh.svg');

  /// File path: assets/icon/remove.svg
  SvgGenImage get remove => const SvgGenImage('assets/icon/remove.svg');

  /// File path: assets/icon/replay.svg
  SvgGenImage get replay => const SvgGenImage('assets/icon/replay.svg');

  /// File path: assets/icon/report.svg
  SvgGenImage get report => const SvgGenImage('assets/icon/report.svg');

  /// File path: assets/icon/sad_face.svg
  SvgGenImage get sadFace => const SvgGenImage('assets/icon/sad_face.svg');

  /// File path: assets/icon/sample.svg
  SvgGenImage get sample => const SvgGenImage('assets/icon/sample.svg');

  /// File path: assets/icon/search.png
  AssetGenImage get searchPng => const AssetGenImage('assets/icon/search.png');

  /// File path: assets/icon/search.svg
  SvgGenImage get searchSvg => const SvgGenImage('assets/icon/search.svg');

  /// File path: assets/icon/search2.svg
  SvgGenImage get search2 => const SvgGenImage('assets/icon/search2.svg');

  /// File path: assets/icon/search_gradient.svg
  SvgGenImage get searchGradient => const SvgGenImage('assets/icon/search_gradient.svg');

  /// File path: assets/icon/send.svg
  SvgGenImage get send => const SvgGenImage('assets/icon/send.svg');

  /// File path: assets/icon/send_reply_icon.svg
  SvgGenImage get sendReplyIcon => const SvgGenImage('assets/icon/send_reply_icon.svg');

  /// File path: assets/icon/settings.svg
  SvgGenImage get settings => const SvgGenImage('assets/icon/settings.svg');

  /// File path: assets/icon/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icon/share.svg');

  /// File path: assets/icon/shop.svg
  SvgGenImage get shop => const SvgGenImage('assets/icon/shop.svg');

  /// File path: assets/icon/shopping_bag_outlined.svg
  SvgGenImage get shoppingBagOutlined => const SvgGenImage('assets/icon/shopping_bag_outlined.svg');

  /// File path: assets/icon/shopping_cart.svg
  SvgGenImage get shoppingCart => const SvgGenImage('assets/icon/shopping_cart.svg');

  /// File path: assets/icon/smile_face.svg
  SvgGenImage get smileFace => const SvgGenImage('assets/icon/smile_face.svg');

  /// File path: assets/icon/smile_face2.svg
  SvgGenImage get smileFace2 => const SvgGenImage('assets/icon/smile_face2.svg');

  /// File path: assets/icon/smile_face_filled.svg
  SvgGenImage get smileFaceFilled => const SvgGenImage('assets/icon/smile_face_filled.svg');

  /// File path: assets/icon/smiling_face_gradient.svg
  SvgGenImage get smilingFaceGradient => const SvgGenImage('assets/icon/smiling_face_gradient.svg');

  /// File path: assets/icon/speaker.png
  AssetGenImage get speaker => const AssetGenImage('assets/icon/speaker.png');

  /// File path: assets/icon/spf_logo.svg
  SvgGenImage get spfLogo => const SvgGenImage('assets/icon/spf_logo.svg');

  /// File path: assets/icon/star.svg
  SvgGenImage get star => const SvgGenImage('assets/icon/star.svg');

  /// File path: assets/icon/start_chat_bold.svg
  SvgGenImage get startChatBold => const SvgGenImage('assets/icon/start_chat_bold.svg');

  /// File path: assets/icon/stripe_icon.svg
  SvgGenImage get stripeIcon => const SvgGenImage('assets/icon/stripe_icon.svg');

  /// File path: assets/icon/subscription_bg.svg
  SvgGenImage get subscriptionBg => const SvgGenImage('assets/icon/subscription_bg.svg');

  /// File path: assets/icon/subscription_premium_icon.svg
  SvgGenImage get subscriptionPremiumIcon => const SvgGenImage('assets/icon/subscription_premium_icon.svg');

  /// File path: assets/icon/support.svg
  SvgGenImage get support => const SvgGenImage('assets/icon/support.svg');

  /// File path: assets/icon/switch.svg
  SvgGenImage get switchSvg => const SvgGenImage('assets/icon/switch.svg');

  /// File path: assets/icon/tabletop_logo.svg
  SvgGenImage get tabletopLogo => const SvgGenImage('assets/icon/tabletop_logo.svg');

  /// File path: assets/icon/takeaway_tab_icon.png
  AssetGenImage get takeawayTabIcon => const AssetGenImage('assets/icon/takeaway_tab_icon.png');

  /// File path: assets/icon/telegram.png
  AssetGenImage get telegram => const AssetGenImage('assets/icon/telegram.png');

  /// File path: assets/icon/thank_you_paid.svg
  SvgGenImage get thankYouPaid => const SvgGenImage('assets/icon/thank_you_paid.svg');

  /// File path: assets/icon/thumbs_up.svg
  SvgGenImage get thumbsUp => const SvgGenImage('assets/icon/thumbs_up.svg');

  /// File path: assets/icon/thumbs_up_filled.png
  AssetGenImage get thumbsUpFilled => const AssetGenImage('assets/icon/thumbs_up_filled.png');

  /// File path: assets/icon/thumbs_up_outline.png
  AssetGenImage get thumbsUpOutline => const AssetGenImage('assets/icon/thumbs_up_outline.png');

  /// File path: assets/icon/tick.svg
  SvgGenImage get tick => const SvgGenImage('assets/icon/tick.svg');

  /// File path: assets/icon/tick2.svg
  SvgGenImage get tick2 => const SvgGenImage('assets/icon/tick2.svg');

  /// File path: assets/icon/tick_rounded.svg
  SvgGenImage get tickRounded => const SvgGenImage('assets/icon/tick_rounded.svg');

  /// File path: assets/icon/tick_rounded_outline.svg
  SvgGenImage get tickRoundedOutline => const SvgGenImage('assets/icon/tick_rounded_outline.svg');

  /// File path: assets/icon/ticket.svg
  SvgGenImage get ticket => const SvgGenImage('assets/icon/ticket.svg');

  /// File path: assets/icon/ticket_details_icon.svg
  SvgGenImage get ticketDetailsIcon => const SvgGenImage('assets/icon/ticket_details_icon.svg');

  /// File path: assets/icon/ticket_resolved_icon.svg
  SvgGenImage get ticketResolvedIcon => const SvgGenImage('assets/icon/ticket_resolved_icon.svg');

  /// File path: assets/icon/top_creations.png
  AssetGenImage get topCreations => const AssetGenImage('assets/icon/top_creations.png');

  /// File path: assets/icon/tracking.svg
  SvgGenImage get tracking => const SvgGenImage('assets/icon/tracking.svg');

  /// File path: assets/icon/trailer.svg
  SvgGenImage get trailer => const SvgGenImage('assets/icon/trailer.svg');

  /// File path: assets/icon/tutorial_gradient.svg
  SvgGenImage get tutorialGradient => const SvgGenImage('assets/icon/tutorial_gradient.svg');

  /// File path: assets/icon/twitter.png
  AssetGenImage get twitter => const AssetGenImage('assets/icon/twitter.png');

  /// File path: assets/icon/under_maintenance.png
  AssetGenImage get underMaintenance => const AssetGenImage('assets/icon/under_maintenance.png');

  /// File path: assets/icon/unmute.png
  AssetGenImage get unmute => const AssetGenImage('assets/icon/unmute.png');

  /// File path: assets/icon/user.png
  AssetGenImage get user => const AssetGenImage('assets/icon/user.png');

  /// File path: assets/icon/user_default_photo.svg
  SvgGenImage get userDefaultPhoto => const SvgGenImage('assets/icon/user_default_photo.svg');

  /// File path: assets/icon/user_icon_chat.svg
  SvgGenImage get userIconChat => const SvgGenImage('assets/icon/user_icon_chat.svg');

  /// File path: assets/icon/username_icon.svg
  SvgGenImage get usernameIcon => const SvgGenImage('assets/icon/username_icon.svg');

  /// File path: assets/icon/verified.svg
  SvgGenImage get verified => const SvgGenImage('assets/icon/verified.svg');

  /// File path: assets/icon/verified_check.svg
  SvgGenImage get verifiedCheck => const SvgGenImage('assets/icon/verified_check.svg');

  /// File path: assets/icon/visa_logo-expired.svg
  SvgGenImage get visaLogoExpired => const SvgGenImage('assets/icon/visa_logo-expired.svg');

  /// File path: assets/icon/visa_logo.svg
  SvgGenImage get visaLogo => const SvgGenImage('assets/icon/visa_logo.svg');

  /// File path: assets/icon/volume_loud.svg
  SvgGenImage get volumeLoud => const SvgGenImage('assets/icon/volume_loud.svg');

  /// File path: assets/icon/volume_muted.svg
  SvgGenImage get volumeMuted => const SvgGenImage('assets/icon/volume_muted.svg');

  /// File path: assets/icon/warning.svg
  SvgGenImage get warning => const SvgGenImage('assets/icon/warning.svg');

  /// List of all assets
  List<dynamic> get values => [
        aSvg,
        congratsPopup,
        add,
        arrowRightIos,
        attachmentIcon,
        back,
        cartOutlined,
        check,
        checkMark,
        chevonDown,
        chevronUp,
        correctiveActionIcon,
        couponLeftSideGradient,
        crossPng,
        crossSvg,
        cross2,
        customerSupportIcon,
        danger,
        delete,
        download,
        edit,
        emptyCart,
        examFailed,
        feedback,
        filterIcon,
        helpdeskIcon,
        helpdeskTicketIcon,
        link,
        linkedIn,
        liveNow,
        lock,
        moreDotIcon,
        noData,
        notifications,
        paid,
        pdf,
        person,
        person2,
        phone2,
        phoneGradient,
        pickup,
        placeholderImage,
        plan,
        playPng,
        playSvg,
        playCircle,
        playCircleFilled,
        playFill,
        playRounded,
        popularPick,
        postCopy,
        postPlay,
        premier,
        profile,
        profileCircle,
        profileFilled,
        profileTab,
        profileTabBold,
        raiseTicketIcon,
        recipe,
        recipeIcon,
        refresh,
        remove,
        replay,
        report,
        sadFace,
        sample,
        searchPng,
        searchSvg,
        search2,
        searchGradient,
        send,
        sendReplyIcon,
        settings,
        share,
        shop,
        shoppingBagOutlined,
        shoppingCart,
        smileFace,
        smileFace2,
        smileFaceFilled,
        smilingFaceGradient,
        speaker,
        spfLogo,
        star,
        startChatBold,
        stripeIcon,
        subscriptionBg,
        subscriptionPremiumIcon,
        support,
        switchSvg,
        tabletopLogo,
        takeawayTabIcon,
        telegram,
        thankYouPaid,
        thumbsUp,
        thumbsUpFilled,
        thumbsUpOutline,
        tick,
        tick2,
        tickRounded,
        tickRoundedOutline,
        ticket,
        ticketDetailsIcon,
        ticketResolvedIcon,
        topCreations,
        tracking,
        trailer,
        tutorialGradient,
        twitter,
        underMaintenance,
        unmute,
        user,
        userDefaultPhoto,
        userIconChat,
        usernameIcon,
        verified,
        verifiedCheck,
        visaLogoExpired,
        visaLogo,
        volumeLoud,
        volumeMuted,
        warning
      ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/app_bg.png
  AssetGenImage get appBg => const AssetGenImage('assets/images/app_bg.png');

  /// File path: assets/images/coming_soon_bg_full.png
  AssetGenImage get comingSoonBgFull => const AssetGenImage('assets/images/coming_soon_bg_full.png');

  /// File path: assets/images/coming_soon_bg_half.png
  AssetGenImage get comingSoonBgHalf => const AssetGenImage('assets/images/coming_soon_bg_half.png');

  /// File path: assets/images/coming_soon_text.png
  AssetGenImage get comingSoonText => const AssetGenImage('assets/images/coming_soon_text.png');

  /// File path: assets/images/cpe_tracker_into_image.jpg
  AssetGenImage get cpeTrackerIntoImage => const AssetGenImage('assets/images/cpe_tracker_into_image.jpg');

  /// File path: assets/images/cpe_tracker_intro_image.png
  AssetGenImage get cpeTrackerIntroImage => const AssetGenImage('assets/images/cpe_tracker_intro_image.png');

  /// File path: assets/images/deactivate_account.png
  AssetGenImage get deactivateAccount => const AssetGenImage('assets/images/deactivate_account.png');

  /// File path: assets/images/download_certificate.png
  AssetGenImage get downloadCertificate => const AssetGenImage('assets/images/download_certificate.png');

  /// File path: assets/images/exit_popup_img.png
  AssetGenImage get exitPopupImg => const AssetGenImage('assets/images/exit_popup_img.png');

  /// File path: assets/images/feedback_emoji.png
  AssetGenImage get feedbackEmoji => const AssetGenImage('assets/images/feedback_emoji.png');

  /// File path: assets/images/hero_section_image.png
  AssetGenImage get heroSectionImage => const AssetGenImage('assets/images/hero_section_image.png');

  /// File path: assets/images/linkedin.png
  AssetGenImage get linkedin => const AssetGenImage('assets/images/linkedin.png');

  /// File path: assets/images/lock.png
  AssetGenImage get lock => const AssetGenImage('assets/images/lock.png');

  /// File path: assets/images/logout.png
  AssetGenImage get logout => const AssetGenImage('assets/images/logout.png');

  /// File path: assets/images/payment_banner.png
  AssetGenImage get paymentBanner => const AssetGenImage('assets/images/payment_banner.png');

  /// File path: assets/images/payment_options.png
  AssetGenImage get paymentOptions => const AssetGenImage('assets/images/payment_options.png');

  /// File path: assets/images/plan_hero_image.png
  AssetGenImage get planHeroImage => const AssetGenImage('assets/images/plan_hero_image.png');

  /// File path: assets/images/premium.png
  AssetGenImage get premium => const AssetGenImage('assets/images/premium.png');

  /// File path: assets/images/profile_image.jpeg
  AssetGenImage get profileImage => const AssetGenImage('assets/images/profile_image.jpeg');

  /// File path: assets/images/switch_to_cpe.png
  AssetGenImage get switchToCpe => const AssetGenImage('assets/images/switch_to_cpe.png');

  /// File path: assets/images/update_profile.png
  AssetGenImage get updateProfile => const AssetGenImage('assets/images/update_profile.png');

  /// File path: assets/images/warning.svg
  SvgGenImage get warning => const SvgGenImage('assets/images/warning.svg');

  /// File path: assets/images/webinar_coming_soon_image.png
  AssetGenImage get webinarComingSoonImage => const AssetGenImage('assets/images/webinar_coming_soon_image.png');

  /// List of all assets
  List<dynamic> get values => [
        appBg,
        comingSoonBgFull,
        comingSoonBgHalf,
        comingSoonText,
        cpeTrackerIntoImage,
        cpeTrackerIntroImage,
        deactivateAccount,
        downloadCertificate,
        exitPopupImg,
        feedbackEmoji,
        heroSectionImage,
        linkedin,
        lock,
        logout,
        paymentBanner,
        paymentOptions,
        planHeroImage,
        premium,
        profileImage,
        switchToCpe,
        updateProfile,
        warning,
        webinarComingSoonImage
      ];
}

class $AssetsJsonGen {
  const $AssetsJsonGen();

  /// File path: assets/json/countries_list.json
  String get countriesList => 'assets/json/countries_list.json';

  /// List of all assets
  List<String> get values => [countriesList];
}

class $AssetsLogoGen {
  const $AssetsLogoGen();

  /// File path: assets/logo/app_logo.png
  AssetGenImage get appLogo => const AssetGenImage('assets/logo/app_logo.png');

  /// File path: assets/logo/cpe_logo.png
  AssetGenImage get cpeLogo => const AssetGenImage('assets/logo/cpe_logo.png');

  /// File path: assets/logo/launcher_icon.png
  AssetGenImage get launcherIcon => const AssetGenImage('assets/logo/launcher_icon.png');

  /// File path: assets/logo/miles_masterclass.svg
  SvgGenImage get milesMasterclass => const SvgGenImage('assets/logo/miles_masterclass.svg');

  /// File path: assets/logo/stripe_logo.png
  AssetGenImage get stripeLogo => const AssetGenImage('assets/logo/stripe_logo.png');

  /// List of all assets
  List<dynamic> get values => [appLogo, cpeLogo, launcherIcon, milesMasterclass, stripeLogo];
}

class $AssetsNavbarGen {
  const $AssetsNavbarGen();

  /// File path: assets/navbar/cpe_tracker.svg
  SvgGenImage get cpeTracker => const SvgGenImage('assets/navbar/cpe_tracker.svg');

  /// File path: assets/navbar/cpe_tracker_selected.svg
  SvgGenImage get cpeTrackerSelected => const SvgGenImage('assets/navbar/cpe_tracker_selected.svg');

  /// File path: assets/navbar/home.svg
  SvgGenImage get home => const SvgGenImage('assets/navbar/home.svg');

  /// File path: assets/navbar/home_selected.svg
  SvgGenImage get homeSelected => const SvgGenImage('assets/navbar/home_selected.svg');

  /// File path: assets/navbar/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/navbar/profile.svg');

  /// File path: assets/navbar/profile_selected.svg
  SvgGenImage get profileSelected => const SvgGenImage('assets/navbar/profile_selected.svg');

  /// List of all assets
  List<SvgGenImage> get values => [cpeTracker, cpeTrackerSelected, home, homeSelected, profile, profileSelected];
}

class $AssetsNumbersGen {
  const $AssetsNumbersGen();

  /// File path: assets/numbers/1.png
  AssetGenImage get a1 => const AssetGenImage('assets/numbers/1.png');

  /// File path: assets/numbers/10.png
  AssetGenImage get a10 => const AssetGenImage('assets/numbers/10.png');

  /// File path: assets/numbers/2.png
  AssetGenImage get a2 => const AssetGenImage('assets/numbers/2.png');

  /// File path: assets/numbers/3.png
  AssetGenImage get a3 => const AssetGenImage('assets/numbers/3.png');

  /// File path: assets/numbers/4.png
  AssetGenImage get a4 => const AssetGenImage('assets/numbers/4.png');

  /// File path: assets/numbers/5.png
  AssetGenImage get a5 => const AssetGenImage('assets/numbers/5.png');

  /// File path: assets/numbers/6.png
  AssetGenImage get a6 => const AssetGenImage('assets/numbers/6.png');

  /// File path: assets/numbers/7.png
  AssetGenImage get a7 => const AssetGenImage('assets/numbers/7.png');

  /// File path: assets/numbers/8.png
  AssetGenImage get a8 => const AssetGenImage('assets/numbers/8.png');

  /// File path: assets/numbers/9.png
  AssetGenImage get a9 => const AssetGenImage('assets/numbers/9.png');

  /// List of all assets
  List<AssetGenImage> get values => [a1, a10, a2, a3, a4, a5, a6, a7, a8, a9];
}

class $AssetsQuizGen {
  const $AssetsQuizGen();

  /// File path: assets/quiz/quiz_blur.png
  AssetGenImage get quizBlur => const AssetGenImage('assets/quiz/quiz_blur.png');

  /// File path: assets/quiz/take_quiz.png
  AssetGenImage get takeQuiz => const AssetGenImage('assets/quiz/take_quiz.png');

  /// File path: assets/quiz/think_emoji.png
  AssetGenImage get thinkEmoji => const AssetGenImage('assets/quiz/think_emoji.png');

  /// List of all assets
  List<AssetGenImage> get values => [quizBlur, takeQuiz, thinkEmoji];
}

class $AssetsSplashScreenGen {
  const $AssetsSplashScreenGen();

  /// File path: assets/splash_screen/hero_section_video.mp4
  String get heroSectionVideo => 'assets/splash_screen/hero_section_video.mp4';

  /// File path: assets/splash_screen/masterclass_into_video.mp4
  String get masterclassIntoVideo => 'assets/splash_screen/masterclass_into_video.mp4';

  /// File path: assets/splash_screen/splash_screen_animation.mp4
  String get splashScreenAnimation => 'assets/splash_screen/splash_screen_animation.mp4';

  /// List of all assets
  List<String> get values => [heroSectionVideo, masterclassIntoVideo, splashScreenAnimation];
}

class Assets {
  Assets._();

  static const $AssetsBgGen bg = $AssetsBgGen();
  static const $AssetsChatIconsGen chatIcons = $AssetsChatIconsGen();
  static const $AssetsCpeTrackerGen cpeTracker = $AssetsCpeTrackerGen();
  static const $AssetsEnvGen env = $AssetsEnvGen();
  static const $AssetsFinalExamGen finalExam = $AssetsFinalExamGen();
  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconGen icon = $AssetsIconGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsJsonGen json = $AssetsJsonGen();
  static const $AssetsLogoGen logo = $AssetsLogoGen();
  static const $AssetsNavbarGen navbar = $AssetsNavbarGen();
  static const $AssetsNumbersGen numbers = $AssetsNumbersGen();
  static const $AssetsQuizGen quiz = $AssetsQuizGen();
  static const $AssetsSplashScreenGen splashScreen = $AssetsSplashScreenGen();
  static const $MockDataGen mockData = $MockDataGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package = 'resources',
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package = 'resources',
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package = 'resources',
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(
    this._assetName, {
    this.flavors = const {},
  });

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(
      BuildContext,
      Widget,
      _lottie.LottieComposition?,
    )? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package = 'resources',
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
