{"code": 200, "data": [{"helpdesk_id": 1774, "course": "masterclass", "category_id": 23, "category": "Masterclass Category", "sub_category": "Master class sub category", "issue_raised": "Test", "issue_raised_date": "2025-05-23 10:00:53", "updated_date": "2025-05-23 10:00:53", "sub_question_id": "2553", "ticketStatus": 1, "student_reason_date": null, "is_escalate": 0, "ticket_id": "MOBILEAPP1774", "ticket_status": "Open", "is_feedback_given": 0, "feedback_rating": null, "comment": [{"id": 1795, "helpdesk_id": 1774, "created_date": "2025-05-23 10:00:55", "updated_by": null, "category_id": 22, "student_reason": "Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.", "admin_comment": null, "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "10:00 AM", "date": "23/05/2025"}, {"id": 1796, "helpdesk_id": 1774, "created_date": "2025-05-23 10:00:55", "updated_by": null, "category_id": 22, "student_reason": null, "admin_comment": "Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.", "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "10:00 AM", "date": "23/05/2025"}, {"id": 1797, "helpdesk_id": 1774, "created_date": "2025-05-23 10:00:55", "updated_by": null, "category_id": 22, "student_reason": "Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.", "admin_comment": "Yes, There was a glitch and the problem is being looked into, Thanks for your patience. e problem is being looked into, Thanks for your patience.", "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "10:00 AM", "date": "23/05/2025"}], "show_status": 0, "issue_checked": ["2553"], "hide_reopen_ticket": 0, "show_escalate": 0}, {"helpdesk_id": 1773, "course": "masterclass", "category_id": 23, "category": "Masterclass Category", "sub_category": "Master class sub category", "issue_raised": "Test", "issue_raised_date": "2025-05-23 09:55:58", "updated_date": "2025-05-23 09:55:58", "sub_question_id": "2553", "ticketStatus": 1, "student_reason_date": null, "is_escalate": 0, "ticket_id": "MOBILEAPP1773", "ticket_status": "Open", "is_feedback_given": 0, "feedback_rating": null, "comment": [{"id": 1794, "helpdesk_id": 1773, "created_date": "2025-05-23 09:56:05", "updated_by": null, "category_id": 22, "student_reason": "", "admin_comment": null, "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "9:56 AM", "date": "23/05/2025"}], "show_status": 0, "issue_checked": ["2553"], "hide_reopen_ticket": 0, "show_escalate": 0}, {"helpdesk_id": 1772, "course": "masterclass", "category_id": 23, "category": "Masterclass Category", "sub_category": "Master class sub category", "issue_raised": "Test", "issue_raised_date": "2025-05-23 09:49:05", "updated_date": "2025-05-23 09:49:05", "sub_question_id": "2553", "ticketStatus": 1, "student_reason_date": null, "is_escalate": 0, "ticket_id": "LMS1772T", "ticket_status": "Open", "is_feedback_given": 0, "feedback_rating": null, "comment": [], "show_status": 0, "issue_checked": ["2553"], "hide_reopen_ticket": 0, "show_escalate": 0}, {"helpdesk_id": 1771, "course": "masterclass", "category_id": 23, "category": "Masterclass Category", "sub_category": "Master class sub category", "issue_raised": "Test", "issue_raised_date": "2025-05-23 09:48:02", "updated_date": "2025-05-23 09:48:02", "sub_question_id": "2553", "ticketStatus": 1, "student_reason_date": null, "is_escalate": 0, "ticket_id": "MOBILEAPP1771", "ticket_status": "Open", "is_feedback_given": 0, "feedback_rating": null, "comment": [{"id": 1793, "helpdesk_id": 1771, "created_date": "2025-05-23 09:48:17", "updated_by": null, "category_id": 22, "student_reason": "", "admin_comment": null, "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "9:48 AM", "date": "23/05/2025"}], "show_status": 0, "issue_checked": ["2553"], "hide_reopen_ticket": 0, "show_escalate": 0}, {"helpdesk_id": 1770, "course": "masterclass", "category_id": 23, "category": "Masterclass Category", "sub_category": "Master class sub category", "issue_raised": "Test", "issue_raised_date": "2025-05-23 09:43:51", "updated_date": "2025-05-23 09:43:51", "sub_question_id": "2553", "ticketStatus": 1, "student_reason_date": null, "is_escalate": 0, "ticket_id": "MOBILEAPP1770", "ticket_status": "Open", "is_feedback_given": 0, "feedback_rating": null, "comment": [{"id": 1792, "helpdesk_id": 1770, "created_date": "2025-05-23 09:44:18", "updated_by": null, "category_id": 22, "student_reason": "", "admin_comment": null, "student_additional_comment": "Test", "user_id": null, "admin_id": null, "comment_attachments": null, "ticket_status": "Open", "time": "9:44 AM", "date": "23/05/2025"}], "show_status": 0, "issue_checked": ["2553"], "hide_reopen_ticket": 0, "show_escalate": 0}], "message": "success", "status": true, "metadata": [{"totalCount": 5, "ratingWiseIssueList": {"2553": "Masterclass question level"}, "getTicketCountDetails": {"all_count": 5, "open_count": 5, "closed_count": 0, "escalated_count": 0, "inprogress_count": 0}}]}